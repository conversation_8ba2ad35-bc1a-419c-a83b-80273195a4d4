worker_processes 1;

events {
    worker_connections 1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    keepalive_timeout  65;

    # Gzip压缩配置，可以加快静态文件的传输速度
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    server {
        # 监听80端口，处理HTTP请求
        listen 80;
        server_name localhost;  # 可以替换为你的域名，例如：frontend.example.com

        # 指定静态文件的根目录
        root /usr/share/nginx/html;

        # 默认访问的文件
        index index.html;

        # 处理所有的前端路由，如 React 或 Vue 的 SPA
        location /bd-manage {
            try_files $uri $uri/ /index.html;  # 所有请求都会重定向到index.html，以支持前端路由
        }

        # 配置对静态资源的缓存控制
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 7d;
            add_header Cache-Control "public, max-age=604800, immutable";
        }

        location /bd-manage/bd-manage-api-dev {
           proxy_pass http://**************:9080/bd-manage-api-dev;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /bd-manage/bd-client {
           proxy_pass http://***************:8261/bd-client;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 处理错误页面
        error_page 404 /index.html;

    }
}
