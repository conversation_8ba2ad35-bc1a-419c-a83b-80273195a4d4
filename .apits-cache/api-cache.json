{"bd-manage": {"post:/bd-manage-api/docBusiness/list": {"hash": "ac2e1d48bb0519d8fee1414d01b576b5", "lastUpdated": 1753497701393}, "post:/bd-manage-api/fileManage/deleteFileByIds": {"hash": "024aedaef1aa13cecf617c4a3c577840", "lastUpdated": 1753497701417}, "get:/bd-manage-api/fileManage/queryFileByPatientId": {"hash": "c10c2d0ae136065374ea404b62afb05e", "lastUpdated": 1753497701443}, "post:/bd-manage-api/fileManage/uploadByPatientId": {"hash": "2f80f8434b936f88d541684a8e05202a", "lastUpdated": 1753497701462}, "post:/bd-manage-api/followup/getBdFollowCount": {"hash": "85ab5340f00b82215df38e08d58a28d6", "lastUpdated": 1753497701491}, "post:/bd-manage-api/followup/getBdFollowList": {"hash": "d8c887f23dc17733c15190ee314a9d8a", "lastUpdated": 1753497701524}, "post:/bd-manage-api/followup/getFollowInfoById": {"hash": "39718a3d44efa17a13b9d8cb48a9597e", "lastUpdated": 1753497701545}, "post:/bd-manage-api/followup/getPatientFollowupHis": {"hash": "193b491702b86f2e6a624c062f4a6153", "lastUpdated": 1753497701567}, "post:/bd-manage-api/followup/getPatientFollowupNow": {"hash": "dbcc6386761106bef537ed750a939904", "lastUpdated": 1753497701623}, "post:/bd-manage-api/followup/getPatientPackage": {"hash": "ad1e40a0b1b64cb45fae1d856c3de860", "lastUpdated": 1753497701660}, "post:/bd-manage-api/followup/saveFollowBySign": {"hash": "8fc3746438a9048e64155bba646c876f", "lastUpdated": 1753497701696}, "post:/bd-manage-api/followup/saveFollowResult": {"hash": "0c4394c67badd315760bff9872a930f1", "lastUpdated": 1753497701726}, "post:/bd-manage-api/patientDoc/add": {"hash": "8f16b042e384123b04af66e9f1178ef8", "lastUpdated": 1753497701776}, "post:/bd-manage-api/patientDoc/pushById": {"hash": "734801170f19585287c7ad1bad08ab11", "lastUpdated": 1753497701839}, "get:/bd-manage-api/patientDoc/queryById": {"hash": "d58c82354ebbd1e61268d9d425d63329", "lastUpdated": 1753497701901}, "post:/bd-manage-api/patientDoc/queryByParams": {"hash": "39ecf887e7e6eb5080d54e021d8c8e66", "lastUpdated": 1753497701950}, "post:/bd-manage-api/patientDoc/updateById": {"hash": "0cd84e92fb0f1eab1986a001eb63287a", "lastUpdated": 1753497701987}, "post:/bd-manage-api/servicePersonnelManagement/addAgentUser": {"hash": "2cd2cd8bad582b8180d1ae5d5c18e505", "lastUpdated": 1753497702008}, "post:/bd-manage-api/servicePersonnelManagement/addInternetDoctor": {"hash": "a816716037d04e2034b30434ff8aac30", "lastUpdated": 1753497702036}, "post:/bd-manage-api/servicePersonnelManagement/editManagementUserAdmin": {"hash": "630d9f37e7a5aa4fb772ebc810f65d65", "lastUpdated": 1753497702087}, "get:/bd-manage-api/servicePersonnelManagement/queryManagementUserAdminDetail": {"hash": "e2bbdb3bb34209d7c2cab1464245f30b", "lastUpdated": 1753497702122}, "post:/bd-manage-api/servicePersonnelManagement/queryManagementUserAdminList": {"hash": "bc642d2a1ee710fe9c80e7a8b1c50ada", "lastUpdated": 1753497702153}, "post:/bd-manage-api/servicePersonnelManagement/queryManagementUserAdminPage": {"hash": "78a4d757da961ee8f3dd5a8245ae0b95", "lastUpdated": 1753497702214}, "get:/bd-manage-api/servicePersonnelManagement/syncCRMUser": {"hash": "0b8ec24db5bbc3e3727e78e24c964ba5", "lastUpdated": 1753497702237}, "post:/bd-manage-api/channel/list": {"hash": "304708e41dc16753fc8943a762278920", "lastUpdated": 1753497702269}, "post:/bd-manage-api/diagnosis/getDocDiagnosis": {"hash": "19569e61afda1893307d761c3b41fcfa", "lastUpdated": 1753497702283}, "post:/bd-manage-api/doc/checkHasEnable": {"hash": "3f2d289c99b915c6502dcf6cbc8fc190", "lastUpdated": 1753497702311}, "post:/bd-manage-api/doc/createOrUpdateTemplate": {"hash": "02b356a782503640d3a564f004ef8807", "lastUpdated": 1753497702329}, "post:/bd-manage-api/doc/getDocTypeBusiness": {"hash": "928cf14d9f614461d918f5e59a176d14", "lastUpdated": 1753497702358}, "post:/bd-manage-api/doc/getPatientInfo": {"hash": "73e0e349bbecfa6d94d412e2cdc2f933", "lastUpdated": 1753497702379}, "post:/bd-manage-api/doc/queryDocTemplateList": {"hash": "72cc054214a59372516dfe7f970933f3", "lastUpdated": 1753497702398}, "post:/bd-manage-api/drug/getDocDrug": {"hash": "28a3dd94b30dc2cd98c31eece4689db3", "lastUpdated": 1753497702437}, "post:/bd-manage-api/examination/getDocExamination": {"hash": "54518f21e20c57a44da0fcfc7092e900", "lastUpdated": 1753497702456}, "post:/bd-manage-api/flow/push": {"hash": "92ab9b1ae9b3021f98499855011772d0", "lastUpdated": 1753497702491}, "post:/bd-manage-api/healthInsurance/applyOrder": {"hash": "7ed25807cabefbaceffddb84e1b8a06f", "lastUpdated": 1753497702551}, "post:/bd-manage-api/healthInsurance/cancelById": {"hash": "778c900c59f85ceaef2896e979397739", "lastUpdated": 1753497702567}, "post:/bd-manage-api/healthInsurance/getBaseCodesForApp": {"hash": "22ff6953c9f26dc38e409dd8264fd65a", "lastUpdated": 1753497702581}, "post:/bd-manage-api/healthInsurance/queryById": {"hash": "020a7869aa46e214620e19514f0b3a2f", "lastUpdated": 1753497702604}, "post:/bd-manage-api/healthInsurance/queryByParams": {"hash": "098f10f2c07396f15951ce92889ebef4", "lastUpdated": 1753497702632}, "post:/bd-manage-api/healthInsurance/queryByPatientId": {"hash": "541a6d60462db683201b91db95abb351", "lastUpdated": 1753497702652}, "post:/bd-manage-api/healthInsurance/queryDetail": {"hash": "26b519f979505b4fd21ffc2f7bb56f9b", "lastUpdated": 1753497702678}, "post:/bd-manage-api/healthInsurance/queryHospitalInfoForOuter": {"hash": "9aadf547a7b760f4e367a9e63faf5100", "lastUpdated": 1753497702705}, "post:/bd-manage-api/healthInsurance/queryLayerDepartmentInfoList": {"hash": "51fbea87ef8c8dbcdcb4d9823aa75e92", "lastUpdated": 1753497702725}, "post:/bd-manage-api/healthInsurance/queryRights": {"hash": "629b45fe48bfd485871673c682a1c69c", "lastUpdated": 1753497702741}, "post:/bd-manage-api/healthInsurance/syncOrderStatus": {"hash": "363e653a4d74e15b8fa5e9a092cedb18", "lastUpdated": 1753497702754}, "post:/bd-manage-api/healthInsurance/uploadFile": {"hash": "277164f50de9f17b9b2b04c430783417", "lastUpdated": 1753497702771}, "get:/bd-manage-api/healthRecords/queryHealthRecordsPackageList": {"hash": "1d23ea1ef4c7d66960f0de4b641b1739", "lastUpdated": 1753497702812}, "post:/bd-manage-api/ih/abnormalCount": {"hash": "ff95f3db9b7d743fd2f35ce4ccb35bee", "lastUpdated": 1753497702828}, "post:/bd-manage-api/ih/abnormalList": {"hash": "5ac325fd4f85c5667f852cb7c4068b45", "lastUpdated": 1753497702851}, "post:/bd-manage-api/ih/changeAbnormalStatus": {"hash": "a18d0e4b3c5f886514280531b931a90f", "lastUpdated": 1753497702866}, "post:/bd-manage-api/internetDoc/add": {"hash": "b1bbabf9a9a6ea4e83eb37bb07f2fafe", "lastUpdated": 1753497702906}, "post:/bd-manage-api/organ/add": {"hash": "c6f8cf9b11fd36d5932e9041c78110ca", "lastUpdated": 1753497702969}, "post:/bd-manage-api/organ/detail": {"hash": "281308bcc3a43812504bc3605ab844b8", "lastUpdated": 1753497702986}, "post:/bd-manage-api/organ/page": {"hash": "940c7c07eda4d074c00204b45fdb4d82", "lastUpdated": 1753497703015}, "post:/bd-manage-api/organ/queryOrganList": {"hash": "0ee3bad028e36a5cf835ceda81323dcf", "lastUpdated": 1753497703032}, "post:/bd-manage-api/organ/start": {"hash": "8e871382c794aba59f2ee202a5b6a139", "lastUpdated": 1753497703049}, "post:/bd-manage-api/organ/update": {"hash": "087894872f4ceaff0adaea52aaf137e0", "lastUpdated": 1753497703062}, "post:/bd-manage-api/call/getPatCallList": {"hash": "6869f605509abdacd13c385bb210b676", "lastUpdated": 1753497703079}, "post:/bd-manage-api/call/patCallCount": {"hash": "80f99fadb071d42916728cef19a0355c", "lastUpdated": 1753497703097}, "post:/bd-manage-api/call/patCallHandle": {"hash": "1a30ce8694b52c4f991cd17a69b76433", "lastUpdated": 1753497703114}, "post:/bd-manage-api/patient/queryMainAndSharePatient": {"hash": "9a2e7790c5141864e33e173521808c91", "lastUpdated": 1753497703147}, "post:/bd-manage-api/patient/queryPatientByParams": {"hash": "0a37894f9f41fa59146fbefc2c0df555", "lastUpdated": 1753497703184}, "post:/bd-manage-api/patient/queryPatientByParamsPage": {"hash": "c796f0f72d53fee3304bf041df12d550", "lastUpdated": 1753497703206}, "post:/bd-manage-api/patient/queryPatientGroup": {"hash": "c337fa7fcbd86c7d8306f056fef82f1f", "lastUpdated": 1753497703221}, "post:/bd-manage-api/patient/querySelectedPatient": {"hash": "ca018d43dda483835e4de6cb1e95cb9d", "lastUpdated": 1753497703241}, "post:/bd-manage-api/department/add": {"hash": "498af9da01656a9de83999f9a0c40c43", "lastUpdated": 1753497703259}, "post:/bd-manage-api/department/page": {"hash": "1b44bc1e7b45c612a8cfde1ac7756391", "lastUpdated": 1753497703284}, "post:/bd-manage-api/department/update": {"hash": "f670c6e5c5ad6d1666ca1190aa57bd7e", "lastUpdated": 1753497703298}, "post:/bd-manage-api/tag/add": {"hash": "1dd6fbe10a6ddd22d8d4472b074f5859", "lastUpdated": 1753497703329}, "post:/bd-manage-api/tag/page": {"hash": "a31580b7f088dc905ff16d3245a31d23", "lastUpdated": 1753497703353}, "post:/bd-manage-api/tag/update": {"hash": "8f3ad4ff73ce94c43145b2a648d478de", "lastUpdated": 1753497703367}, "post:/bd-manage-api/professionalTitle/addProfessionalTitle": {"hash": "11a7ad5a451199c07b5102bc8db99139", "lastUpdated": 1753497703384}, "post:/bd-manage-api/professionalTitle/queryProfessionalTitleByName": {"hash": "5f83418e52c4e4b93ecd4fec5c68a081", "lastUpdated": 1753497703403}, "post:/bd-manage-api/rightsItem/add": {"hash": "cd9ac137490c45fee8c69fce33c204e9", "lastUpdated": 1753497703425}, "post:/bd-manage-api/package/add": {"hash": "8e81cb6b4f923468d2fd9337450d874d", "lastUpdated": 1753497703442}, "get:/bd-manage-api/package/list": {"hash": "0634321a69f3fa60ce4e03af5c468ce5", "lastUpdated": 1753497703457}, "post:/bd-manage-api/package/listByPatientId": {"hash": "2ba98ebca9a5ab979f695fd3ba2e5a66", "lastUpdated": 1753497703477}, "get:/bd-manage-api/package/queryByPackageId": {"hash": "e1b38a765d4e698e0b8d0a2220ce3954", "lastUpdated": 1753497703512}, "post:/bd-manage-api/package/queryByParams": {"hash": "357ac1da552986a427fd92a86793cc25", "lastUpdated": 1753497703538}, "post:/bd-manage-api/package/syncServicePackage": {"hash": "e693db6d59f1dac3366edbc3920cc39d", "lastUpdated": 1753497703553}, "post:/bd-manage-api/package/updateByPackageId": {"hash": "f01dbea7425eb6cf613d0c92532827d6", "lastUpdated": 1753497703565}, "post:/bd-manage-api/serviceReservation/addServiceReservation": {"hash": "fa0ce553992e00b7c51ec6ed8828911b", "lastUpdated": 1753497703593}, "post:/bd-manage-api/serviceReservation/cancelServiceReservation": {"hash": "72950599ba592149721c8caf455d3677", "lastUpdated": 1753497703611}, "post:/bd-manage-api/serviceReservation/createRecord": {"hash": "016238972ae31bffefc7af70d4e63d8e", "lastUpdated": 1753497703623}, "get:/bd-manage-api/serviceReservation/getDoctorIdByPhone": {"hash": "4b59e1574a27c34601014fdf46fd51ef", "lastUpdated": 1753497703634}, "post:/bd-manage-api/serviceReservation/getInternetDoctorList": {"hash": "7e1130a319931fa1456555797d5d704a", "lastUpdated": 1753497703656}, "post:/bd-manage-api/serviceReservation/getNoPayOrder": {"hash": "f608fc4e36a68aa03428b7134512ce9c", "lastUpdated": 1753497703685}, "post:/bd-manage-api/serviceReservation/getOrderStatus": {"hash": "1485a509f68da65e279e6c9d6fb8eb5b", "lastUpdated": 1753497703704}, "get:/bd-manage-api/serviceReservation/getPatientDoctorId": {"hash": "1eab815a9d9f829e94baf8e461e503d4", "lastUpdated": 1753497703718}, "get:/bd-manage-api/serviceReservation/getServiceItem": {"hash": "390b6c020e5942357cd0ae5d2ff00e44", "lastUpdated": 1753497703736}, "get:/bd-manage-api/serviceReservation/getServicePackage": {"hash": "5feb8c3416dd193d7036d8c6f42a087a", "lastUpdated": 1753497703752}, "post:/bd-manage-api/serviceReservation/getServiceReservationById": {"hash": "53fc1fcb3f02aae235c7f0044469a49c", "lastUpdated": 1753497703785}, "post:/bd-manage-api/serviceReservation/getTeamDoctor": {"hash": "8183c142b1d1f35f82bd12036306e140", "lastUpdated": 1753497703803}, "post:/bd-manage-api/serviceReservation/getVisitRecord": {"hash": "6189b2d672c317475e09d9aec57eb005", "lastUpdated": 1753497703819}, "post:/bd-manage-api/serviceReservation/inspect": {"hash": "4f8ac9c3346687b1e88c26f6937cf845", "lastUpdated": 1753497703834}, "post:/bd-manage-api/serviceReservation/occupy": {"hash": "6f9198c4d994d14bdeda3b4ab6986471", "lastUpdated": 1753497703851}, "post:/bd-manage-api/serviceReservation/queryAvailableServiceReservations": {"hash": "17d08eedd803574502c3726a75937d3d", "lastUpdated": 1753497703873}, "post:/bd-manage-api/serviceReservation/queryServiceItems": {"hash": "3c48e53d8199bd833f8bf1b41a143e13", "lastUpdated": 1753497703884}, "post:/bd-manage-api/serviceReservation/queryServiceReservationList": {"hash": "95231b45466f61e9e349642f07044bfa", "lastUpdated": 1753497703925}, "post:/bd-manage-api/serviceReservation/receiveServiceReservationStatusPush": {"hash": "5cbe6859961823391a7bcf5bfacae723", "lastUpdated": 1753497703939}, "post:/bd-manage-api/serviceReservation/testGetServiceInfo": {"hash": "aff710cafb75870261ed5678a62e3845", "lastUpdated": 1753497703959}, "post:/bd-manage-api/sign/add": {"hash": "c9b915112bdd2dca1eceefa9105de6f9", "lastUpdated": 1753497703974}, "post:/bd-manage-api/sign/getSignedInfoByIdCard": {"hash": "195eff96a08914353da8d45c5a5cd0f2", "lastUpdated": 1753497704000}, "post:/bd-manage-api/sign/getSignedRenewalCount": {"hash": "66a338f0e9b0dd3c53c0302f47f98daa", "lastUpdated": 1753497704024}, "post:/bd-manage-api/sign/getSignedRenewalList": {"hash": "6aed5748d5d64e9dbceb3d94c013723a", "lastUpdated": 1753497704046}, "post:/bd-manage-api/sign/getSignedTeamInfoByPatientId": {"hash": "7217478e11f5edbf00e790ee8c37c6aa", "lastUpdated": 1753497704088}, "get:/bd-manage-api/sign/test": {"hash": "71b19e67cf346d609dceb54a66016c19", "lastUpdated": 1753497704174}, "post:/bd-manage-api/sign/update": {"hash": "05e2f23266411225007115045b80bcfe", "lastUpdated": 1753497704192}, "post:/bd-manage-api/smsRecord/page": {"hash": "6ea23d8be092d34788d18102b70e0dfb", "lastUpdated": 1753497704237}, "post:/bd-manage-api/smsRecord/sendSms": {"hash": "2d1734ea5722037775e8e20733d30493", "lastUpdated": 1753497704253}, "post:/bd-manage-api/smsRecord/sendSmsBatch": {"hash": "f387cc0d87f5021d0cbaa4fbfa4e3bcf", "lastUpdated": 1753497704269}, "post:/bd-manage-api/smsTemplate/list": {"hash": "7e03fb404077eaf3a7a7110bda246325", "lastUpdated": 1753497704282}, "post:/bd-manage-api/useful/add": {"hash": "9ce8738a389348eb63f7bf0a885d6576", "lastUpdated": 1753497704295}, "get:/bd-manage-api/useful/delete": {"hash": "de33cc7e80240cc9dbcd885a81877604", "lastUpdated": 1753497704372}, "post:/bd-manage-api/useful/list": {"hash": "dac9c6b796baf8d8c1645f1befa11db1", "lastUpdated": 1753497704399}, "put:/bd-manage-api/system/dict/data": {"hash": "0a712a14f0125f719103de748fe38361", "lastUpdated": 1753497704481}, "post:/bd-manage-api/system/dict/data": {"hash": "29c4977d58df8d292908c36693fbfebc", "lastUpdated": 1753497704498}, "get:/bd-manage-api/system/dict/data/list": {"hash": "293824da3606b4ee11e2fb43338f7fec", "lastUpdated": 1753497704517}, "get:/bd-manage-api/system/dict/data/type/{dictType}": {"hash": "5914c7de526d2280968b50b0c4c2f347", "lastUpdated": 1753497704537}, "delete:/bd-manage-api/system/dict/data/{dictCodes}": {"hash": "7f47e6b567ca15e721836e2354662458", "lastUpdated": 1753497704552}, "get:/bd-manage-api/system/dict/data/{dictCode}": {"hash": "3e2ba37167cf8b05f40d4cea61cd384c", "lastUpdated": 1753497704566}, "put:/bd-manage-api/system/dict/type": {"hash": "7aad14ade67e58537894d8c7918438a3", "lastUpdated": 1753497704579}, "post:/bd-manage-api/system/dict/type": {"hash": "fc7aa5e8ac132bd654d2cca8876b3ec0", "lastUpdated": 1753497704598}, "get:/bd-manage-api/system/dict/type/list": {"hash": "0215bc9dfbc68622909dd6d8c5b89df5", "lastUpdated": 1753497704620}, "get:/bd-manage-api/system/dict/type/optionselect": {"hash": "9f0dbd312e9c308daaf004217c592a2d", "lastUpdated": 1753497704635}, "delete:/bd-manage-api/system/dict/type/refreshCache": {"hash": "0b05caac324e6c9fc37cd74b62d2dd51", "lastUpdated": 1753497704646}, "delete:/bd-manage-api/system/dict/type/{dictIds}": {"hash": "27bb2cbfeff612429e7343abfdd15c54", "lastUpdated": 1753497704663}, "get:/bd-manage-api/system/dict/type/{dictId}": {"hash": "7fa38c5fd446a53b6bfd25adc4ff1b1b", "lastUpdated": 1753497704678}, "post:/bd-manage-api/teamEquityPackage/editTeamEquityPackage": {"hash": "7d77680589d0db1707ea5c6c5a18e0c0", "lastUpdated": 1753497704712}, "get:/bd-manage-api/teamEquityPackage/queryTeamEquityPackageById": {"hash": "b447a8adf6a0647603083025733e18ee", "lastUpdated": 1753497704746}, "post:/bd-manage-api/teamEquityPackage/queryTeamEquityPackagePage": {"hash": "b4efc1e94691ae77486e275364413fdd", "lastUpdated": 1753497704773}, "get:/bd-manage-api/teamEquityPackage/syncTeamEquityPackageInfo": {"hash": "9a6dd2addee807e07cd17194a3bbaafe", "lastUpdated": 1753497704782}, "post:/bd-manage-api/ticketAssignee/finish/{id}": {"hash": "065d270815c1d6252d13d9b963416f8d", "lastUpdated": 1753497704799}, "post:/bd-manage-api/ticketAssignee/my": {"hash": "575453bae5f003f0bef65b5d2fbc324b", "lastUpdated": 1753497704890}, "post:/bd-manage-api/ticketAssignee/status/{id}": {"hash": "85e7a6a069a0e466fe25011e5a470f41", "lastUpdated": 1753497704964}, "post:/bd-manage-api/ticketAssignee/transfer/{id}": {"hash": "c8e83168114e7ffee7a3e40c3c5eb6d9", "lastUpdated": 1753497705007}, "post:/bd-manage-api/ticket/createDraft": {"hash": "feb20b37756b854378d25cde0c1af85b", "lastUpdated": 1753497705083}, "get:/bd-manage-api/ticket/drafts": {"hash": "63834da7ac5e4a4fd9c3cda3189cadcf", "lastUpdated": 1753497705172}, "post:/bd-manage-api/ticket/list": {"hash": "afd3677b16e77564dca4b42c2620e6b3", "lastUpdated": 1753497705240}, "post:/bd-manage-api/ticket/my": {"hash": "ebb5913dac1b3f0b190adc3e7810b01f", "lastUpdated": 1753497705349}, "post:/bd-manage-api/ticket/status/{id}": {"hash": "995b702e707e7fdb830c7ed1400d2b18", "lastUpdated": 1753497705372}, "post:/bd-manage-api/ticket/submit/{id}": {"hash": "6019c62b67320b294aad941c9673a647", "lastUpdated": 1753150273129}, "get:/bd-manage-api/ticket/{id}": {"hash": "768f047ceef00a63fff97760b5539826", "lastUpdated": 1753497705409}, "put:/bd-manage-api/ticket/{id}": {"hash": "0d738d2c2673c21eef1ddf862fbcc780", "lastUpdated": 1753497705430}, "delete:/bd-manage-api/ticket/{id}": {"hash": "5d8f832dad571e17b652f6ccc1d5e384", "lastUpdated": 1753497705459}, "post:/bd-manage-api/ticketLog/my": {"hash": "e6eeb2686470fb0bd2fca99cb060e617", "lastUpdated": 1753497705478}, "post:/bd-manage-api/todoCarport/add": {"hash": "acc947d02190ad26f2aaa00167413700", "lastUpdated": 1753497705497}, "get:/bd-manage-api/todoCarport/cancel": {"hash": "0e173b76bcb0c061e40a9102186fd3eb", "lastUpdated": 1753497705516}, "post:/bd-manage-api/todoCarport/update": {"hash": "e4e087cd2a4f629987314e533e3cd7cb", "lastUpdated": 1753497705540}, "post:/bd-manage-api/todo/add": {"hash": "7d4a7fe718c106111ed77fe0d50a1d2a", "lastUpdated": 1753497705557}, "post:/bd-manage-api/todo/create": {"hash": "873aea11008af886381b74ff8c94d0b4", "lastUpdated": 1753497705576}, "get:/bd-manage-api/todo/finish": {"hash": "99758e7ba953045a3cdf4f7be74ed179", "lastUpdated": 1753497705604}, "post:/bd-manage-api/todo/page": {"hash": "38f9cf5a080dd67127cf7155749342ab", "lastUpdated": 1753497705661}, "post:/bd-manage-api/todo/statistics": {"hash": "50d6dc1e5a8674b3b5b44f5ba5d5ed4b", "lastUpdated": 1753497705679}, "post:/bd-manage-api/todo/todoStatistics": {"hash": "94da973df4388ce566fa50f058f4b386", "lastUpdated": 1753497705703}, "post:/bd-manage-api/todo/update": {"hash": "862d42f967d688e45b8ba6e25131889f", "lastUpdated": 1753497705738}, "post:/bd-manage-api/todoOffline/add": {"hash": "d927074757f0e73d1e8c0acf7edde2e9", "lastUpdated": 1753497705771}, "post:/bd-manage-api/todoOffline/update": {"hash": "008861d4129793a5f0c3772f7285cd2b", "lastUpdated": 1753497705787}, "post:/bd-manage-api/user/login/free": {"hash": "51128bc5afa4853367ce42525301e6a1", "lastUpdated": 1753497705823}, "post:/bd-manage-api/user/loginByPhone": {"hash": "3eb921629819c1c3c400c505f3aa682e", "lastUpdated": 1753497705890}, "post:/bd-manage-api/user/loginByTeamIdRoleIdUserId": {"hash": "a917e48a70aa49d2e82a67ae959c08a6", "lastUpdated": 1753497705969}, "get:/bd-manage-api/user/queryRoleByUserId": {"hash": "b38a9e473f77d861f8cce48530c1455b", "lastUpdated": 1753497705986}, "get:/bd-manage-api/user/queryTeamByRoleType": {"hash": "efa0d67f66647d9cbdb51498c8f48234", "lastUpdated": 1753497706007}, "get:/bd-manage-api/user/queryTeamByUserId": {"hash": "25516ad6a29d2427f29b614ef7f95d10", "lastUpdated": 1753497706022}, "get:/bd-manage-api/user/teamList": {"hash": "c4569957768d4002db99201ad13d1bed", "lastUpdated": 1753497706046}, "put:/bd-manage-api/user/teamList": {"hash": "09f1d4d72e43831d717c61281f566986", "lastUpdated": 1753497706069}, "post:/bd-manage-api/user/teamList": {"hash": "2f8e129f075972570caf728c728b44d0", "lastUpdated": 1753497706094}, "delete:/bd-manage-api/user/teamList": {"hash": "10c18867861119d7005e9fb6bac2a649", "lastUpdated": 1753497706140}, "options:/bd-manage-api/user/teamList": {"hash": "aefa8dc11a3be4a154fff3dedb844151", "lastUpdated": 1753497706166}, "head:/bd-manage-api/user/teamList": {"hash": "293de498d654a957044c12adc6891ef2", "lastUpdated": 1753497706184}, "patch:/bd-manage-api/user/teamList": {"hash": "ed0bc6b00ca0c3fc20c94bbb7f118175", "lastUpdated": 1753497706206}, "get:/bd-manage-api/user/userList": {"hash": "30f3c7b0c9ce0c2e1f322d3c2bd910d3", "lastUpdated": 1753497706234}, "put:/bd-manage-api/user/userList": {"hash": "fad17c3062e858ac9691183c00068c3e", "lastUpdated": 1753497706261}, "post:/bd-manage-api/user/userList": {"hash": "c1ba4b266101cc69db89d7bf9b6114f3", "lastUpdated": 1753497706309}, "delete:/bd-manage-api/user/userList": {"hash": "4559ce99a9cefb91bb380666d4ea0876", "lastUpdated": 1753497706339}, "options:/bd-manage-api/user/userList": {"hash": "0ee9e4443bbac96ad36ea82d87eb56ef", "lastUpdated": 1753497706360}, "head:/bd-manage-api/user/userList": {"hash": "defaccc7521aa22cbd76491b741d86bc", "lastUpdated": 1753497706380}, "patch:/bd-manage-api/user/userList": {"hash": "58288234bc157623ff76ec9e7dfb1062", "lastUpdated": 1753497706410}, "post:/bd-manage-api/userRole/addUserRole": {"hash": "ba771a0de5927fd7d47a23b70f103a46", "lastUpdated": 1753497706423}, "post:/bd-manage-api/userRole/queryUserRoleList": {"hash": "d734d7890de18809518f7524e5ac564f", "lastUpdated": 1753497706447}, "post:/bd-manage-api/userRole/queryUserRolePage": {"hash": "47e4fb1d672dc06f90fefc8b576f09f9", "lastUpdated": 1753497706478}, "post:/bd-manage-api/userRoleUser/selectUserRoleUserPage": {"hash": "1cff06060a4f0fe97cbae4c1b95bf0d2", "lastUpdated": 1753497706505}, "post:/bd-manage-api/userRoleUser/updateUserRole": {"hash": "e0608e6c60224ade0ca1816f83476a20", "lastUpdated": 1753497706541}, "get:/bd-manage-api/userTeam/queryUserTeamDetail": {"hash": "84acfd1bdc667dadaf66c567dcf2fad8", "lastUpdated": 1753497706562}, "post:/bd-manage-api/userTeam/queryUserTeamPage": {"hash": "11572faf4d0c7801b5ceb2c28314a2c0", "lastUpdated": 1753497706581}, "index:GongDan": {"hash": "77859f9626535c6c6c3ef755396c0568", "lastUpdated": 1753342121046}, "post:/bd-manage-api/file/upload": {"hash": "64a3cc00eccb2961bd1e89b489ea940f", "lastUpdated": 1753497702476}, "post:/bd-manage-api/patientLabel/groupList": {"hash": "d7dab1a2037742f20a87da4eb5b1a150", "lastUpdated": 1753497702926}, "post:/bd-manage-api/patientLabel/userDefinedLabe": {"hash": "f3532766d5ae1b73488eb6cae156761a", "lastUpdated": 1753497702941}, "post:/bd-manage-api/patientLabel/userDefinedLabe/add}": {"hash": "4a5419fcadfdb4c3d2adf51b1a8d8569", "lastUpdated": 1753497702958}, "post:/bd-manage-api/patient/home/<USER>/{patientId}": {"hash": "df273b933c2b8a31023f9fb425d618ea", "lastUpdated": 1753497703130}, "index:BiaoQian": {"hash": "3a480336180bdff63483b1293662c6b1", "lastUpdated": 1753342121039}, "index:HuanZheXinXiXiangGuanAPI": {"hash": "fe15b2d47116b8e2dcb94d0cb4ab960d", "lastUpdated": 1753342121042}, "main:index": {"hash": "2c47d000e0e518fec3d4befd60d4f096", "lastUpdated": 1753411476096}, "index:TuanDuiGuanLi": {"hash": "eb81f600016c76f8aeaf5ba383a09ad9", "lastUpdated": 1753411476095}, "post:/bd-manage-api/diagnose/list": {"hash": "c506a62d827122d76d952e714f13500a", "lastUpdated": 1753497703315}, "get:/bd-manage-api/userTeam/userList": {"hash": "cd7b81c658d771b2d242e9ec5f191aed", "lastUpdated": 1753497706598}, "index:FuWuYuYueXiangGuanAPI": {"hash": "28a45e2acee4aad9cd18eccf6fe3f29f", "lastUpdated": 1753427626596}, "post:/bd-manage-api/doc/queryDocTemplatePage": {"hash": "e76e0d8d7f819e399d0e180ec28eab1e", "lastUpdated": 1753497702417}, "get:/bd-manage-api/serviceReservation/getInternetOrderDetail": {"hash": "b14cde9c20ef1e8b01961200fd9d211c", "lastUpdated": 1753497703670}, "index:WenShuMoBan": {"hash": "5cfc2c96c6f68fe4c3dc0cc8108ab4fe", "lastUpdated": 1753492921475}, "index:DuanXinFaSongJiLu": {"hash": "b253e274bea410fd64ca91fbefa9688e", "lastUpdated": 1753492921477}}, "bd-client": {"post:/bd-client/healthInsurance/queryById": {"hash": "906b05f5ff041100cb755810585127c3", "lastUpdated": 1753497707067}, "post:/bd-client/healthInsurance/queryByPatientId": {"hash": "2f9df91b4654e5eca749d6952a793930", "lastUpdated": 1753497707086}, "post:/bd-client/patient/createPatShare": {"hash": "11aed41ef23c2c439d2f54748f36bd2a", "lastUpdated": 1753497708054}, "post:/bd-client/patient/getPatShare": {"hash": "4aadb69fb0964d55a784036b72b9399f", "lastUpdated": 1753497708071}, "post:/bd-client/patient/getPatShareList": {"hash": "15487356f97a9d7dfcd0f35189f81521", "lastUpdated": 1753497708086}, "post:/bd-client/patient/getPatSignInfo": {"hash": "7db1b909c83be3e4b4ef01d61e155626", "lastUpdated": 1753497708102}, "post:/bd-client/pingAnServicePackage/queryServicePackage": {"hash": "5acde45f7dd0e3ff1058c2980c576ff3", "lastUpdated": 1753497708306}, "post:/bd-client/service/queryUserService": {"hash": "077367b6600d423dc074c4b5488db50b", "lastUpdated": 1753497708730}, "post:/bd-client/service/queryUserServiceFromCRM": {"hash": "dda71d1835968808d60aadeb332bb505", "lastUpdated": 1753497708776}, "post:/bd-client/serviceOperation/addServicePackage": {"hash": "09b3a6e35e54c9351c38c1e68bb32c8b", "lastUpdated": 1753497708801}, "post:/bd-client/serviceOperation/deleteServicePackage": {"hash": "b0d414719cd59304ea08552cd4fe7276", "lastUpdated": 1753497708816}, "post:/bd-client/serviceOperation/getServicePackageById": {"hash": "7721ff7553ee24f9c45cc5bd9f885f9f", "lastUpdated": 1753497708852}, "post:/bd-client/serviceOperation/queryServicePackage": {"hash": "ea60afd1ca660a2dc4048a87629c6abd", "lastUpdated": 1753497708944}, "post:/bd-client/serviceOperation/updateServicePackageStatus": {"hash": "0c57f0e3a9dbeafe1cbd059085a5f103", "lastUpdated": 1753497708984}, "index:FuWuBaoXiangGuanAPI": {"hash": "fb11930a23bf1311e679b954b8c1aec6", "lastUpdated": 1753149591359}, "post:/bd-client/service/queryUserServiceByUserIdAndChannel": {"hash": "d7b2506055fa40e82435592a96ab2023", "lastUpdated": 1753497708748}, "main:index": {"hash": "f5f9baa7d227afffd023f45585723103", "lastUpdated": 1753492923130}, "post:/bd-client/auth/health/insurance": {"hash": "8883e1eb07f4624a65c8ceae76dc8597", "lastUpdated": 1753497706940}, "post:/bd-client/auth/life/insurance": {"hash": "b78f315610ed577fc34c7fa8b9980d9a", "lastUpdated": 1753497706970}, "get:/bd-client/patientDoc/downCount": {"hash": "df82f88d8ec50e236cd16bcc10ec2fb5", "lastUpdated": 1753497706986}, "get:/bd-client/patientDoc/getCountByPatientId": {"hash": "5e1b1d8e1d8d7c66142201f930799f74", "lastUpdated": 1753497707002}, "get:/bd-client/patientDoc/queryById": {"hash": "4bdf574b412b4c4c20f2c07ebdd54b6f", "lastUpdated": 1753497707021}, "get:/bd-client/patientDoc/queryByPatientId": {"hash": "0b5948021cd1f56f17f301aa8d8c67ea", "lastUpdated": 1753497707045}, "post:/bd-client/crm/activateBenefitsCallBack": {"hash": "c90b4abe85a35362bb1ecb57f34f6cba", "lastUpdated": 1753497707102}, "post:/bd-client/crm/getDoctorTeamWithPkgId": {"hash": "fd0f5dd63d94802db3ddaa44caa7a126", "lastUpdated": 1753497707121}, "get:/bd-client/crm/getOrgDict": {"hash": "c2dec3cffbcc79f8f252bae8374b0286", "lastUpdated": 1753497707138}, "post:/bd-client/crm/getToken": {"hash": "8454b957c2902c5e20d119e7170483e0", "lastUpdated": 1753497707156}, "get:/bd-client/crm/hello": {"hash": "13a246176b24358bd47bdc045b786437", "lastUpdated": 1753497707172}, "post:/bd-client/crm/orgteam/save": {"hash": "d2f74f553f145fba88d563487b9f9089", "lastUpdated": 1753497707188}, "post:/bd-client/crm/package/save": {"hash": "c362b058ea886f47dea972a322e808c9", "lastUpdated": 1753497707212}, "post:/bd-client/crm/queryUserRights": {"hash": "1f72e60cae156c4197064551977d5bdb", "lastUpdated": *************}, "post:/bd-client/crm/syncOrderStatus": {"hash": "936e36ed3fe4f4f87214027009752c7f", "lastUpdated": *************}, "post:/bd-client/crm/token": {"hash": "91e6f7c5d1ef9439adebb2498c22a043", "lastUpdated": *************}, "post:/bd-client/crm/user/save": {"hash": "a9b9c3d35ae68a8e08266893aa1eb942", "lastUpdated": *************}, "post:/bd-client/crm/userRights": {"hash": "ffc5fab2d072491379051b4cde3d907e", "lastUpdated": *************}, "post:/bd-client/externalInterface/queryAccountInfoByAccountId": {"hash": "c9cac254aafc6f33eb48ab2bde876c30", "lastUpdated": *************}, "post:/bd-client/externalInterface/token": {"hash": "6206720895c26fd3775eea3485814f2d", "lastUpdated": *************}, "post:/bd-client/file/upload": {"hash": "cd43e173badee809516cf522459a1af0", "lastUpdated": *************}, "post:/bd-client/flow/execute": {"hash": "411629b5a822dde6466c44224f5997c2", "lastUpdated": *************}, "post:/bd-client/flow/page/execute": {"hash": "dcf864cbfcb75db49d438bf64201a309", "lastUpdated": *************}, "get:/bd-client/flow/tcm/history": {"hash": "07aee00d4b15dc121fc87ad3e20f8a28", "lastUpdated": *************}, "post:/bd-client/flow/union/tasks": {"hash": "83e1685318aa575a58219d965b5263af", "lastUpdated": *************}, "post:/bd-client/flow/update/execute": {"hash": "65ed5c72986b821ae7678e4551f74cc7", "lastUpdated": *************}, "get:/bd-client/gdtocrm/GetAccountInfo": {"hash": "e8b460e8075ebc821e9e587709bd638c", "lastUpdated": *************}, "post:/bd-client/gdtocrm/UpdateTreatmentResult": {"hash": "634af992192518fce34682954af798d0", "lastUpdated": *************}, "post:/bd-client/gdtocrm/getServiceTimes": {"hash": "acc66456fd5c88051a898a6199036f61", "lastUpdated": *************}, "post:/bd-client/gdtocrm/inspect": {"hash": "1e6f75988fc59f3bddd41b6fe8d91974", "lastUpdated": *************}, "post:/bd-client/gdtocrm/page": {"hash": "7a590400a2de6491691d5dc659bb8605", "lastUpdated": *************}, "post:/bd-client/gdtocrm/priceSum": {"hash": "10cf45eb47993db27cf8d4a7b59fc362", "lastUpdated": *************}, "post:/bd-client/gdtocrm/sendSms": {"hash": "13526c9ef7e31fb6154a2a7febc40333", "lastUpdated": *************}, "post:/bd-client/ih/bindUserAndPatient": {"hash": "b0c34298e25e30b313c77954ea79aa4d", "lastUpdated": *************}, "post:/bd-client/ih/channelMainBindFiling": {"hash": "59903ca76c3faea28e840c6aa0499ff2", "lastUpdated": 1753497707699}, "post:/bd-client/message/haveUnRead": {"hash": "1578e12cbbf94cd2e73cd4b8458bb3bf", "lastUpdated": 1753497707710}, "post:/bd-client/message/page": {"hash": "312b8b34bcdbb3e484d95a4add84e36d", "lastUpdated": 1753497707734}, "post:/bd-client/message/query": {"hash": "fa8576e129dddd18c4786d77c840cf34", "lastUpdated": 1753497707746}, "post:/bd-client/message/readByIds": {"hash": "bdda9087680766635a13ab858cb5d97a", "lastUpdated": 1753497707759}, "post:/bd-client/order/appointment": {"hash": "4cd9d3828a45c65cd014e3876cf825d3", "lastUpdated": 1753497707808}, "post:/bd-client/organ/start": {"hash": "2bd6008bbbaa3ab447af000e22d395da", "lastUpdated": 1753497707858}, "post:/bd-client/other/cancel/order": {"hash": "47ff58c17590573ecab5ea868125a745", "lastUpdated": 1753497707888}, "post:/bd-client/other/change/appointment": {"hash": "80cabfb81aacd165274c7995e244f50c", "lastUpdated": 1753497707913}, "get:/bd-client/other/index/total": {"hash": "7b2b64fc834efe80a7f1d096f39f14c0", "lastUpdated": 1753497707981}, "post:/bd-client/other/target/url": {"hash": "552ee1322239377b8bbf577007e2bdf2", "lastUpdated": 1753497708000}, "post:/bd-client/call/createPatCall": {"hash": "4f5e5c46dc4dc5d970017d7cf66e7677", "lastUpdated": 1753497708012}, "post:/bd-client/call/exitPatCall": {"hash": "b92c3cb29134954450dc61f8b9301415", "lastUpdated": 1753497708028}, "post:/bd-client/call/urgentPatCall": {"hash": "41b06557b7371093b074325e6a40af1b", "lastUpdated": 1753497708040}, "post:/bd-client/patientInfo/getPatientDoctorTeamInfo": {"hash": "a203f2ca2c8facfe7f0209b68e385e02", "lastUpdated": 1753497708120}, "get:/bd-client/inquiry/detail": {"hash": "74e6e452ba700788853ef86840cc5c53", "lastUpdated": 1753497708181}, "post:/bd-client/inquiry/page": {"hash": "82a2a42978be43a66b35ea62ca0ed215", "lastUpdated": 1753497708210}, "post:/bd-client/processService/list": {"hash": "bed3bb59b29aa578e1f0df39bbd54e7e", "lastUpdated": 1753497708236}, "post:/bd-client/patientTemplatePackageView/closeAndAdd": {"hash": "c557add8d392bdb437861e6488d87eea", "lastUpdated": 1753497708254}, "post:/bd-client/patientTemplatePackageView/queryDocTemplate": {"hash": "89760c98ba13f1cdf717d8386e58fa84", "lastUpdated": 1753497708270}, "post:/bd-client/report/add": {"hash": "9766e006ac1540888f1a32ee9225e563", "lastUpdated": 1753497708324}, "get:/bd-client/report/delete": {"hash": "06551753602f6e79746186a7ea5cd200", "lastUpdated": 1753497708337}, "get:/bd-client/report/health": {"hash": "3197655253ff095fd171a3df3460215b", "lastUpdated": 1753497708354}, "post:/bd-client/report/list": {"hash": "0bba3530763a9567f1c94643d067bea4", "lastUpdated": 1753497708377}, "post:/bd-client/report/page": {"hash": "7739c2dfdeb799428722375692a2903b", "lastUpdated": 1753497708398}, "get:/bd-client/report/read": {"hash": "998f9a6b0abf0140d47f34c9701540b1", "lastUpdated": 1753497708409}, "get:/bd-client/report/total": {"hash": "0b6d7642e6c113b480df1fb4fc1685b4", "lastUpdated": 1753497708435}, "post:/bd-client/report/update": {"hash": "6faa5fe1025de35a443d9e5f090c8673", "lastUpdated": 1753497708459}, "post:/bd-client/rightsReceive/activateBenefits": {"hash": "143b59ea845d7706cf9bb2b42c6f7e5d", "lastUpdated": 1753497708483}, "post:/bd-client/rightsReceive/claimBenefits": {"hash": "156f00b8655db43e49324c991e2cac49", "lastUpdated": 1753497708502}, "get:/bd-client/rightsReceive/claimBenefitsForIndividual": {"hash": "966f19daad09d9b66c87a2cebc837f5a", "lastUpdated": 1753497708528}, "get:/bd-client/rightsReceive/getHealthAuthCode": {"hash": "********************************", "lastUpdated": 1753497708543}, "post:/bd-client/rightsReceive/queryBenefitsInfo": {"hash": "329c276f996cbeb0a4be5e07aafb402d", "lastUpdated": 1753497708637}, "get:/bd-client/rightsReceive/queryByBatchNumber": {"hash": "f84754f44171b18cfd297cd72b1702a2", "lastUpdated": 1753497708679}, "get:/bd-client/rightsReceive/queryRecords": {"hash": "8598a0444c8796d0fb413beba5084641", "lastUpdated": 1753497708693}, "post:/bd-client/rightsReceive/useBenefitsInfo": {"hash": "e36b49c6c95ad5fd57e43fb0d6bb15cf", "lastUpdated": 1753497708709}, "put:/bd-client/system/dict/data": {"hash": "ff12581b40d5383719a5fc6b81761ed9", "lastUpdated": 1753497709022}, "post:/bd-client/system/dict/data": {"hash": "8b62f55f2079a95ab0a92b7565dc663a", "lastUpdated": 1753497709055}, "get:/bd-client/system/dict/data/list": {"hash": "0a18133231ea682ebb2f2b0dbcd0b330", "lastUpdated": 1753497709091}, "get:/bd-client/system/dict/data/type/{dictType}": {"hash": "eb884224cd543f2ebf39b1f0aad0b4f3", "lastUpdated": 1753497709150}, "delete:/bd-client/system/dict/data/{dictCodes}": {"hash": "47d1bd20f64596f1c8a1bd3584302bf0", "lastUpdated": 1753497709167}, "get:/bd-client/system/dict/data/{dictCode}": {"hash": "10d015092802ea8c710896307d1b2614", "lastUpdated": 1753497709186}, "put:/bd-client/system/dict/type": {"hash": "4dfe83597b8989c63de83a6ebbf26310", "lastUpdated": 1753497709201}, "post:/bd-client/system/dict/type": {"hash": "b7086f943e5a1f7faa715681ad17d380", "lastUpdated": 1753497709214}, "get:/bd-client/system/dict/type/list": {"hash": "f51dcbc80fdd5d86786f811e2641950a", "lastUpdated": 1753497709369}, "get:/bd-client/system/dict/type/optionselect": {"hash": "535f8fdab46b9947ba5054875e4379cf", "lastUpdated": 1753497709382}, "delete:/bd-client/system/dict/type/refreshCache": {"hash": "36b0b4baee3ac8e3819b583c9578070f", "lastUpdated": 1753497709405}, "delete:/bd-client/system/dict/type/{dictIds}": {"hash": "a37603cfac67a6ba616dae07b7716e87", "lastUpdated": 1753497709422}, "get:/bd-client/system/dict/type/{dictId}": {"hash": "25ee76c224cc915a298b1f6b9174a5cf", "lastUpdated": 1753497709439}, "post:/bd-client/third/consultationOrderList": {"hash": "f2afa0a6bc9425e3381cb2b4a49132e8", "lastUpdated": 1753497709455}, "post:/bd-client/customer/authCode": {"hash": "7cfe3694b60698e3fd63536aa3e7f95d", "lastUpdated": 1753497709480}, "get:/bd-client/customer/get": {"hash": "d08ecd0ec0835c5adf4fd4606c04daf5", "lastUpdated": 1753497709497}, "get:/bd-client/customer/getHyMessageList": {"hash": "97e42a5cb2372e29c92f7315f487c89d", "lastUpdated": 1753497709513}, "post:/bd-client/customer/login": {"hash": "e6271fdaaa2e5bddaf06bd6f0ef2af02", "lastUpdated": 1753497709529}, "post:/bd-client/customer/login/free": {"hash": "e33f01c69e75ba56851596ecc79be6ac", "lastUpdated": 1753497709544}, "get:/bd-client/customer/login/getFreeLoginToken": {"hash": "04f8c7fa2f52016c04d40d6b2dacfe27", "lastUpdated": 1753497709565}, "post:/bd-client/customer/name": {"hash": "7399ba10298f41e2fbcffe401df28f49", "lastUpdated": 1753497709574}, "post:/bd-client/customer/queryUserRightsWithPatientId": {"hash": "3c679c16025bbeaba909e7f3872aaf38", "lastUpdated": 1753497709596}, "get:/bd-client/customer/servicePackageIdByAse": {"hash": "35d81d7604440274402e9bdd9c4594da", "lastUpdated": 1753497709607}, "post:/bd-client/customer/sync/user": {"hash": "5747562d294e60376604436356bb1a8b", "lastUpdated": 1753497709624}, "index:YongHuXiangGuan": {"hash": "7c4ef8ee541d2036baea3e4b7e342865", "lastUpdated": 1753342131120}, "get:/bd-client/customer/syncCrmUser": {"hash": "fb9fa0452515f776c0dead03952b954a", "lastUpdated": 1753497709635}, "post:/bd-client/third/getPackageByPhone": {"hash": "e1d4ef22e9bcb614fc12ac489fce214e", "lastUpdated": 1753497709469}, "index:QuanYiLingQuXiangGuanAPI": {"hash": "8b9679f6d619054b7a3cb3d032ae2c96", "lastUpdated": 1753492923129}}}