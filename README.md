<!--
 * @Description: insert description
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-11-15 10:02:32
 
 * @LastEditors: yangrong<PERSON>
 * @LastEditTime: 2025-01-07 10:30:33
-->
# 规范
## 文件夹命名，功能性的js文件 -- 使用kebab-case
例如：user-account

## vue组件命名 -- 使用kebab-case
例如：user-account.vue

## 项目启动
使用pnpm v9及以上版本安装依赖并启动，版本锁定使用根目录pnpm-lock.yaml实现

## 项目文档
### 原型图/设计稿地址
https://mastergo.com/file/***************?fileOpenFrom=project&fileTileSwitch=false&page_id=4%3A0791 - 原型
https://mastergo.com/file/***************?fileOpenFrom=project&fileTileSwitch=false&page_id=M - 设计图
### 项目计划文档地址
位于项目组的钉钉网盘内
### 产品对接人
@华西公用-李亮颖

### 接口地址
http://***************:8261/bd-client/swagger-ui.html#!/25253215783045620851API/totalUsingGET
#### 接口对接人
@华西公用-徐全美 php
@华西公用-徐乾力 java