* {
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px;
  background-color: var(--color-bg-1);
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

#app {
  height: 100vh;
}

.cursor-pointer {
  cursor: pointer;
}

// 取消抽屉组件的内部样式
// .arco-drawer-body {
//   padding: 0 !important;
// }

// 多选框的内部样式
// .arco-select-view-multiple {
//   padding-left: 10px !important;
//   padding-right: 10px !important;
// }

.datePickerView .arco-panel-date-inner {
  width: 514px !important;
}

.datePickerView .arco-picker-footer-extra-wrapper {
  border-top: none;
}

.datePickerView .arco-picker-footer-btn-wrapper {
  display: none;
}

div[body-class="noPadding"] .arco-drawer-body {
  padding: 0px;
}

div[body-class="appointmentView"] .arco-drawer-body {
  background: #f9fbff !important;
  padding: 0px;
}

div[body-class="appointmentDetailsView"] .arco-drawer-body {
  background: #f9fbff !important;
  padding: 0px;
}

.outboundModal .arco-modal-footer {
  border-top: none;
}

.arco-picker .arco-picker-start-time {
  padding-left: 5px !important;
}

.echarts-tooltip-diy {
  background: linear-gradient(
    304.17deg,
    rgb(253 254 255 / 60%) -6.04%,
    rgb(244 247 252 / 60%) 85.2%
  ) !important;
  border: none !important;

  /* Note: backdrop-filter has minimal browser support */

  border-radius: 6px !important;
  backdrop-filter: blur(10px) !important;

  .content-panel {
    display: flex;
    justify-content: space-between;
    width: 164px;
    height: 32px;
    margin-bottom: 4px;
    padding: 0 9px;
    line-height: 32px;
    background: rgb(255 255 255 / 80%);
    border-radius: 4px;
    box-shadow: 6px 0 20px rgb(34 87 188 / 10%);
  }

  .tooltip-title {
    margin: 0 0 10px;
  }

  p {
    margin: 0;
  }

  .tooltip-title,
  .tooltip-value {
    display: flex;
    align-items: center;
    color: #1d2129;
    font-weight: bold;
    font-size: 13px;
    line-height: 15px;
    text-align: right;
  }

  .tooltip-item-icon {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin-right: 8px;
    border-radius: 50%;
  }
}

.general-card {
  border: none;
  border-radius: 4px;

  & > .arco-card-header {
    height: auto;
    padding: 20px;
    border: none;
  }

  & > .arco-card-body {
    padding: 0 20px 20px;
  }
}

.split-line {
  border-color: rgb(var(--gray-2));
}

.arco-table-cell {
  .circle {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 4px;
    background-color: rgb(var(--blue-6));
    border-radius: 50%;

    &.pass {
      background-color: rgb(var(--green-6));
    }
  }
}

input::placeholder {
  color: #86909c !important;
  opacity: 0.5;
}
.full-width {
  width: 100%;
}
