:root {
  --gray-10: hsl(220, 17%, 14%);
  --gray-9: #272e3b;
  --gray-8: #4e5969;
  --gray-7: #6b7785;
  --gray-6: #86909c;
  --gray-5: #a9aeb8;
  --gray-4: #c9cdd4;
  --gray-3: #e5e6eb;
  --gray-2: #f2f3f5;
  --gray-1: #f7f8fa;
  --gold-6: #f7ba1e;
  --gold-5: #F9CC45;
  --gold-7: #CC9213;
  --gold-4: #FADC6D;
  --gold-3: #FCE996;
  --gold-2: #FDF4BF;
  --gold-1: #FFFCE8;
  --yellow-6: #fadc19;
  --yellow-7: #CFAF0F;
  --yellow-5: #FBE842;
  --yellow-4: #FCF26B;
  --yellow-3: #FDFA94;
  --yellow-2: #FEFEBE;
  --yellow-1: #FEFFE8;
  --lime-6: #9fdb1d;
  --lime-7: #7EB712;
  --lime-5: #B5E241;
  --lime-4: #C9E968;
  --lime-3: #DCF190;
  --lime-2: #EDF8BB;
  --lime-1: #FCFFE8;
  --cyan-6: #14c9c9;
  --cyan-7: #0DA5AA;
  --cyan-5: #37D4CF;
  --cyan-4: #5EDFD6;
  --cyan-3: #89E9E0;
  --cyan-2: #B7F4EC;
  --cyan-1: #E8FFFB;
  --blue-6: #3491fa;
  --blue-5: #57A9FB;
  --blue-4: #7BC0FC;
  --blue-3: #9FD4FD;
  --blue-2: #C3E7FE;
  --blue-1: #E8F7FF;
  --purple-6: #722ed1;
  --purple-7: #551DB0;
  --purple-5: #8D4EDA;
  --purple-4: #A871E3;
  --purple-3: #C396ED;
  --purple-2: #DDBEF6;
  --purple-1: #F5E8FF;
  --pinkpurple-6: #d91ad9;
  --pinkpurple-7: #B010B6;
  --pinkpurple-5: #E13EDB;
  --pinkpurple-4: #E865DF;
  --pinkpurple-3: #F08EE6;
  --pinkpurple-2: #F7BAEF;
  --pinkpurple-1: #FFE8FB;
  --magenta-6: #f5319d;
  --magenta-7: #CB1E83;
  --magenta-5: #F754A8;
  --magenta-4: #F979B7;
  --magenta-3: #FB9DC7;
  --magenta-2: #FDC2DB;
  --magenta-1: #FFE8F1;
  --border-radius-none: 0;
  --border-radius-small: 2px;
  --border-radius-medium: 4px;
  --border-radius-large: 8px;
  --border-radius-delete: 12px;
  --border-radius-circle: 50%;
  --border-none: 0;
  --border-1: 1px;
  --border-2: 2px;
  --border-3: 3px;
  --border-4: 4px;
  --border-solid: solid;
  --border-dashed: dashed;
  --border-dotted: dotted;
  --color-menu-light-bg: #192A53;
  --color-menu-dark-bg: #232324;
  --shadow-none: none;
  --shadow-special: 0 0 1px rgba(0, 0, 0, 0.3);
  --shadow1-center: 0 0 5px rgba(0, 0, 0, 0.1);
  --shadow1-up: 0 -2px 5px rgba(0, 0, 0, 0.1);
  --shadow1-down: 0 2px 5px rgba(0, 0, 0, 0.1);
  --shadow1-left: -2px 0 5px rgba(0, 0, 0, 0.1);
  --shadow1-right: 2px 0 5px rgba(0, 0, 0, 0.1);
  --shadow1-left-up: -2px -2px 5px rgba(0, 0, 0, 0.1);
  --shadow1-left-down: -2px 2px 5px rgba(0, 0, 0, 0.1);
  --shadow1-right-up: 2px -2px 5px rgba(0, 0, 0, 0.1);
  --shadow1-right-down: 2px 2px 5px rgba(0, 0, 0, 0.1);
  --shadow2-center: 0 0 10px rgba(0, 0, 0, 0.1);
  --shadow2-up: 0 -4px 10px rgba(0, 0, 0, 0.1);
  --shadow2-down: 0 4px 10px rgba(0, 0, 0, 0.1);
  --shadow2-left: -4px 0 10px rgba(0, 0, 0, 0.1);
  --shadow2-right: 4px 0 10px rgba(0, 0, 0, 0.1);
  --shadow2-left-up: -4px -4px 10px rgba(0, 0, 0, 0.1);
  --shadow2-left-down: -4px 4px 10px rgba(0, 0, 0, 0.1);
  --shadow2-right-up: 4px -4px 10px rgba(0, 0, 0, 0.1);
  --shadow2-right-down: 4px 4px 10px rgba(0, 0, 0, 0.1);
  --shadow3-center: 0 0 20px rgba(0, 0, 0, 0.1);
  --shadow3-up: 0 -8px 20px rgba(0, 0, 0, 0.1);
  --shadow3-down: 0 8px 20px rgba(0, 0, 0, 0.1);
  --shadow3-left: -8px 0 20px rgba(0, 0, 0, 0.1);
  --shadow3-right: 8px 0 20px rgba(0, 0, 0, 0.1);
  --shadow3-left-up: -8px -8px 20px rgba(0, 0, 0, 0.1);
  --shadow3-left-down: -8px 8px 20px rgba(0, 0, 0, 0.1);
  --shadow3-right-up: 8px -8px 20px rgba(0, 0, 0, 0.1);
  --shadow3-right-down: 8px 8px 20px rgba(0, 0, 0, 0.1);
  --shadow-distance-none: 0;
  --shadow-distance-1: 1px;
  --shadow-distance-2: 2px;
  --shadow-distance-3: 3px;
  --shadow-distance-4: 4px;
  --font-family: Inter,-apple-system,BlinkMacSystemFont,PingFang SC,Hiragino Sans GB,noto sans,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif;
  --font-size-small: 10px;
  --font-size-display-3: 56px;
  --font-size-display-2: 48px;
  --font-size-display-1: 36px;
  --font-size-title-3: 24px;
  --font-size-title-2: 20px;
  --font-size-title-1: 16px;
  --font-size-body-4: 18px;
  --font-size-body-3: 14px;
  --font-size-body-2: 13px;
  --font-size-body-1: 12px;
  --font-size-caption: 12px;
  --font-weight-900: 900;
  --font-weight-800: 800;
  --font-weight-700: 700;
  --font-weight-600: 600;
  --font-weight-500: 500;
  --font-weight-400: 400;
  --font-weight-300: 300;
  --font-weight-200: 200;
  --font-weight-100: 100;
  --size-1: 4px;
  --size-2: 8px;
  --size-3: 12px;
  --size-4: 16px;
  --size-5: 20px;
  --size-6: 24px;
  --size-7: 28px;
  --size-8: 32px;
  --size-9: 36px;
  --size-10: 40px;
  --size-11: 44px;
  --size-12: 48px;
  --size-13: 52px;
  --size-14: 56px;
  --size-15: 60px;
  --size-16: 64px;
  --size-17: 68px;
  --size-18: 72px;
  --size-19: 76px;
  --size-20: 80px;
  --size-21: 84px;
  --size-22: 88px;
  --size-23: 92px;
  --size-24: 96px;
  --size-25: 100px;
  --size-26: 104px;
  --size-27: 108px;
  --size-28: 112px;
  --size-29: 116px;
  --size-30: 120px;
  --size-31: 124px;
  --size-32: 128px;
  --size-33: 132px;
  --size-34: 136px;
  --size-35: 140px;
  --size-36: 144px;
  --size-37: 148px;
  --size-38: 152px;
  --size-39: 156px;
  --size-40: 160px;
  --size-41: 164px;
  --size-42: 168px;
  --size-43: 172px;
  --size-44: 176px;
  --size-45: 180px;
  --size-46: 184px;
  --size-47: 188px;
  --size-48: 192px;
  --size-49: 196px;
  --size-50: 200px;
  --spacing-none: 0;
  --spacing-1: 2px;
  --spacing-2: 4px;
  --spacing-3: 6px;
  --spacing-4: 8px;
  --spacing-5: 10px;
  --spacing-6: 12px;
  --spacing-7: 16px;
  --spacing-8: 20px;
  --spacing-9: 24px;
  --spacing-10: 32px;
  --spacing-11: 36px;
  --spacing-12: 40px;
  --spacing-13: 48px;
  --spacing-14: 56px;
  --spacing-15: 60px;
  --spacing-16: 64px;
  --spacing-17: 72px;
  --spacing-18: 80px;
  --spacing-19: 84px;
  --spacing-20: 96px;
  --spacing-21: 100px;
  --spacing-22: 120px;
  --size-mini: --size-6;
  --size-small: --size-7;
  --size-default: --size-8;
  --size-large: --size-9;
  --upload-prefix-cls: ~'--{prefix}-upload';
  --steps-prefix-cls: ~'--{prefix}-steps';
  --image-prefix-cls: ~'--{prefix}-image';
  --resizebox-prefix-cls: ~'--{prefix}-resizebox';
  --card-prefix-cls: ~'--{prefix}-card';
  --breadcrumb-prefix-cls: ~'--{prefix}-breadcrumb';
  --avatar-prefix-cls: ~'--{prefix}-avatar';
  --dropdown-prefix-cls: ~'--{prefix}-dropdown';
  --list-prefix-cls: ~'--{prefix}-list';
  --page-header-prefix-cls: ~'--{prefix}-page-header';
  --table-prefix-cls: ~'--{prefix}-table';
  --spin-prefix-cls: ~'--{prefix}-spin';
  --statistic-prefix-cls: ~'--{prefix}-statistic';
  --descriptions-prefix-cls: ~'--{prefix}-descriptions';
  --timeline-prefix-cls: ~'--{prefix}-timeline';
  --tag-prefix-cls: ~'--{prefix}-tag';
  --switch-prefix-cls: ~'--{prefix}-switch';
  --collapse-prefix-cls: ~'--{prefix}-collapse';
  --input-tag-prefix-cls: ~'--{prefix}-input-tag';
  --comment-prefix-cls: ~'--{prefix}-comment';
  --input-prefix-cls: ~'--{prefix}-input';
  --result-prefix-cls: ~'--{prefix}-result';
  --input-number-prefix-cls: ~'--{prefix}-input-number';
  --tree-select-prefix-cls: ~'--{prefix}-tree-select';
  --btn-prefix-cls: ~'--{prefix}-btn';
  --cascader-prefix-cls: ~'--{prefix}-cascader';
  --select-prefix-cls: ~'--{prefix}-select';
  --menu-prefix-cls: ~'--{prefix}-menu';
  --transfer-prefix-cls: ~'--{prefix}-transfer';
  --anchor-prefix-cls: ~'--{prefix}-anchor';
  --picker-prefix-cls: ~'--{prefix}-picker';
  --date-panel-prefix-cls: ~'--{prefix}-panel-date';
  --time-picker-prefix-cls: ~'--{prefix}-timepicker';
  --quarter-panel-prefix-cls: ~'--{prefix}-panel-quarter';
  --year-panel-prefix-cls: ~'--{prefix}-panel-year';
  --week-panel-prefix-cls: ~'--{prefix}-panel-week';
  --range-picker-prefix-cls: ~'--{prefix}-picker-range';
  --date-picker-prefix-cls: ~'--{prefix}-datepicker';
  --modal-prefix-cls: ~'--{prefix}-modal';
  --drawer-prefix-cls: ~'--{prefix}-drawer';
  --auto-complete-prefix-cls: ~'--{prefix}-autocomplete';
  --tabs-prefix-cls: ~'--{prefix}-tabs';
  --checkbox-prefix-cls: ~'--{prefix}-checkbox';
  --form-prefix-cls: ~'--{prefix}-form';
  --radio-prefix-cls: ~'--{prefix}-radio';
  --link-prefix-cls: ~'--{prefix}-link';
  --divider-prefix-cls: ~'--{prefix}-divider';
  --tooltip-prefix-cls: ~'--{prefix}-tooltip';
  --popover-prefix-cls: ~'--{prefix}-popover';
  --popconfirm-prefix-cls: ~'--{prefix}-popconfirm';
  --message-prefix-cls: ~'--{prefix}-message';
  --notification-prefix-cls: ~'--{prefix}-notification';
  --tree-prefix-cls: ~'--{prefix}-tree';
  --alert-prefix-cls: ~'--{prefix}-alert';
  --pagination-prefix-cls: ~'--{prefix}-pagination';
  --progress-prefix-cls: ~'--{prefix}-progress';
  --empty-prefix-cls: ~'--{prefix}-empty';
  --slider-prefix: ~'--{prefix}-slider';
  --typography-prefix-cls: ~'--{prefix}-typography';
  --month-panel-prefix-cls: ~'--{prefix}-panel-month';
  --red-4: #F98981;
  --red-1: #FFECE8;
  --orange-2: #FFE4BA;
  --orangered-10: #4D0E00;
  --red-2: #FDCDC5;
  --orange-8: #A64500;
  --orange-9: #792E00;
  --orange-7: #D25F00;
  --red-9: #770813;
  --orange-6: #ff7d00;
  --orangered-6: #f77234;
  --orangered-7: #CC5120;
  --orangered-2: #FDDDC3;
  --orange-3: #FFCF8B;
  --red-3: #FBACA3;
  --orange-10: #4D1B00;
  --orangered-4: #FAAC7B;
  --red-7: #CB272D;
  --red-10: #4D000A;
  --red-5: #F76560;
  --gold-10: #4D2D00;
  --yellow-10: #4D3800;
  --red-6: #f53f3f;
  --orangered-3: #FCC59F;
  --orange-1: #FFF7E8;
  --orange-5: #FF9A2E;
  --orangered-1: #FFF3E8;
  --orangered-8: #A23511;
  --red-8: #A1151E;
  --yellow-8: #A38408;
  --orangered-9: #771F06;
  --gold-9: #774B04;
  --gold-8: #A26D0A;
  --lime-8: #5F940A;
  --green-6: #00b42a;
  --yellow-9: #785D03;
  --lime-9: #437004;
  --cyan-8: #07828B;
  --green-4: #4CD263;
  --green-9: #006622;
  --cyan-9: #03616C;
  --orangered-5: #F99057;
  --green-3: #7BE188;
  --lime-10: #2A4D00;
  --green-10: #004D1C;
  --green-7: #009A29;
  --green-5: #23C343;
  --green-1: #E8FFEA;
  --green-8: #008026;
  --cyan-10: #00424D;
  --green-2: #AFF0B5;
  --arcoblue-1: #E8F3FF;
  --arcoblue-6: #165dff;
  --blue-9: #063078;
  --arcoblue-2: #BEDAFF;
  --arcoblue-4: #6AA1FF;
  --arcoblue-3: #94BFFF;
  --blue-8: #114BA3;
  --blue-10: #001A4D;
  --blue-7: #206CCF;
  --arcoblue-8: #072CA6;
  --purple-10: #16004D;
  --arcoblue-9: #031A79;
  --pinkpurple-8: #8A0993;
  --pinkpurple-10: #42004D;
  --purple-8: #3C108F;
  --magenta-9: #77064F;
  --magenta-8: #A11069;
  --arcoblue-5: #4080FF;
  --arcoblue-7: #0E42D2;
  --arcoblue-10: #000D4D;
  --purple-9: #27066E;
  --pinkpurple-9: #650370;
  --dark-red-10: #FFF0EC;
  --dark-orangered-9: #FDDEC5;
  --dark-orange-4: #D26913;
  --dark-green-1: #004D1C;
  --dark-blue-9: #C6E8FE;
  --dark-purple-6: #8E51DA;
  --dark-pinkpurple-5: #D92ED9;
  --dark-green-4: #129A37;
  --magenta-10: #4D0034;
  --dark-lime-7: #CBE970;
  --dark-yellow-3: #A38614;
  --dark-orangered-1: #4D0E00;
  --dark-gold-3: #A26F0F;
  --dark-orange-7: #FFB357;
  --dark-cyan-4: #1FA6AA;
  --dark-yellow-6: #FBE94B;
  --dark-red-5: #F54E4E;
  --dark-purple-9: #DFC2F6;
  --dark-magenta-10: #FFE8F1;
  --dark-orangered-8: #FCC6A1;
  --dark-red-2: #770611;
  --dark-gold-2: #774B04;
  --dark-orangered-7: #FAAD7D;
  --dark-arcoblue-9: #BEDAFF;
  --dark-gold-7: #FADC6C;
  --dark-lime-2: #447006;
  --dark-gold-1: #4D2D00;
  --dark-orangered-3: #A23714;
  --dark-cyan-5: #30C9C9;
  --dark-red-3: #A1161F;
  --dark-orange-1: #4D1B00;
  --dark-red-7: #F98D86;
  --dark-lime-9: #EEF8C2;
  --dark-cyan-2: #06616C;
  --dark-purple-2: #27066E;
  --dark-pinkpurple-9: #F7C1F0;
  --dark-arcoblue-7: #689FFF;
  --dark-red-6: #F76965;
  --dark-arcoblue-4: #1D4DD2;
  --dark-yellow-7: #FCF374;
  --dark-red-9: #FDD1CA;
  --dark-yellow-9: #FEFEC6;
  --dark-magenta-1: #4D0034;
  --dark-gray-4: #5f5f60;
  --dark-yellow-2: #785E07;
  --dark-pinkpurple-3: #8A0D93;
  --dark-gold-5: #F7C034;
  --dark-primary-6: rgb(34, 113, 225);
  --dark-arcoblue-8: #93BEFF;
  --dark-green-8: #7EE18B;
  --dark-arcoblue-3: #0E32A6;
  --success-6: rgb(var(~'--{arco-cssvars-prefix}-green-6'));
  --dark-gray-1: #17171a;
  --dark-yellow-4: #CFB325;
  --dark-primary-8: rgb(127, 182, 240);
  --dark-purple-10: #F7EDFF;
  --success-10: rgb(var(~'--{arco-cssvars-prefix}-green-10'));
  --dark-orange-3: #A64B0A;
  --dark-gray-6: #929293;
  --dark-yellow-1: #4D3800;
  --dark-orange-2: #793004;
  --dark-orange-10: #FFF7E8;
  --dark-pinkpurple-7: #E866DF;
  --dark-gold-9: #FDF4BE;
  --dark-orange-5: #FF8D1F;
  --dark-blue-8: #A1D5FD;
  --dark-orangered-10: #FFF4EB;
  --primary-1: rgb(232, 245, 255);
  --dark-red-8: #FBB0A7;
  --success-9: rgb(var(~'--{arco-cssvars-prefix}-green-9'));
  --primary-4: rgb(85, 151, 232);
  --dark-lime-10: #FDFFEE;
  --dark-gray-5: #78787a;
  --dark-yellow-5: #FAE13C;
  --dark-magenta-3: #A1176C;
  --dark-arcoblue-5: #306FFF;
  --success-5: rgb(var(~'--{arco-cssvars-prefix}-green-5'));
  --dark-lime-4: #84B723;
  --dark-magenta-7: #F97AB8;
  --dark-yellow-8: #FDFA9D;
  --dark-magenta-2: #770850;
  --dark-green-7: #50D266;
  --dark-success-1: rgb(var(~'--{arco-cssvars-prefix}-green-1'));
  --dark-blue-3: #134CA3;
  --dark-blue-1: #001A4D;
  --dark-pinkpurple-1: #42004D;
  --dark-primary-2: rgb(3, 34, 112);
  --dark-pinkpurple-2: #650370;
  --dark-green-5: #1DB440;
  --dark-purple-1: #16004D;
  --dark-magenta-6: #F756A9;
  --dark-pinkpurple-8: #F092E6;
  --dark-red-1: #4D000A;
  --dark-purple-4: #5A25B0;
  --dark-green-9: #B2F0B7;
  --primary-6: rgb(0, 82, 217);
  --dark-pinkpurple-6: #E13DDB;
  --dark-cyan-6: #3FD4CF;
  --dark-primary-10: rgb(232, 245, 255);
  --dark-primary-1: rgb(0, 19, 77);
  --dark-cyan-8: #90E9E1;
  --dark-cyan-3: #11838B;
  --danger-6: rgb(var(~'--{arco-cssvars-prefix}-red-6'));
  --dark-lime-1: #2A4D00;
  --dark-primary-4: rgb(16, 75, 182);
  --dark-success-3: rgb(var(~'--{arco-cssvars-prefix}-green-3'));
  --dark-orange-8: #FFCD87;
  --success-4: rgb(var(~'--{arco-cssvars-prefix}-green-4'));
  --dark-cyan-7: #66DFD7;
  --success-2: rgb(var(~'--{arco-cssvars-prefix}-green-2'));
  --success-1: rgb(var(~'--{arco-cssvars-prefix}-green-1'));
  --success-8: rgb(var(~'--{arco-cssvars-prefix}-green-8'));
  --dark-cyan-1: #00424D;
  --dark-gray-2: #2e2e30;
  --dark-lime-3: #629412;
  --success-3: rgb(var(~'--{arco-cssvars-prefix}-green-3'));
  --dark-primary-3: rgb(9, 52, 147);
  --dark-gold-8: #FCE995;
  --dark-arcoblue-1: #000D4D;
  --dark-blue-6: #5AAAFB;
  --dark-orangered-5: #F77E45;
  --dark-red-4: #CB2E34;
  --dark-purple-7: #A974E3;
  --dark-primary-9: rgb(178, 214, 247);
  --warning-4: rgb(var(~'--{arco-cssvars-prefix}-orange-4'));
  --dark-success-2: rgb(var(~'--{arco-cssvars-prefix}-green-2'));
  --dark-lime-5: #A8DB39;
  --danger-1: rgb(var(~'--{arco-cssvars-prefix}-red-1'));
  --dark-primary-7: rgb(79, 148, 232);
  --dark-success-5: rgb(var(~'--{arco-cssvars-prefix}-green-5'));
  --dark-blue-5: #469AFA;
  --dark-purple-8: #C59AED;
  --dark-arcoblue-6: #3C7EFF;
  --dark-gold-6: #F9CC44;
  --dark-gray-9: #dfdfdf;
  --dark-warning-7: rgb(var(~'--{arco-cssvars-prefix}-orange-7'));
  --dark-pinkpurple-10: #FFF2FD;
  --dark-yellow-10: #FEFFF0;
  --dark-green-10: #EBFFEC;
  --dark-green-3: #0A802D;
  --dark-orangered-2: #771E05;
  --warning-5: rgb(var(~'--{arco-cssvars-prefix}-orange-5'));
  --primary-7: rgb(0, 64, 182);
  --primary-9: rgb(0, 32, 112);
  --dark-cyan-9: #BEF4ED;
  --link-3: rgb(131, 184, 240);
  --dark-magenta-4: #CB2B88;
  --warning-1: rgb(var(~'--{arco-cssvars-prefix}-orange-1'));
  --dark-success-10: rgb(var(~'--{arco-cssvars-prefix}-green-10'));
  --success-7: rgb(var(~'--{arco-cssvars-prefix}-green-7'));
  --dark-warning-4: rgb(var(~'--{arco-cssvars-prefix}-orange-4'));
  --dark-primary-5: rgb(26, 98, 217);
  --dark-gold-10: #FFFCE8;
  --dark-danger-2: rgb(var(~'--{arco-cssvars-prefix}-red-2'));
  --warning-9: rgb(var(~'--{arco-cssvars-prefix}-orange-9'));
  --dark-magenta-9: #FDC3DB;
  --danger-5: rgb(var(~'--{arco-cssvars-prefix}-red-5'));
  --dark-danger-6: rgb(var(~'--{arco-cssvars-prefix}-red-6'));
  --link-8: rgb(0, 46, 147);
  --primary-2: rgb(180, 215, 247);
  --dark-cyan-10: #F0FFFC;
  --dark-gray-10: #f6f6f6;
  --dark-warning-3: rgb(var(~'--{arco-cssvars-prefix}-orange-3'));
  --link-2: rgb(180, 215, 247);
  --dark-green-2: #046625;
  --dark-danger-5: rgb(var(~'--{arco-cssvars-prefix}-red-5'));
  --link-10: rgb(0, 19, 77);
  --dark-danger-9: rgb(var(~'--{arco-cssvars-prefix}-red-9'));
  --dark-gray-8: #c5c5c5;
  --dark-magenta-8: #FB9EC8;
  --dark-success-4: rgb(var(~'--{arco-cssvars-prefix}-green-4'));
  --dark-danger-7: rgb(var(~'--{arco-cssvars-prefix}-red-7'));
  --dark-lime-8: #DEF198;
  --dark-arcoblue-10: #EAF4FF;
  --link-9: rgb(0, 32, 112);
  --danger-9: rgb(var(~'--{arco-cssvars-prefix}-red-9'));
  --dark-link-8: rgb(127, 182, 240);
  --link-5: rgb(41, 117, 225);
  --dark-gray-7: #ababac;
  --dark-danger-8: rgb(var(~'--{arco-cssvars-prefix}-red-8'));
  --link-4: rgb(85, 151, 232);
  --dark-warning-5: rgb(var(~'--{arco-cssvars-prefix}-orange-5'));
  --dark-magenta-5: #F545A6;
  --dark-success-6: rgb(var(~'--{arco-cssvars-prefix}-green-6'));
  --dark-lime-6: #B8E24B;
  --warning-6: rgb(var(~'--{arco-cssvars-prefix}-orange-6'));
  --link-6: rgb(0, 82, 217);
  --dark-danger-3: rgb(var(~'--{arco-cssvars-prefix}-red-3'));
  --primary-5: rgb(41, 117, 225);
  --dark-orange-9: #FFE3B8;
  --dark-warning-9: rgb(var(~'--{arco-cssvars-prefix}-orange-9'));
  --primary-3: rgb(131, 184, 240);
  --warning-3: rgb(var(~'--{arco-cssvars-prefix}-orange-3'));
  --dark-orange-6: #FF9626;
  --dark-purple-5: #7B3DD1;
  --dark-orangered-4: #CC5729;
  --dark-link-2: rgb(3, 34, 112);
  --primary-10: rgb(0, 19, 77);
  --dark-blue-2: #052F78;
  --dark-success-7: rgb(var(~'--{arco-cssvars-prefix}-green-7'));
  --warning-8: rgb(var(~'--{arco-cssvars-prefix}-orange-8'));
  --danger-3: rgb(var(~'--{arco-cssvars-prefix}-red-3'));
  --dark-link-4: rgb(16, 75, 182);
  --dark-link-7: rgb(79, 148, 232);
  --warning-7: rgb(var(~'--{arco-cssvars-prefix}-orange-7'));
  --dark-blue-4: #2971CF;
  --dark-gray-3: #484849;
  --dark-warning-6: rgb(var(~'--{arco-cssvars-prefix}-orange-6'));
  --dark-warning-10: rgb(var(~'--{arco-cssvars-prefix}-orange-10'));
  --dark-pinkpurple-4: #B01BB6;
  --warning-2: rgb(var(~'--{arco-cssvars-prefix}-orange-2'));
  --danger-7: rgb(var(~'--{arco-cssvars-prefix}-red-7'));
  --dark-warning-2: rgb(var(~'--{arco-cssvars-prefix}-orange-2'));
  --dark-purple-3: #3E138F;
  --dark-link-1: rgb(0, 19, 77);
  --dark-green-6: #27C346;
  --dark-danger-4: rgb(var(~'--{arco-cssvars-prefix}-red-4'));
  --dark-link-3: rgb(9, 52, 147);
  --dark-warning-1: rgb(var(~'--{arco-cssvars-prefix}-orange-1'));
  --primary-8: rgb(0, 46, 147);
  --dark-arcoblue-2: #041B79;
  --danger-4: rgb(var(~'--{arco-cssvars-prefix}-red-4'));
  --link-7: rgb(0, 64, 182);
  --link-1: rgb(232, 245, 255);
  --dark-blue-7: #7DC1FC;
  --dark-orangered-6: #F9925A;
  --danger-8: rgb(var(~'--{arco-cssvars-prefix}-red-8'));
  --danger-10: rgb(var(~'--{arco-cssvars-prefix}-red-10'));
  --dark-link-6: rgb(34, 113, 225);
  --dark-danger-10: rgb(var(~'--{arco-cssvars-prefix}-red-10'));
  --warning-10: rgb(var(~'--{arco-cssvars-prefix}-orange-10'));
  --dark-link-10: rgb(232, 245, 255);
  --dark-blue-10: #EAF8FF;
  --dark-warning-8: rgb(var(~'--{arco-cssvars-prefix}-orange-8'));
  --dark-gold-4: #CC961F;
  --dark-success-9: rgb(var(~'--{arco-cssvars-prefix}-green-9'));
  --dark-link-9: rgb(178, 214, 247);
  --dark-success-8: rgb(var(~'--{arco-cssvars-prefix}-green-8'));
  --danger-2: rgb(var(~'--{arco-cssvars-prefix}-red-2'));
  --dark-link-5: rgb(26, 98, 217);
  --dark-danger-1: rgb(var(~'--{arco-cssvars-prefix}-red-1'));
  --orange-4: #FFB65D;
  --dark-color-text-1: rgba(255, 255, 255, 0.9);
  --color-text-1: var(~'--{arco-cssvars-prefix}-color-neutral-10');
  --color-bg-white: #ffffff;
  --color-white: #ffffff;
  --dark-color-text-2: rgba(255, 255, 255, 0.7);
  --dark-color-white: rgba(255, 255, 255, 0.9);
  --color-text-3: var(~'--{arco-cssvars-prefix}-color-neutral-6');
  --color-text-2: var(~'--{arco-cssvars-prefix}-color-neutral-8');
  --color-text-4: var(~'--{arco-cssvars-prefix}-color-neutral-4');
  --dark-color-text-3: rgba(255, 255, 255, 0.5);
  --color-border-2: var(~'--{arco-cssvars-prefix}-color-neutral-3');
  --color-fill-4: var(~'--{arco-cssvars-prefix}-color-neutral-4');
  --color-fill-2: var(~'--{arco-cssvars-prefix}-color-neutral-2');
  --color-fill-3: var(~'--{arco-cssvars-prefix}-color-neutral-3');
  --dark-color-bg-1: #17171A;
  --dark-color-fill-4: rgba(255, 255, 255, 0.16);
  --dark-color-bg-white: #f6f6f6;
  --dark-color-border-1: --dark-gray-2;
  --color-bg-1: #ffffff;
  --color-border-1: var(~'--{arco-cssvars-prefix}-color-neutral-2');
  --dark-color-border-2: --dark-gray-3;
  --color-fill-1: var(~'--{arco-cssvars-prefix}-color-neutral-1');
  --dark-color-border-4: --dark-gray-6;
  --dark-color-fill-1: rgba(255, 255, 255, 0.04);
  --color-border-4: var(~'--{arco-cssvars-prefix}-color-neutral-6');
  --dark-color-fill-2: rgba(255, 255, 255, 0.08);
  --dark-color-border-3: --dark-gray-4;
  --dark-color-fill-3: rgba(255, 255, 255, 0.12);
  --color-bg-3: #ffffff;
  --dark-color-bg-2: #232324;
  --dark-color-bg-4: #313132;
  --dark-color-bg-5: #373739;
  --color-bg-5: #ffffff;
  --color-bg-4: #ffffff;
  --dark-color-bg-3: #2a2a2b;
  --color-bg-2: #ffffff;
  --color-border-3: var(~'--{arco-cssvars-prefix}-color-neutral-4');
  --textarea-prefix-cls: ~'--{prefix}-textarea';
  --color-primary-1: rgb(var(~'--{arco-cssvars-prefix}-primary-1'));
  --color-primary-5: rgb(var(~'--{arco-cssvars-prefix}-primary-5'));
  --color-link-10: rgb(var(~'--{arco-cssvars-prefix}-link-10'));
  --color-primary-3: rgb(var(~'--{arco-cssvars-prefix}-primary-3'));
  --color-primary-4: rgb(var(~'--{arco-cssvars-prefix}-primary-4'));
  --color-link-5: rgb(var(~'--{arco-cssvars-prefix}-link-5'));
  --color-danger-2: rgb(var(~'--{arco-cssvars-prefix}-danger-2'));
  --color-link-4: rgb(var(~'--{arco-cssvars-prefix}-link-4'));
  --color-link-8: rgb(var(~'--{arco-cssvars-prefix}-link-8'));
  --color-warning-3: rgb(var(~'--{arco-cssvars-prefix}-warning-3'));
  --color-warning-7: rgb(var(~'--{arco-cssvars-prefix}-warning-7'));
  --color-link-9: rgb(var(~'--{arco-cssvars-prefix}-link-9'));
  --color-primary-2: rgb(var(~'--{arco-cssvars-prefix}-primary-2'));
  --color-danger-10: rgb(var(~'--{arco-cssvars-prefix}-danger-10'));
  --color-success-9: rgb(var(~'--{arco-cssvars-prefix}-success-9'));
  --color-success-8: rgb(var(~'--{arco-cssvars-prefix}-success-8'));
  --color-danger-8: rgb(var(~'--{arco-cssvars-prefix}-danger-8'));
  --color-success-5: rgb(var(~'--{arco-cssvars-prefix}-success-5'));
  --color-warning-8: rgb(var(~'--{arco-cssvars-prefix}-warning-8'));
  --color-success-3: rgb(var(~'--{arco-cssvars-prefix}-success-3'));
  --color-link-6: rgb(var(~'--{arco-cssvars-prefix}-link-6'));
  --color-warning-9: rgb(var(~'--{arco-cssvars-prefix}-warning-9'));
  --color-danger-1: rgb(var(~'--{arco-cssvars-prefix}-danger-1'));
  --color-primary-6: rgb(var(~'--{arco-cssvars-prefix}-primary-6'));
  --color-warning-2: rgb(var(~'--{arco-cssvars-prefix}-warning-2'));
  --color-primary-7: rgb(var(~'--{arco-cssvars-prefix}-primary-7'));
  --color-warning-4: rgb(var(~'--{arco-cssvars-prefix}-warning-4'));
  --color-danger-5: rgb(var(~'--{arco-cssvars-prefix}-danger-5'));
  --color-primary-8: rgb(var(~'--{arco-cssvars-prefix}-primary-8'));
  --color-warning-6: rgb(var(~'--{arco-cssvars-prefix}-warning-6'));
  --color-danger-9: rgb(var(~'--{arco-cssvars-prefix}-danger-9'));
  --color-success-4: rgb(var(~'--{arco-cssvars-prefix}-success-4'));
  --color-success-2: rgb(var(~'--{arco-cssvars-prefix}-success-2'));
  --color-danger-7: rgb(var(~'--{arco-cssvars-prefix}-danger-7'));
  --color-warning-1: rgb(var(~'--{arco-cssvars-prefix}-warning-1'));
  --color-warning-5: rgb(var(~'--{arco-cssvars-prefix}-warning-5'));
  --color-link-3: rgb(var(~'--{arco-cssvars-prefix}-link-3'));
  --color-danger-3: rgb(var(~'--{arco-cssvars-prefix}-danger-3'));
  --color-link-7: rgb(var(~'--{arco-cssvars-prefix}-link-7'));
  --color-primary-9: rgb(var(~'--{arco-cssvars-prefix}-primary-9'));
  --color-link-2: rgb(var(~'--{arco-cssvars-prefix}-link-2'));
  --color-warning-10: rgb(var(~'--{arco-cssvars-prefix}-warning-10'));
  --color-success-7: rgb(var(~'--{arco-cssvars-prefix}-success-7'));
  --color-primary-10: rgb(var(~'--{arco-cssvars-prefix}-primary-10'));
  --color-danger-4: rgb(var(~'--{arco-cssvars-prefix}-danger-4'));
  --color-success-10: rgb(var(~'--{arco-cssvars-prefix}-success-10'));
  --color-link-1: rgb(var(~'--{arco-cssvars-prefix}-link-1'));
  --color-success-1: rgb(var(~'--{arco-cssvars-prefix}-success-1'));
  --color-danger-6: rgb(var(~'--{arco-cssvars-prefix}-danger-6'));
  --color-success-6: rgb(var(~'--{arco-cssvars-prefix}-success-6'));
  --dark-color-menu-dark-hover: var(~'--{arco-cssvars-prefix}-color-fill-2');
  --color-black: #000000;
  --color-primary-light-2: rgb(var(~'--{arco-cssvars-prefix}-primary-2'));
  --color-spin-layer-bg: rgba(255, 255, 255, 0.6);
  --color-tooltip-bg: rgb(var(~'--{arco-cssvars-prefix}-gray-10'));
  --color-primary-light-3: rgb(var(~'--{arco-cssvars-prefix}-primary-3'));
  --dark-color-tooltip-bg: #373739;
  --color-bg-popup: var(~'--{arco-cssvars-prefix}-color-bg-5');
  --color-secondary: var(~'--{arco-cssvars-prefix}-color-neutral-2');
  --dark-mask-color-bg: rgba(23, 23, 26, 0.6);
  --color-primary-light-1: rgb(var(~'--{arco-cssvars-prefix}-primary-1'));
  --dark-color-spin-layer-bg: rgba(51, 51, 51, 0.6);
  --dark-color-black: #000000;
  --color-secondary-hover: var(~'--{arco-cssvars-prefix}-color-neutral-3');
  --color-primary-light-4: rgb(var(~'--{arco-cssvars-prefix}-primary-4'));
  --color-border: rgb(var(~'--{arco-cssvars-prefix}-gray-3'));
  --color-menu-dark-hover: rgba(255, 255, 255, 0.04);
  --mask-color-bg: rgba(29, 33, 41, 0.6);
  --color-secondary-active: var(~'--{arco-cssvars-prefix}-color-neutral-4');
  --color-danger-light-3: rgb(var(~'--{arco-cssvars-prefix}-danger-3'));
  --color-danger-light-1: rgb(var(~'--{arco-cssvars-prefix}-danger-1'));
  --color-secondary-disabled: var(~'--{arco-cssvars-prefix}-color-neutral-1');
  --color-danger-light-2: rgb(var(~'--{arco-cssvars-prefix}-danger-2'));
  --color-success-light-1: rgb(var(~'--{arco-cssvars-prefix}-success-1'));
  --color-danger-light-4: rgb(var(~'--{arco-cssvars-prefix}-danger-4'));
  --color-success-light-2: rgb(var(~'--{arco-cssvars-prefix}-success-2'));
  --color-success-light-4: rgb(var(~'--{arco-cssvars-prefix}-success-4'));
  --color-success-light-3: rgb(var(~'--{arco-cssvars-prefix}-success-3'));
  --color-warning-light-1: rgb(var(~'--{arco-cssvars-prefix}-warning-1'));
  --color-warning-light-3: rgb(var(~'--{arco-cssvars-prefix}-warning-3'));
  --color-warning-light-2: rgb(var(~'--{arco-cssvars-prefix}-warning-2'));
  --dark-color-border: #333335;
  --color-warning-light-4: rgb(var(~'--{arco-cssvars-prefix}-warning-4'));
  --dark-color-primary-light-1: rgba(var(~'--{arco-cssvars-prefix}-primary-6'), 0.2);
  --dark-color-primary-light-4: rgba(var(~'--{arco-cssvars-prefix}-primary-6'), 0.65);
  --dark-color-secondary: rgba(var(~'--{arco-cssvars-prefix}-gray-9'), 0.08);
  --dark-color-primary-light-2: rgba(var(~'--{arco-cssvars-prefix}-primary-6'), 0.35);
  --dark-color-secondary-disabled: rgba(var(~'--{arco-cssvars-prefix}-gray-9'), 0.08);
  --dark-color-secondary-active: rgba(var(~'--{arco-cssvars-prefix}-gray-7'), 0.24);
  --dark-color-secondary-hover: rgba(var(~'--{arco-cssvars-prefix}-gray-8'), 0.16);
  --dark-color-text-4: rgba(255, 255, 255, 0.3);
  --dark-color-primary-light-3: rgba(var(~'--{arco-cssvars-prefix}-primary-6'), 0.5);
  --dark-color-danger-light-1: rgba(var(~'--{arco-cssvars-prefix}-danger-6'), 0.2);
  --dark-color-danger-light-2: rgba(var(~'--{arco-cssvars-prefix}-danger-6'), 0.35);
  --dark-color-danger-light-3: rgba(var(~'--{arco-cssvars-prefix}-danger-6'), 0.5);
  --dark-color-danger-light-4: rgba(var(~'--{arco-cssvars-prefix}-danger-6'), 0.65);
  --dark-color-success-light-1: rgb(var(~'--{arco-cssvars-prefix}-success-6'), 0.2);
  --dark-color-success-light-2: rgb(var(~'--{arco-cssvars-prefix}-success-6'), 0.35);
  --dark-color-success-light-3: rgb(var(~'--{arco-cssvars-prefix}-success-6'), 0.5);
  --dark-color-success-light-4: rgb(var(~'--{arco-cssvars-prefix}-success-6'), 0.65);
  --dark-color-warning-light-1: rgb(var(~'--{arco-cssvars-prefix}-warning-6'), 0.2);
  --dark-color-warning-light-2: rgb(var(~'--{arco-cssvars-prefix}-warning-6'), 0.35);
  --dark-color-warning-light-3: rgb(var(~'--{arco-cssvars-prefix}-warning-6'), 0.5);
  --dark-color-warning-light-4: rgb(var(~'--{arco-cssvars-prefix}-warning-6'), 0.65);
  --color-link-light-1: rgb(var(~'--{arco-cssvars-prefix}-link-1'));
  --color-link-light-2: rgb(var(~'--{arco-cssvars-prefix}-link-2'));
  --color-link-light-3: rgb(var(~'--{arco-cssvars-prefix}-link-3'));
  --color-link-light-4: rgb(var(~'--{arco-cssvars-prefix}-link-4'));
  --dark-color-link-light-1: rgba(var(~'--{arco-cssvars-prefix}-link-6'), 0.2);
  --dark-color-link-light-2: rgba(var(~'--{arco-cssvars-prefix}-link-6'), 0.35);
  --dark-color-link-light-3: rgba(var(~'--{arco-cssvars-prefix}-link-6'), 0.5);
  --dark-color-link-light-4: rgba(var(~'--{arco-cssvars-prefix}-link-6'), 0.65);
  --opacity-none: 0;
  --opacity-1: 10%;
  --opacity-2: 20%;
  --opacity-3: 30%;
  --opacity-4: 40%;
  --opacity-5: 50%;
  --opacity-6: 60%;
  --opacity-7: 70%;
  --opacity-8: 80%;
  --opacity-9: 90%;
  --opacity-10: 100%;
  --mask-bg-opacity: 60%;
  --transition-duration-1: 0.1s;
  --transition-duration-2: 0.2s;
  --transition-duration-3: 0.3s;
  --transition-duration-4: 0.4s;
  --transition-duration-5: 0.5s;
  --transition-duration-loading: 1s;
  --transition-timing-function-linear: cubic-bezier(0, 0, 1, 1);
  --transition-timing-function-standard: cubic-bezier(0.34, 0.69, 0.1, 1);
  --transition-timing-function-overshoot: cubic-bezier(0.3, 1.3, 0.3, 1);
  --transition-timing-function-decelerate: cubic-bezier(0.4, 0.8, 0.74, 1);
  --transition-timing-function-accelerate: cubic-bezier(0.26, 0, 0.6, 0.2);
  --icon-hover-border-radius: var(~'--{arco-cssvars-prefix}-border-radius-circle');
  --icon-hover-color-bg: var(~'--{arco-cssvars-prefix}-color-fill-2');
  --icon-hover-size-default-height: 20px;
  --icon-hover-size-small-height: 20px;
  --icon-hover-size-mini-height: 20px;
  --icon-hover-size-large-height: 24px;
  --icon-hover-size-huge-height: 24px;
  --icon-hover-size-small-icon: 12px;
  --icon-hover-size-mini-icon: 12px;
  --icon-hover-size-default-icon: 12px;
  --icon-hover-size-large-icon: 12px;
  --icon-hover-size-huge-icon: 12px;
  --radius-none: var(~'--{arco-cssvars-prefix}-border-radius-none');
  --radius-small: var(~'--{arco-cssvars-prefix}-border-radius-small');
  --radius-medium: var(~'--{arco-cssvars-prefix}-border-radius-medium');
  --radius-large: var(~'--{arco-cssvars-prefix}-border-radius-large');
  --radius-circle: var(~'--{arco-cssvars-prefix}-border-radius-circle');
  --code-family: Consolas, Menlo;
  --z-index-popup-base: 1000;
  --z-index-affix: 999;
  --z-index-popup: 1000;
  --z-index-drawer: 1001;
  --z-index-modal: 1001;
  --z-index-message: 1003;
  --z-index-notification: 1003;
  --z-index-image-preview: 1001;
  --line-height-base: 1.5715;
  --popup-box-shadow-base: 0 2px 5px rgba(0, 0, 0, 0.1);
  --color-transparent: transparent;
  --prefix: arco;
  --badge-prefix-cls: ~'--{prefix}-badge';
  --calendar-prefix-cls: ~'--{prefix}-calendar';
  --rate-prefix-cls: ~'--{prefix}-rate';
  --font-size-body: --font-size-body-3;
  --size-none: 0;
  --border-4: 4px;
  --border-5: 5px;
  --arco-theme-tag: body;
  --skeleton-prefix-cls: ~'--{prefix}-skeleton';
  --arco-vars-prefix: ~'';
  --arco-cssvars-prefix: if(--arco-vars-prefix = ~'', -, ~'----{arco-vars-prefix}');
  --watermark-prefix-cls: ~'--{prefix}-watermark';
  --verification-code-prefix-cls: ~'--{prefix}-verification-code';
  --color-prefix-cls: ~'--{prefix}-color';
  /** 对设计图上存在 但是首次没有的内容进行优化 */
  --primary-color: #0052D9;
  --text-color: #333333;
  --text-h1-color: #3D3D3D;
  --text-span-color: #1A1A1A;
  --text-gray-color: #767676;
  --text-warn-color: #F5660C;
  --warn-bg-color: #FFF5EF;
  --text-sub-color: #666666;
  --text-sub-color-two: #999999;
  --backlog-item-color: #F9FBFF;
  --backlog-item-sub-color: #AAAAAA;
  --evaluation-bg-color: #0052D9;
  --tcm-info-bg-color: #FAFAFA;
  --tcm-info-border-color: #95B4E7;
  --tcm-info-border-color-two: #B2DFDD;
  --evaluate-border-color: #E5E5E5;
  --gray-bg-color: #F2F3F5;
  --border-color: #E8E8E8;
  --border-gray-color: #D8D8D8;
  --notice-color: #CFC54F;
  --border-split-color: #E5E6EB;
  --disabled-bg-color: #EBECEE;
  --disabled-font-color: #AFB2B9;
  --a-package-bgc: #77DB89;
  --b-package-bgc: #ED964F;
  --c-package-bgc: #E77975;
  --d-package-bgc: #394BEA;
  --label-disabled-color: #c9cdd4;
  --progress-line-bg-color: #eeeeee;
  --package-normal-bg-color: #77DC89;
  --package-expire-bg-color: #F4660C;
  // 用于提示文字
  --text-gray-deep-color: #86909C;
  --text-satisfaction-color: #00B42A;
  --satisfaction-bg-color: #00b42a1a;
  --text-dissatisfy-color: #FF9500;
  --dissatisfy-bg-color: #ff95001f;
  --text-outdate-color: #F53F3F;
  --dis-active-bg-color: #F3F3F3;
  --card-item-bg-color: rgba(0, 82, 217, 0.08);
  // 服务包配置中卡片的背景色
  --package-title-bg-color: rgba(0, 82, 217, 0.06);
  --text-enabled-color: #00b42a;
  --text-info-color: rgba(0, 0, 0, 0.3);
  --text-upload-btn-info-text-color: rgba(134, 144, 156, 0.5);
  --text-upload-btn-delete-icon-bg-color: rgba(0, 0, 0, 0.4);
  --text-notice-title-text-color: #1D2129;

  // 展示提示到期时间的文案颜色
  --text-expired-time-text-color: #FF9900;
  --text-hit-color: rgba(0, 82, 217, 0.5);
  --item-hit-bg-color: rgba(0, 82, 217, 0.06);

  --intro-bg-color: rgba(0, 82, 217, 0.04);
}