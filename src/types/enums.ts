/*
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-10-17 10:54:09
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-05-13 15:31:15
 */
// export enum ETemplateCode {
//   LOGIN = 'HEALTH_ACCOUNT_LOGIN',
// }

import { color } from "echarts"

/**
 * 人群类型
 * PotentialPopulation 1：潜在人群
 * ServicePopulation 2：服务人群
 */
const enum ECrowdType {
  PotentialPopulation = '1',
  ServicePopulation = '2'
}

/**
 * 是 / 否
 * Is：1 是
 * Not：0 否
 */
const enum EIsOrNotOne {
  Is = '1',
  Not = '0'
}

/**
 * 是 / 否
 * Is：1 是
 * Not：2 否
 */
const enum EIsOrNotTwo {
  Is = '1',
  Not = '2'
}

/**
 * 性别对应的字段
 * 1 男
 * 2 女
 */
enum EGenderType {
  Man = 1,
  Woman = 2
}

/**
 * 当前任务的执行状态
 */
const enum EExecuteType  {
  // 进行中
  Progress = 2,
  // 已完成
  Finish = 3,
}

/**
 * 报告类型
 */
const enum EReportType  {
  // 检验报告
  surveyCount = '1',
  // 检查报告
  inspectionCount = '2',
  // 病历
  caseCount = '3',
  // 检查报告
  examCount = '4',
  // 健康建议书
  proposalCount = '5',
}

/**
 * 缓存
 */
const enum EStorage {
  DIAGNOSIS_INIT = 'diagnosisInit',
}

export const enum EPackageCode {
  // 高血压
  GXY = 'JY_FOLLOW_HBP',
  // 甲状腺结节
  JZXJJ = 'JY_FOLLOW_TN',
  // 混合性高脂血症
  GZXZ = 'JY_FOLLOW_MH',
  // 高尿酸血症
  GNSXZ = 'JY_FOLLOW_HR',
  // 糖尿病
  TNB = 'JY_FOLLOW_DM',
  // 肥胖
  FP = 'JY_FOLLOW_FAT',
  // 冠心病
  CH = 'JY_FOLLOW_CH',
  // 心力衰竭
  HF = 'JY_FOLLOW_HF',
  // 心律失常
  HA = 'JY_FOLLOW_HA',
}

/**
 * 0:停用 1：启用 2:删除 3:暂存
 */
export const enum EPaperStatus {
  /**
   * 停用
   */
  STOP = 0,
  /**
   * 启用
   */
  ENABELE = 1,
  /**
   * 删除
   */
  DELETE = 2,
  /**
   * 暂存
   */
  TSSAVE = 3
}


// 书写状态(0.待完成1.已完成)
export const enum EWriteStatus {
  /**
   * 已完成
   */
  FINISH = 1,
  /**
   * 待完成
   */
  UN_FINISH = 0
}

// 推送状态(0.待推送1.已推送2.推送失败)
export const enum EPushStatus {
  /**
   * 待推送
   */
  WAIT = 0,
  /**
   * 已推送
   */
  FINISH = 1,
  /**
   * 推送失败
   */
  FAILED = 2,
}

/* 保险购买订单状态 1、已下单。2、下单成功。3、已取消  */
export const enum OrderStatus {
  Ordered = '1',
  Confirmation = '2',
  Cancelled = '3'
}

/* 服务预约状态 */
export const ServiceAppointmentStatusMap =  new Map([
  ['01',{color: '#0052D9'}],
  ['02',{color: '#FF5252'}],
  ['03',{color: '#0052D9'}],
  ['04',{color: '#0052D9'}],
  ['05',{color: '#77DC89'}],
  ['06',{color: '#FF5252'}],
  ['07',{color: '#333333'}],
  ['08',{color: '#333333'}],
  ['09',{color: '#333333'}],
  ['10',{color: '#333333'}],
  ['11',{color: '#333333'}],
  ['12',{color: '#333333'}],
  ['13',{color: '#333333'}],
  ['14',{color: '#333333'}],
])
export {
  ECrowdType,
  EIsOrNotOne,
  EIsOrNotTwo,
  EGenderType,
  EExecuteType,
  EReportType,
  EStorage,
}

// 登录失效后端返回的错误码
export const LOGIN_EXPIRED = "1110001"

export const enum EDocType {
  /** 文书 */
  Document = 'doc',
  /** 问卷 */
  Question = 'question',
}