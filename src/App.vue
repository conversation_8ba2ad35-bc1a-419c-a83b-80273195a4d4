<!--
 * @Description: 所有页面的入口文件
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-09-20 15:57:10
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-17 23:38:27
-->
<template>
  <a-config-provider :locale="locale">
    <router-view />
    <global-setting />
    <!-- 顶层抽离 患者360使用的组件 -->
    <global-component />
    <!-- 顶层保险服务预约组件 -->
    <reservation />
    <!-- 顶层服务预约组件 -->
    <serviceAppointment />
    <!-- 顶层服务发送短信 -->
    <sendSMS />
    <!-- 顶层的签约使用的组件 -->
    <signing />
    <!-- 工单创建 -->
    <workOrder />
    <!-- 顶层推送组件 -->
    <send-evaluation />
  </a-config-provider>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import enUS from "@arco-design/web-vue/es/locale/lang/en-us";
import zhCN from "@arco-design/web-vue/es/locale/lang/zh-cn";
import GlobalSetting from "@/components/global-setting/index.vue";
import globalComponent from "@/components/global-component/index.vue";
import reservation from "@/components/global-component/reservation.vue";
import serviceAppointment from "@/components/global-component/service-appointment.vue";
import sendSMS from "@/components/global-component/sendSMS.vue";
import signing from "@/components/global-component/signing/signing.vue";
import useLocale from "@/hooks/locale";
import workOrder from "./components/global-component/work-order/index.vue";
import sendEvaluation from './components/global-component/send-evaluation.vue';

const { currentLocale } = useLocale();
const locale = computed(() => {
  switch (currentLocale.value) {
    case "zh-CN":
      return zhCN;
    case "en-US":
      return enUS;
    default:
      return enUS;
  }
});
</script>
