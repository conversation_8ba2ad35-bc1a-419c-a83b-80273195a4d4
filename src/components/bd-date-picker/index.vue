<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-10-21 10:45:22
 * @LastEditors: yangrongxin
 * @LastEditTime: 2024-10-25 10:27:46
-->
<template>
  <a-date-picker
    :style="{ width: props.width ?? '200px' }"
    @change="handleDateChange"
  />
</template>

<script setup lang="ts">

interface IBdDatePicerProp {
  width?: string
}

const props = withDefaults(
  defineProps<IBdDatePicerProp>(),
  {
    width: '200px'
  }
)

const emits = defineEmits([
  'update:modelValue'
])

const handleDateChange = (
  _value: Date | string | number | undefined,
  _date: Date | undefined,
  _dateString: string | undefined
) => {
  console.log('dateChange', _value, _date, _dateString)
  emits('update:modelValue', _value);
}

</script>

<style lang="scss" scoped>
:deep(.arco-picker-input) {
  border-right: 1px solid #C5C5C5 !important;
}
:deep(.arco-picker .arco-picker-start-time) {
  padding-left: 6px !important;
}
</style>