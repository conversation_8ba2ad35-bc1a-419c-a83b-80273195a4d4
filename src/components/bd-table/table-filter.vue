<!--
 * @Description: 用于控制表格筛选的内容
 * @Author: ya<PERSON><PERSON><PERSON>
 * @Date: 2024-10-18 14:26:55
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 15:58:39
-->
<template>
  <div class="table-filter-wrapper">
    <a-form
      :model="form"
      layout="inline"
      class="table-filter-form"
    >
      <!-- 展示筛选元素的内容 超出之后自动换行 -->
      <div class="table-filter-content">
        <!-- <a-form-item field="teamId" label="团队">
          <bd-select
            v-model="form.teamId"
            placeholder="请选择"
            :request-fn="getUserTeamList"
            :label-field="'name'"
            :value-field="'teamId'"
          />
        </a-form-item> -->
        <a-form-item field="customerSources" label="客户来源">
          <bd-select
            v-model="form.customerSources"
            multiple
            :label-field="'name'"
            :value-field="'categoryId'"
            :enums="targetPopulation.customerLabelSource"
            placeholder="请选择"
            @change="handleCustomerSourceChange"
          />
        </a-form-item>
        <a-form-item field="serviceGroupIds" label="服务包">
          <bd-select
            v-model="form.serviceGroupIds"
            multiple
            :label-field="'name'"
            :value-field="'categoryId'"
            :enums="targetPopulation.filteredServiceGroup"
            placeholder="请选择"
            @change="handleServiceIdsChange"
          />
        </a-form-item>
        <a-form-item field="labelIds" label="患者标签">
          <bd-tree-select
            v-model="form.labelIds"
            :request-fn="getLabelList"
            :label-field="'name'"
            :value-field="'labelId'"
            :children-field="'labels'"
            placeholder="请选择"
          />
        </a-form-item>
        <a-form-item
          v-if="form.serviceGroupIds.length === 1"
          field="expireTimeEnd"
          label="权益到期日期"
        >
          <bd-date-range
            v-model="form.expireTime"
            width="240px"
          />
        </a-form-item>
      </div>
      <a-form-item
        class="keyword-search"
        field="keyword"
      >
        <bd-input
          width="253px"
          field="keyword"
          @search="handleSearch"
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue';
import bdSelect from '@/components/bd-select/index.vue';
import bdDateRange from '@/components/bd-date-range/index.vue';
import bdInput from '@/components/bd-input/index.vue';
import bdTreeSelect from '@/components/bd-tree-select/index.vue';
import { useTargetPopulation } from '@/store';
import {
  getUserTeamList
} from '@/api/user'
import { getLabelList } from '@/api/patients';
import { postBdManageApiPatientLabelGroupList } from '@/batchApi';

const targetPopulation = useTargetPopulation()


console.log('watch_targetPopulation.getState', targetPopulation.getState.customerSource)

const form = reactive<{
  name: string | undefined,
  post: string | undefined,
  keyword: string | undefined,
  // teamId: string | undefined,
  serviceGroupIds: string[],
  customerSources: string[],
  labelIds: string[],
  expireTime: string[]
}>({
  name: undefined,
  post: undefined,
  // 姓名、证件号/电话 搜索
  keyword: undefined,
  // teamId: undefined,
  serviceGroupIds: [],
  customerSources: [],
  labelIds: [],
  expireTime: []
})

// watch(() => form.customerSources, (newFormValue) => {
  
// }, {
//   deep: true
// })

// watch(() => form.serviceGroupIds, (newFormValue) => {
  
// })

watch([
  () => targetPopulation.getState.serviceGroupIds,
  () => targetPopulation.getState.customerSources,
], (
  [
    newServiceGroupIds,
    newCustomerSources
  ]
) => {
  // 选中的分组数据变了 设置变化的服务包的数据
  if (
    Array.isArray(newServiceGroupIds) &&
    (
      JSON.stringify(newServiceGroupIds) !== JSON.stringify(form.serviceGroupIds)
    )
  ) {
    form.serviceGroupIds = newServiceGroupIds as string[]
  }
  // 设置的服务包编码变了 设置选中的服务包的数据
  if (
    Array.isArray(newCustomerSources) &&
    (
      JSON.stringify(newCustomerSources) !== JSON.stringify(form.customerSources)
    )
  ) {
    form.customerSources = newCustomerSources as string[]
  }
  // 如果参数变化了 使用当前的参数进行搜索
  handleSearch();
})

// 通过选中项目的 categoryId 去获取子集中的值
const handleCustomerSourceChange = (newFormValue: any[]) => {
  const customerSources = newFormValue;
  const inlineCustomerSource = targetPopulation.getState.customerSource;
  // 获取对应的树节点
  const selectedCustomerSource = inlineCustomerSource.filter(_fi => {
    return customerSources.includes(_fi.categoryId) 
  })
  // 获取树节点下的子集元素
  const selectedCustomerSourceSubs = selectedCustomerSource.map(_item => _item.subs || [])
  let canSelectServiceIds: string[] = [];
  // 获取子集下面的数据 拿到其中的 categoryId
  if (
    Array.isArray(selectedCustomerSourceSubs) &&
    selectedCustomerSourceSubs.length !== 0
  ) {
    canSelectServiceIds = selectedCustomerSourceSubs
      .reduce((b, a) => (b || []).concat(a))
      .map(item => item.categoryId)
  }
  if (
    (
      JSON.stringify(targetPopulation.getState.customerSources) !== JSON.stringify(customerSources)
    ) ||
    (
      JSON.stringify(targetPopulation.getState.serviceGroupIds) !== JSON.stringify(canSelectServiceIds)
    )
  ) {
    // customerSources 变化的时候 对应的服务包的选中也需要变化
    console.log('inlineCustomerSource', inlineCustomerSource, canSelectServiceIds)
    targetPopulation.changeCustomerSources(customerSources)
    targetPopulation.changeServiceGroupIds(canSelectServiceIds)
  }
}

// 根据服务包的选择进行参数的联动
const handleServiceIdsChange = (newFormValue: any[]) => {
  // TODO: 取消单个服务包的时候 参数没有取消
  console.log('_serviceIds_newFormValue_', newFormValue)
  const selectCustomerSourceSet = new Set()
  newFormValue.forEach(_item => {
    const sourceItem = targetPopulation.getState.serviceGroup.find(_fi => _fi.categoryId === _item)
    if (
      sourceItem?.pid
    ) {
      selectCustomerSourceSet.add(sourceItem?.pid)
    }
  })
  targetPopulation.changeCustomerSources(Array.from(selectCustomerSourceSet) as string[])
  targetPopulation.changeServiceGroupIds(newFormValue)
}


const emits = defineEmits([
  'search'
])

// 提交当前的筛选参数到上层的组件中
const handleSearch = (_params: any = {}) => {
  const {
    expireTime,
    customerSources,
    serviceGroupIds,
    ...otherFormParams
  } = form
  const params: any = {
    ...otherFormParams,
    ..._params
  }
  const _serviceGroupCodes: Set<string> = new Set();
  console.log('search_params', form, _params, customerSources);
  if (
    Array.isArray(expireTime) &&
    expireTime.length === 2
  ) {
    // eslint-disable-next-line prefer-destructuring
    params.expireTimeStart = expireTime[0]
    // eslint-disable-next-line prefer-destructuring
    params.expireTimeEnd = expireTime[1]
  }
  if (
    Array.isArray(serviceGroupIds)
  ) {
    serviceGroupIds.forEach(_item => {
      const _ele = targetPopulation.getState.serviceGroup.find(_fi => _fi.categoryId === _item);
      if (
        // @ts-expect-error add packageId
        _ele && typeof _ele.packageId === 'string' && _ele.packageId !== ''
      ) {
        // @ts-expect-error add packageId
        _serviceGroupCodes.add(_ele.packageId);
      }
    })
  }
  emits('search', {
    ...params,
    servicePackageIds: Array.from(_serviceGroupCodes)
  })
}
</script>

<style lang="scss" scoped>
.table-filter-wrapper {
  width: 100%;
  margin-bottom: var(--spacing-4); 
  background-color: white;
  border-radius: var(--border-radius-medium);
  padding: var(--spacing-7);
  overflow-x: hidden;
  // form的主要内容
  >.table-filter-form {
    display: flex;
    // 左边的筛选元素的所有内容
    >.table-filter-content {
      display: flex;
      flex-wrap: wrap;
      flex: 1;
      gap: 8px;
    }
    div.arco-form-item {
      margin-bottom: 0px;
      margin-right: var(--spacing-7);
      &.keyword-search {
        margin-right: 0px;
        :deep(.arco-form-item-label-col) {
          padding-right: 0px;
        }
      }
    }
  }
}
</style>
