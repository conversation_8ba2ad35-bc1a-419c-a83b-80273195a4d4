<!--
 * @Description: 分页组件
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-10-18 15:56:25
 * @LastEditors: yangrongxin
 * @LastEditTime: 2024-10-30 10:36:21
-->
<template>
  <a-pagination
    :current="props.current ?? 1"
    :total="props.total ?? 0"
    :default-page-size="props.pageSize ?? 15"
    :page-size-options="[15, 30, 50]"
    size="medium"
    show-total
    show-jumper
    show-page-size
    @change="handlePageChange"
    @page-size-change="handlePageSizeChange"
  />
</template>

<script setup lang="ts">

const props = defineProps([
  'total',
  'current',
  'pageSize'
])

const emits = defineEmits([
  'pageChange',
  'pageSizeChange'
])

const handlePageChange = (_current: number) => {
  emits('pageChange', _current)
}

const handlePageSizeChange = (_pageSize: number) => {
  emits('pageSizeChange', _pageSize)
}

</script>

<style scoped>
div {
  color: red;
}
</style>