<!--
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2024-10-18 11:13:53
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 15:51:43
-->
<template>
  <div class="bd-table-wrapper">
    <!-- 展示表格的筛选内容 -->
    <table-filter @search="handleSearch" />
    <div class="bd-table-container">
      <!-- 展示表格主要的内容 -->
      <a-table
        v-model:selected-keys="selectedKeys"
        size="medium"
        row-key="patientId"
        row-class="common-row"
        :data="tableData"
        :row-selection="rowSelection"
        :pagination="false"
        :bordered="false"
        :loading="loading"
        :scroll="{
          minWidth: 1150,
          maxHeight: dynamicMaxHeight,
        }"
        :scrollbar="false"
        tooltip
      >
        <template #tr="{ record }">
          <tr class="my-tr" @dblclick.prevent="handleOpen360(record)" />
        </template>
        <template #columns>
          <a-table-column title="客户来源" :width="140">
            <template #cell="{ record }">
              <!-- 最多只展示两个元素 多的元素 鼠标移入之后进行展示 -->
              <table-tags :hidden-num="2" :items="record.pkgTypes" />
            </template>
          </a-table-column>
          <a-table-column title="姓名" data-index="name" :width="150">
            <template #cell="{ record }">
              <user-info :record="record" />
            </template>
          </a-table-column>
          <a-table-column
            title="年龄"
            data-index="age"
            :width="65"
          ></a-table-column>
          <a-table-column title="性别" :width="65">
            <template #cell="{ record }">
              <span>{{ record.gender === EGenderType.Man ? "男" : "女" }}</span>
            </template>
          </a-table-column>
          <!-- <a-table-column title="患者端" data-index="weComeUrl"></a-table-column> -->
          <a-table-column
            title="联系方式"
            data-index="contactMobile"
            :width="120"
          >
            <template #cell="{ record }">
              <span>{{ SensitivePhoneDel(record.contactMobile) }}</span>
            </template>
          </a-table-column>
          <a-table-column title="重点随访队列" :width="175">
            <template #cell="{ record }">
              <div class="name-disease">
                <span
                  v-for="_disease in record.diseases?.filter(
                    (_fi: any) => _fi.isEnabled,
                  )"
                  :key="_disease.code"
                  class="disease-item"
                  :style="{ backgroundColor: `#${_disease.color}` }"
                >
                  {{ _disease.briefName }}
                </span>
              </div>
            </template>
          </a-table-column>
          <!-- <a-table-column title="影响因素">
            <template #cell="{ record }">
              <div class="influence-factor">
                <span>0</span>
                <span>-</span>
                <span>{{ record?.pkgTypes?.includes('FD') ? 0 : record.influenceFactors.length }}</span>
                <span>{{ record?.pkgTypes?.includes('FD') ? '-' : record.influenceFactors?.join(',') || '-' }}</span>
              </div>
            </template>
          </a-table-column> -->
          <a-table-column
            v-if="tableParams?.servicePackageIds?.length !== 0"
            title="权益发放日期"
            :width="120"
          >
            <template #cell="{ record }">
              <span>{{ record.contractStart || "-" }}</span>
            </template>
          </a-table-column>
          <a-table-column
            v-if="tableParams?.servicePackageIds?.length !== 0"
            title="权益到期"
            :width="120"
          >
            <template #cell="{ record }">
              <span>{{ record.contractEnd || "-" }}</span>
            </template>
          </a-table-column>
        </template>
      </a-table>
      <!-- 展示表格的分页数据 -->
      <div class="footer">
        <div :style="{ display: 'flex', columnGap: '16px' }">
          <a-button
            type="primary"
            :disabled="!selectedKeys?.length"
            @click="handleSending"
          >
            <template #icon>
              <icon-schedule />
            </template>
            发送短信
          </a-button>
          <a-button
            type="primary"
            :disabled="!selectedKeys?.length"
            @click="handleSendingEvaluation"
          >
            <template #icon>
              <icon-schedule />
            </template>
            推送问卷
          </a-button>
        </div>
        <table-pagination
          :current="pagination.current"
          :total="pagination.total"
          :page-size="pagination.pageSize"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js";
import { onBeforeUnmount, onMounted, reactive, ref, watch } from "vue";
import { IPatientResponse, queryPatientList } from "@/api/patients";
import { EGenderType } from "@/types/enums";
import { TableRowSelection } from "@arco-design/web-vue";
import useAppStore from "@/store/modules/app";
import { SensitivePhoneDel } from "@/utils/common";
import { useQuickAccessStore, useTargetPopulation } from "@/store";
import {
  postBdManageApiPatientQueryPatientByParamsPage,
  QueryPatientByParamsPageUsingPOSTPatientInfoVo,
} from "@/batchApi";
import { IQuickAccessState } from "@/store/modules/quick-access/types";
import userInfo from "./components/user-info.vue";
import tableTags from "../table-tags/index.vue";
import tableFilter from "./table-filter.vue";
import tablePagination from "./table-pagination.vue";

const appStore = useAppStore();
const targetPopulation = useTargetPopulation();
const quickAccessStore = useQuickAccessStore();

const selectedKeys = ref<string[]>([]);
const rowSelection = reactive<TableRowSelection>({
  type: "checkbox",
  showCheckedAll: true,
  onlyCurrent: false,
});

// 存储表格分页信息
const pagination = reactive({
  current: 1,
  total: 0,
  pageSize: 15,
});

// 用于存储当前的表格搜索条件
const tableParams = ref<any>({});

const tableData = ref<QueryPatientByParamsPageUsingPOSTPatientInfoVo[]>([]);
const loading = ref(false);
// 计算当前表格的静态高度
const dynamicMaxHeight = ref();

const handleOpen360 = (_item: IPatientResponse) => {
  console.log("handleOpen360", _item);
  // 向患者360的页面注入当前患者的数据
  appStore.openPatientCenter(_item);
};

const handleSending = () => {
  const result = tableData?.value
    ?.filter(item =>
      selectedKeys.value?.includes(item?.patientId as unknown as string),
    )
    ?.map(item => ({
      patientId: item?.patientId,
      phone: item?.contactMobile,
    })) as any;
  quickAccessStore.toggleSendSMS(true, result);
};

const handleSendingEvaluation = () => {
  quickAccessStore.toggleSendEvaluation(true, selectedKeys.value);
};

const init = async (_packages?: string[]) => {
  if (Array.isArray(_packages) && _packages.length !== 0) {
    // @ts-ignore
    tableParams.value.serviceGroupIds = _packages;
  }
  console.log("tableParams.value", tableParams.value);
  const params: any = {
    isRegistered: 1,
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
    query: {
      // @ts-expect-error keyword参数处理
      params: tableParams.value?.keyword,
      ...tableParams.value
    },
  };
  console.log("params", params);
  loading.value = true;
  // const [err, res] = await to(queryPatientList(params))
  const [err, res] = await to(
    postBdManageApiPatientQueryPatientByParamsPage(params),
  );
  loading.value = false;
  if (err) {
    throw err;
  }
  if (Array.isArray(res.data?.content)) {
    tableData.value = res.data.content;
  }
  pagination.total = res.data?.total ?? 0;
  pagination.current = res.data?.pageNum ?? 1;

  console.log("table_data_res", res);
};

const handlePageChange = async (_current: number) => {
  pagination.current = _current;
  await init();
  console.log("_current", _current);
};

const handlePageSizeChange = async (_pageSize: number) => {
  pagination.pageSize = _pageSize;
  await init();
};

// 根据传入的参数 进行筛选
const handleSearch = async (_values: any) => {
  pagination.current = 1;
  tableParams.value = _values ?? {};
  await init();
  console.log("_values", _values);
};

// 在页面变化的时候 调用更新的方法 更新滚动区域的内容
const updateHeight = () => {
  const filterHeight =
    document.querySelector(".table-filter-wrapper")?.clientHeight ?? 0;
  const scrollHeight = `${window.innerHeight - filterHeight - 58 - 64 - 58}px`;
  dynamicMaxHeight.value = scrollHeight;
};

watch(
  () => targetPopulation.getState.customerSource,
  customerSource => {
    if (Array.isArray(customerSource)) {
      // 默认展示全部的时候 获取全部的数据
      const groupIds: string[] = [];
      let packageIds: string[] = [];
      customerSource.forEach(_item => {
        groupIds.push(_item.categoryId);
        packageIds = packageIds.concat(_item.subs.map(_fi => _fi.categoryId));
      });
      init(packageIds);
    }
  },
);

onMounted(async () => {
  updateHeight();
  window.addEventListener("resize", updateHeight);
  // 获取基础的数据
  // await init()
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", updateHeight);
});
</script>

<style lang="scss" scoped>
.bd-table-wrapper {
  display: flex;
  flex-direction: column;
  align-items: baseline;
  justify-content: start;
  height: 100%;
  overflow-y: hidden;
  // min-width: 1474px;
  // 表格的主要内容
  .bd-table-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    
    border-radius: var(--border-radius-medium);
    padding: var(--spacing-7);
    background-color: white;
    width: 100%;
    flex: 1;
    overflow-y: auto;
    > .arco-pagination {
      margin-top: var(--spacing-7);
      justify-content: end;
    }

    .footer {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
    }
  }
}
// 表格中自定义元素的样式 -- 初版的样式 已经废弃
// :class="{ 'source-item': true, [_pkg.toUpperCase()]: true }"
// .source-item {
//   display: inline-block;
//   width: 20px;
//   height: 20px;
//   text-align: center;
//   line-height: 20px;
//   border-radius: 50%;
//   color: white;
//   font-size: var(--font-size-caption);
//   position: relative;
//   // 偏移量处理
//   &:nth-of-type(2) {
//     left: -4px;
//   }
//   &:nth-of-type(3) {
//     left: -8px;
//   }
//   &:nth-of-type(4) {
//     left: -12px;
//   }
//   &.A {
//     background-color: var(--a-package-bgc);
//   }
//   &.B {
//     background-color: var(--b-package-bgc);
//   }
//   &.C {
//     background-color: var(--c-package-bgc);
//   }
//   &.D {
//     background-color: var(--d-package-bgc);
//   }
// }
.disease-item {
  display: inline-block;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  border-radius: 50%;
  color: white;
  font-size: var(--font-size-caption);
  margin-right: var(--spacing-2);
  &:last-child {
    margin-right: 0px !important;
  }
}
.influence-factor {
  display: flex;
  align-items: center;
  > span {
    &:first-child {
      width: 20px;
      height: 20px;
      text-align: center;
      line-height: 20px;
      border-radius: 50%;
      color: white;
      font-size: var(--font-size-caption);
      background-color: var(--notice-color);
    }
    &:last-child {
      flex: 1;
      margin-left: var(--spacing-4);
      font-size: var(--font-size-body-3);
    }
  }
}

.name-disease {
  display: flex;
  justify-content: end;
}
</style>
