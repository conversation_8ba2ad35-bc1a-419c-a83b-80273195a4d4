<!--
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2025-03-31 11:40:31
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-04-24 15:59:06
-->
<template>
  <div class="name-item">
    <img :src="record.gender === EGenderType.Man ? ManPng : WomanPng" alt="">
    <a-trigger 
      position="bottom"
      auto-fit-position
      :unmount-on-close="false"
      :popup-translate="[20, 20]"
    >
      <span>{{ record.name }}</span>
      <template #content>
        <span class="name-item-desc">{{ record.name }}</span>
      </template>
    </a-trigger>
  </div>
</template>

<script setup lang="ts">
import { EGenderType } from '@/types/enums';
import ManPng from '@/assets/images/peopleMan.png'
import WomanPng from '@/assets/images/peopleGirl.png'

const {
  record
} = defineProps([
  'record'
])

</script>

<style lang="scss" scoped>
.name-item {
  display: flex;
  align-items: center;
  >img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: var(--spacing-4);
  }
  // 展示具体的名称
  // 整体展示的内容是 150的时候 内部的名称 是100
  >span {
    cursor: pointer;
    text-decoration: underline;
    color: var(--primary-color);
    display: inline-block;
    width: 100px;
    white-space: nowrap; /* 确保文本不换行 */
    overflow: hidden; /* 超出部分隐藏 */
    text-overflow: ellipsis;
  }
}
.name-item-desc {
  padding: 8px;
  border-radius: 4px;
  background-color: white;
  box-shadow: 2px 2px 5px #ddd;
}
</style>