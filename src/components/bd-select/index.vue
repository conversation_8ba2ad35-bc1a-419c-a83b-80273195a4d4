<!--
 * @Description: 自定义的select下拉组件
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-10-21 10:32:24
 * @LastEditors: yangrongxin
 * @LastEditTime: 2024-11-21 14:32:52
-->
<template>
  <a-select
    class="select-container"
    :style="{
      width: props.width,
      borderRadius: '4px',
    }"
    :placeholder="props.placeholder"
    :multiple="props.multiple"
    :max-tag-count="1"
    allow-clear
    @change="handleSelectChange"
  >
    <a-option v-if="!props.multiple" value="">全部</a-option>
    <a-option v-for="_item, _index in options" :key="_index" :value="_item.value">{{ _item.label }}</a-option>
    <template #label="{ data }">
      <span>{{ data.label.length > 2 ? data.label.slice(0, 2) + '...' : data.label }}</span>
    </template>
  </a-select>
</template>

<script setup>

</script>

<script setup lang="ts">
import to from 'await-to-js';
import { onMounted, onUpdated, ref } from 'vue'

export interface IBdSelectProps {
  value?: string | string[]
  width?: string
  placeholder?: string
  requestFn?: any
  requestParams?: any
  dataField?: string
  labelField?: string
  valueField?: string
  multiple?: boolean
  enums?: any
}

const props = withDefaults(
  defineProps<IBdSelectProps>(),
  {
    value: '',
    width: '154px',
    placeholder: '',
    requestFn: undefined,
    requestParams: undefined,
    dataField: 'data',
    labelField: 'label',
    valueField: 'value',
    multiple: false,
    enums: [],
  }
)

const emits = defineEmits([
  'update:modelValue',
  'change'
])

console.log('props', props.value)

const options = ref<any[]>([])

const handleSelectChange = (value: any) => {
  emits('update:modelValue', value);
  emits('change', value);
}

const init = async () => {
  if (
    typeof props.requestFn === 'function'
  ) {
    console.log('props.requestFn', props.requestFn)
    const [err, res] = await to<any>(props.requestFn(props.requestParams));
    if (
      err
    ) {
      throw err;
    }
    const resArray = res.data[props.dataField ?? 'content']
    if (
      Array.isArray(resArray)
    ) {
      options.value = resArray.map(_item => {
        return {
          label: _item[props.labelField],
          value: _item[props.valueField],
        }
      })
    }
    console.log('res', res, resArray);
  } else if (
    Array.isArray(props.enums)
  ) {
    options.value = props.enums.map(_item => {
      return {
        label: _item[props.labelField],
        value: _item[props.valueField],
      }
    })
  }
}

onMounted(async () => {
  await init()
})

onUpdated(async () => {
  await init()
})

</script>


<style lang="scss">

.select-container {
  text-align: left;
  :deep(.arco-select-view-has-suffix) {
    padding-left: 4px !important;
  }
  &.arco-select-view-multiple {
    .arco-select-view-suffix {
      padding-left: 0px !important;
    }
  }
}
</style>