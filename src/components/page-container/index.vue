<script setup lang="ts">
import { computed, useSlots } from "vue";

type TitleProps = {
  title?: string;
  titleFontSize?: number
};

defineProps<TitleProps>();
const slots = useSlots();
const titleSlot = computed(() => !!slots.title);
</script>

<template>
  <div class="pageContentView">
    <!-- title -->
    <div v-if="titleSlot || title" class="titleView">
      <div
        v-if="!titleSlot"
        class="titleStyle"
        :style="{
          fontSize: `${titleFontSize ?? 18}px`
        }"
      >
        {{ title }}
      </div>
      <div v-else class="w-full">
        <slot name="title"></slot>
      </div>
      <slot name="titleExtra"></slot>
    </div>
    <!-- 内容 -->
    <div class="content" :class="(titleSlot || title) && 'hasTitleContent'">
      <slot></slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.pageContentView {
  width: 100%;
  // height: 100%;
  padding: 16px 16px;
  border-radius: 4px;
  background: #fff;
  display: flex;
  flex-direction: column;

  .titleView {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .titleStyle {
      color: #333;
      font-size: 18px;
      font-weight: bold;
      text-align: left;
    }
  }

  .content {
    height: 100%;
  }

  .hasTitleContent {
    height: calc(100% - 37px);
  }
}
</style>
