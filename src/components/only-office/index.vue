<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2025-06-28 17:10:21
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-02 14:13:44
-->
<template>
  <DocumentEditor
    v-if="props.url"
    id="docEditor" 
    documentServerUrl="http://onlyoffice.dev.hxgy.com/"
    :config="config"
    :key="props.url"
    :events_onDocumentReady="onDocumentReady"
    :onLoadComponentError="onLoadComponentError"
  /> 
</template>

<script setup lang="ts">
import { DocumentEditor } from "@onlyoffice/document-editor-vue";
import { computed, useId } from 'vue';
import { EFileType } from '../../views/patient-center/components/file-upload/content.vue';

// const config = {
//   document: {
//       fileType: "docx",
//       key: "Khirz6zTPdfd7",
//       title: "Example Document Title.docx",
//       // url: "https://example.com/url-to-example-document.docx"
//       url: "https://cdnhyt.cd120.com/health-assets/resource/%E8%B4%A2%E6%99%BA%E4%B8%AD%E5%BF%83%E9%9D%9E%E6%9C%BA%E5%8A%A8%E8%BD%A6%E4%B8%AA%E4%BA%BA%E8%BD%A6%E8%BE%86%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%9D%A8%E8%8D%A3%E9%91%AB.doc"
//   },
//   documentType: "word",
//   // editorConfig: {
//   //     callbackUrl: "https://example.com/url-to-callback.ashx"
//   // }
// }
const props = defineProps<{
  url: string,
  type: string
  title: string
}>()

const config = computed(() => {
  // 生成当前元素的唯一id
  const _uuid = useId();
  console.log('_uuid', _uuid)
  const documentType = getDocumentTypeWithType(props.type as EFileType)
  const _config = {
    document: {
      fileType: props.type,
      key: _uuid,
      title: props.title,
      url: props.url
      // url: "http://192.168.168.154:9000/medical-bd/1940012181078753280.pptx"
      // url: "https://cdn.cd120.info/health-assets/resource/1940012181078753280.pptx"
      // url: "http://192.168.168.154:9000/medical-bd/1940011123577274369.pdf"
      // fileType: props.type ?? EFileType.DOCX,
      // key: "Khirz6zTPdfd7",
      // title: props.title ?? "-",
      // url: props.url ?? "https://cdnhyt.cd120.com/health-assets/resource/%E8%B4%A2%E6%99%BA%E4%B8%AD%E5%BF%83%E9%9D%9E%E6%9C%BA%E5%8A%A8%E8%BD%A6%E4%B8%AA%E4%BA%BA%E8%BD%A6%E8%BE%86%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%9D%A8%E8%8D%A3%E9%91%AB.doc"
    },
    // 当前文档的类型
    documentType,
    token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkb2N1bWVudCI6eyJmaWxlVHlwZSI6ImRvY3giLCJrZXkiOiJLaGlyejZ6VFBkZmQ3IiwidGl0bGUiOiJFeGFtcGxlIERvY3VtZW50IFRpdGxlLmRvY3giLCJ1cmwiOiJodHRwczovL2V4YW1wbGUuY29tL3VybC10by1leGFtcGxlLWRvY3VtZW50LmRvY3gifSwiZG9jdW1lbnRUeXBlIjoid29yZCJ9.7IpEJxdOvBQ0kJ8l6ZegIV4tX5vsPbZZCDDVmcFROXc",
    editorConfig: {
      mode: "view", // 关键参数：强制只读预览
      lang: "zh-CN",
      customization: {
          autosave: false,
          comments: false,
          download: false, // 隐藏下载按钮
          editRights: false, // 禁止编辑权限
          print: false,     // 隐藏打印按钮
          toolbar: false,   // 完全隐藏工具栏
      }
    }
  }
  console.log('_config', _config)
  return _config;
  // return {
  //   document: {
  //     fileType: props.type,
  //     key: "Khirz6zTPdfd7",
  //     title: props.title,
  //     url: props.url
  //     // fileType: props.type ?? EFileType.DOCX,
  //     // key: "Khirz6zTPdfd7",
  //     // title: props.title ?? "-",
  //     // url: props.url ?? "https://cdnhyt.cd120.com/health-assets/resource/%E8%B4%A2%E6%99%BA%E4%B8%AD%E5%BF%83%E9%9D%9E%E6%9C%BA%E5%8A%A8%E8%BD%A6%E4%B8%AA%E4%BA%BA%E8%BD%A6%E8%BE%86%E7%94%B3%E8%AF%B7%E8%A1%A8-%E6%9D%A8%E8%8D%A3%E9%91%AB.doc"
  //   },
  //   documentType: "word", // 可选: text, spreadsheet, presentation
  //   editorConfig: {
  //     mode: "view", // 关键参数：强制只读预览
  //     lang: "zh-CN",
  //     customization: {
  //         autosave: false,
  //         comments: false,
  //         download: false, // 隐藏下载按钮
  //         editRights: false, // 禁止编辑权限
  //         print: false,     // 隐藏打印按钮
  //         toolbar: false,   // 完全隐藏工具栏
  //     }
  //   }
  // }
})

// 根据文件的类型 获取 documentType 字段
const getDocumentTypeWithType = (_type: EFileType) => {
  let documentType = '';
  switch (_type) {
    // 展示文档的信息
    case EFileType.DOC:
      case EFileType.DOCX: documentType = "word";break;
    // 展示表格的信息
    case EFileType.XLS:
      case EFileType.XLSX: documentType = "cell";break;
    // 展示ppt的信息
    case EFileType.PPT:
      case EFileType.PPTX: documentType = "slide";break;
    default: documentType = "word";break;
  }
  return documentType;
}

const onDocumentReady = () => {
  console.log("Document is loaded");
}

const onLoadComponentError = (errorCode: number, errorDescription: string) => {
  switch (errorCode) {
    case -1: // Unknown error loading component
        console.log(errorDescription);
        break;

    case -2: // Error load DocsAPI from http://documentserver/
        console.log(errorDescription);
        break;

    case -3: // DocsAPI is not defined
        console.log(errorDescription);
        break;
    default:;
  }
}

</script>

<style scoped>

</style>