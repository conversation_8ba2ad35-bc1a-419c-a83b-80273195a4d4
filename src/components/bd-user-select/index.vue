<!--
 * @Description: 选中用户进行展示的组件
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-12-18 09:40:05
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-08 09:16:48
-->
<template>
  <div
    class="bd-user-select-container"
    :class="{ disabled: _disabled }"
  >
    <a-trigger
      v-if="!_disabled"
      :popup-visible="resultVisible"
      trigger="click"
      show-arrow
    >
      <a-input-search
        v-model="searchValue"
        :style="{ width:'100%' }"
        placeholder="请输入患者名称进行搜索"
        search-button
        @search="handleSearch"
        @press-enter="handleSearch"
      />
      <template #content>
        <!-- 用于展示获取到的表格数据 -->
        <a-table
          :loading
          :columns="columns"
          :data="tableData"
          :pagination="false"
          @row-dblclick="handleTableDbClick"
        />
      </template>
    </a-trigger>
    <!-- 用于站位的空白元素 -->
    <span v-else></span>
    <!-- 展示选中的患者数据 -->
    <slot
      :data="patientInfo"
    >
      <div
        v-if="patientInfo?.name"
        class="patientSelectContainer"
      >
        <span>{{ patientInfo.name }}</span>
        <span class="line"></span>
        <span>{{ String(patientInfo.gender) === '1' ? '男' : '女' }}</span>
        <span class="line"></span>
        <span>{{ patientInfo.age }}</span>
        <span class="line"></span>
        <span>{{ patientInfo.contactMobile }}</span>
        <span class="line"></span>
        <span>{{ patientInfo.idCard }}</span>
      </div>
    </slot>
  </div>
</template>


<script setup lang="ts">
import to from 'await-to-js';
import { useFormItem } from '@arco-design/web-vue';
import { nextTick, onMounted, ref, watch } from 'vue';
import { queryPatientList } from '@/api/patients';
import { postBdManageApiPatientQueryPatientByParams } from '@/batchApi';

const searchValue = ref();
const resultVisible = ref(false)
const patientInfo = ref<TableData>();
const tableData = ref<TableData[]>([]);
const loading = ref(false);

const columns = [
  {
    title: '姓名',
    dataIndex: 'name',
  },
  {
    width: 65,
    title: '性别',
    dataIndex: 'gender',
    render: ({ record }: { record: any }) => {
      return record.gender === 1 ? '男' : '女'
    }
  },
  {
    width: 65,
    title: '年龄',
    dataIndex: 'age',
  },
  {
    width: 125,
    title: '手机号',
    dataIndex: 'contactMobile',
  },
  {
    width: 190,
    title: '证件号',
    dataIndex: 'idCard',
  }
];

const {
  mergedDisabled,
  eventHandlers
} = useFormItem();

const {
  modelValue,
  defaultPatientId,
  disabled
} = defineProps(['modelValue', 'defaultPatientId', 'disabled'])

const _disabled = mergedDisabled.value ?? disabled;


const emits = defineEmits([
  'update:modelValue'
])

// 双击表格行的时候 触发的方法
const handleTableDbClick = (record: TableData, ev: Event) => {
  console.log('_row_data', record, ev, resultVisible.value);
  resultVisible.value = false
  patientInfo.value = record;
  searchValue.value = undefined;
  // 设置当前选中的数据
  handleInput()
}

// 点击搜索的时候 获取对应的数据
const handleSearch = () => {
  if (
    typeof searchValue.value === 'string' &&
    searchValue.value !== ''
  ) {
    resultVisible.value = true
    getPatientList(searchValue.value, undefined)
  }
}

// 设置当前选中的患者数据
const handleInput = () => {
  const ev = {
    target: {
      value: patientInfo.value
    }
  }
  emits('update:modelValue', ev.target.value)
  eventHandlers.value?.onChange?.(ev as unknown as InputEvent)
}

// 初始获取患者的数据
const getPatientList = async (
  _keyword = undefined,
  _patientId = undefined
) => {
  loading.value = true
  const [err, res] = await to(postBdManageApiPatientQueryPatientByParams({
    params: _keyword,
    patientId: _patientId,
  }))
  loading.value = false
  if (
    err
  ) {
    throw err;
  }
  if (
    res.data
  ) {
    console.log('list_data', res.data)
    tableData.value = res.data;
    if (
      res.data.length === 1 &&
      typeof _patientId !== 'undefined' &&
      typeof _keyword === 'undefined'
    ) {
      patientInfo.value = res.data[0] as TableData;
      handleInput()
    }
  }
}

watch(() => modelValue, () => {
  // 如果modelValue 发生了变化 根据这个数据来更新本地的数据
  if (
    typeof modelValue.idCard === 'string' &&
    modelValue.idCard !== ''
  ) {
    console.log('user-select-modelValue', modelValue)
    patientInfo.value = modelValue
  }
})

onMounted(() => {
//   getPatientList();
  if (
    typeof defaultPatientId === 'string' &&
    defaultPatientId !== ''
  ) {
    nextTick(() => {
      getPatientList(
        undefined,
        defaultPatientId
      );
    })
  }
})

</script>

<script lang="ts">
  interface TableData {
    key?: string
    name: string
    gender: string
    age: string
    contactMobile: string
    idCard: string 
  }
</script>

<style lang="scss" scoped>
.bd-user-select-container {
  width: 100%;
  &.disabled {
    pointer-events: none;
  }
}
// 患者选中数据的展示
.patientSelectContainer {
  display: flex;
  padding: var(--spacing-5) var(--spacing-7);
  align-items: center;
  margin-top: var(--spacing-4);
  justify-content: space-between;
  background-color: var(--backlog-item-color);
  >span {
    color: #000000;
    font-weight: bold;
    &.line {
      width: 1px;
      height: 14px;
      background-color: rgba(0, 82, 217, 0.1);
    }
  }
}
</style>