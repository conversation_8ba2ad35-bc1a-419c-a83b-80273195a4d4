<!--
 * @Description: 顶部右侧菜单的操作内容 一般是当前用户信息的展示 以及操作
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-10-16 16:18:03
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 15:31:58
-->
<template>
  <div class="userInfo">
    <!-- 展示提示信息 -->
    <a-badge :count="0">
      <img :src="RingPng" alt="">
    </a-badge>
    <!-- 展示头像 -->
    <a-dropdown trigger="click">
      <a-avatar
        :size="32"
        :style="{ marginRight: '16px', cursor: 'pointer' }"
      >
        <img :src="avatarPng" alt="">
      </a-avatar>
      <template #content>
        <!-- <a-doption>
          <a-space @click="handleChangeTeam">
            <icon-export />
            <span>
              {{ $t('messageBox.changeTeam') }}
            </span>
          </a-space>
        </a-doption> -->
        <a-doption>
          <a-space @click="handleLogout">
            <icon-export />
            <span>
              {{ $t('messageBox.logout') }}
            </span>
          </a-space>
        </a-doption>
      </template>
    </a-dropdown>
  </div>
  
</template>

<script setup lang="ts">
import { useUserStore } from '@/store';
import ManPng from '@/assets/images/man.png';
import WomanPng from '@/assets/images/woman.png';
import RingPng from '@/assets/images/rings.png';
import { useRouter } from 'vue-router';
import {
  computed,
  h,
  ref
} from 'vue'
import { Modal } from '@arco-design/web-vue';
import to from 'await-to-js';
import infoConfirm from '../../views/login/components/info-confirm.vue';


const router = useRouter();
const userStore = useUserStore();
const infoConfirmRef = ref();
console.log('userStore', userStore)

const avatarPng = computed(() => {
  const {
    sex
  } = userStore.userInfo.accountInfo;
  if (
    sex === 2
  ) {
    return WomanPng;
  }
  return ManPng;
})

// 退出当前的登陆状态
const handleLogout = () => {
  userStore.logout().then(() => {
    router.push({
      name: 'login'
    })
  })
}

// 切换团队的结构
const handleChangeTeam = () => {
  Modal.open({
    width: '465px',
    title: '请选择需要登陆信息',
    content: () => h(infoConfirm, {
      ref: infoConfirmRef,
      userId: userStore.userInfo.accountInfo.userId as unknown as string,
      // userId: resData?.info?.userId as string
    }),
    async onBeforeOk(done) {
      const formData = infoConfirmRef.value.form;
      const [err, res] = await to(userStore.loginByRoleAndTeam(
        // @ts-ignore
        // resData?.info?.userId,
        userStore.userInfo.accountInfo.userId,
        formData.roleId,
        formData.teamId
      ))
      if (err) {
        done(false)
        throw err;
      }
      const { redirect, ...othersQuery } = router.currentRoute.value.query;
      console.log('redirect, name', redirect)
      router.push({
        replace: true,
        name: (redirect as string) || 'workbench',
        query: {
          ...othersQuery,
        },
      });
      console.log('formData', formData, res)
      // setLoading(false);
      done(true)
    },
  })
}

</script>

<style lang="scss" scoped>
.userInfo {
  display: flex;
  align-items: center;
  margin-right: var(--spacing-7);
  gap: var(--spacing-6);
  // 展示用户消息信息
  >span.arco-badge {
    >img {
      width: 16px;
      height: 16px;
    }
  }
  // 展示的用户头像信息
  >img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
  }
}
</style>