<template>
  <a-form
    ref="contentFormModalRef"
    :model="form"
    label-align="left"
    :label-col-props="{span: 5}"
    :wrapper-col-props="{span: 19}"
    :disabled="isView"
  >
    <div class="gridTwoForm">
      <a-form-item
        field="relationName"
        :label-col-props="{span: 10}"
        :wrapper-col-props="{span: 14}"
        label="联系人姓名"
        show-colon
        :rules="[
          {
            required: true,
            message: '请输入联系人姓名'
          }
        ]"
      >
        <a-input
          v-model="form.relationName"
          placeholder="请输入"
          allow-clear
        />
      </a-form-item>
      <a-form-item
        field="relationPhone"
        :label-col-props="{span: 10}"
        :wrapper-col-props="{span: 14}"
        label="联系人手机号"
        show-colon
        :rules="[
          {
            required: true,
            message: '请输入联系人手机号'
          }
        ]"
      >
        <a-input
          v-model="form.relationPhone"
          placeholder="请输入"
          allow-clear
        />
      </a-form-item>
    </div>
    <a-form-item
      field="diseaseDescription"
      label="症状描述"
      show-colon
      :rules="[
        {
          required: true,
          message: '请输入症状描述'
        }
      ]"
    >
      <a-textarea
        v-model="form.diseaseDescription"
        placeholder="请输入"
        allow-clear
        :max-length="500"
        show-word-limit
        auto-size
      />
    </a-form-item>
    <a-form-item
      v-if="!isView || form.diseaseInfoDTO.diseaseName || form.diseaseInfoDTO.diseaseInfo"
      field="diseaseInfoDTO"
      label="确诊疾病"
      show-colon
    >
      <a-input
        v-if="!isView || form.diseaseInfoDTO.diseaseName"
        v-model="form.diseaseInfoDTO.diseaseName"
        placeholder="请输入疾病名称"
        allow-clear
      />
      <div :style="{ width: '16px' }"></div>
      <a-input
        v-if="!isView || form.diseaseInfoDTO.diseaseInfo"
        v-model="form.diseaseInfoDTO.diseaseInfo"
        placeholder="请输入疾病信息"
        allow-clear
      />
    </a-form-item>
    <a-form-item
      v-if="!isView || form.fileType"
      field="applyOrderFileComponentDTO"
      label="辅助检查报告"
    >
      <div :style="{ width: '100%' }">
        <a-select
          v-model="form.fileType"
          :style="{
            width: '100%',
            marginBottom: '16px'
          }"
          placeholder="请选择单据类型"
          :allow-search="{ retainInputValue: true }"
        >
          <a-option
            v-for="item in fileTypeList"
            :key="item.dictValue"
            :value="item.dictValue"
          >
            {{ item.dictLabel }}
          </a-option>
        </a-select>
        <upload-file
          v-if="form.fileType"
          v-model="form.applyOrderFileComponentDTO"
          :file-type="form.fileType"
        />
      </div>
    </a-form-item>
    <a-form-item
      v-if="!isView || form.applyMark"
      field="applyMark"
      label="备注"
      show-colon
    >
      <a-input
        v-model="form.applyMark"
        placeholder="请输入"
        allow-clear
      />
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import { ApplyOrderUsingPOSTJiBingXinXi, getBdManageApiSystemDictDataTypeByDictType, getBdManageApiSystemDictDataTypeByDictTypeResponse, getBdManageApiSystemDictTypeList, getBdManageApiSystemDictTypeListResponse, postBdManageApiHealthInsuranceUploadFileResponse } from '@/batchApi';
import to from 'await-to-js';
import { FormInstance } from '@arco-design/web-vue';
import { onMounted, reactive, ref } from 'vue';
import uploadFile from './upload-file.vue';

const contentFormModalRef = ref<FormInstance>();

const fileTypeList = ref<getBdManageApiSystemDictDataTypeByDictTypeResponse['data']>([]);

const {
  isView
} = defineProps(['isView'])

const form = reactive<TEzForm>({
  relationName: '',
  relationPhone: '',
  // 文件类型
  fileType: '',
  // 资料信息
  applyOrderFileComponentDTO: undefined,
  // 症状描述
  diseaseDescription: '',
  // 备注信息
  applyMark: '',
  // 疾病信息
  diseaseInfoDTO: {
    diseaseName: '',
    diseaseInfo: ''
  }
})

const handleDictData = async (
  _dictType: string
) => {
  const [err, res] = await to(getBdManageApiSystemDictDataTypeByDictType({
    dictType: _dictType
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    res.data
  ) {
    return res.data
  }
  return []
}

const setFormData = (data: Partial<typeof form>) => {
  Object.assign(form, data);
};

onMounted(async () => {
  fileTypeList.value = await handleDictData('three_file_type');
})


defineExpose({
  formInstance: contentFormModalRef,
  setFormData
})

</script>

<script lang="ts">
export type TEzForm = {
  relationName: string
  relationPhone: string
  diseaseDescription: string
  fileType: string
  applyOrderFileComponentDTO: postBdManageApiHealthInsuranceUploadFileResponse['data'][] | undefined
  applyMark: string
  diseaseInfoDTO: ApplyOrderUsingPOSTJiBingXinXi
}

</script>

<style scoped>
.gridTwoForm {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}
</style>