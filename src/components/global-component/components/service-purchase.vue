<!--
* @description 服务购买弹窗
* @fileName service-purchase.vue
* <AUTHOR>
* @date 2025/06/05 11:22:46
!-->
<script setup lang="ts">
import { reactive, ref, watch, toRefs } from "vue";
import * as apis from "@/bdclientApi";
import { storeToRefs } from "pinia";
import { isNaN } from "lodash";
import useQuickAccessStore from "@/store/modules/quick-access";
import { Message, TableRowSelection } from "@arco-design/web-vue";

type IRecord = apis.QueryServicePackageUsingPOSTGroupServicePackageDto;
type IServicePackageListRecord =
  apis.QueryServicePackageUsingPOSTPingAnServicePackage;
type State = {
  loading: boolean;
  list: IRecord[];
};

const quickAccessStore = useQuickAccessStore();
const { patientId } = storeToRefs(quickAccessStore);

const props = defineProps<{ userInfo: any }>();
const { userInfo } = toRefs(props);

type AddAndSubType = "prepend" | "append";

const visible = defineModel<boolean>("visible", { required: true });
const selectedKeys = ref<string[]>([]);
const packageMap = ref();
const placeOrderLoading = ref<boolean>(false);

const rowSelection = reactive<TableRowSelection>({
  type: "checkbox",
  showCheckedAll: true,
  onlyCurrent: false,
});

const pageState = reactive<State>({
  loading: false,
  list: [],
});

const columns: any = [
  {
    title: "服务名称",
    dataIndex: "goodsName",
  },
  {
    title: "服务编码",
    dataIndex: "serviceCode",
  },
  {
    title: "服务金额",
    dataIndex: "unitPrice",
    slotName: "unitPrice",
  },
  {
    title: "数量",
    dataIndex: "quantity",
    slotName: "quantity",
    width: 100,
    align: "center",
  },
];

const getList = async () => {
  try {
    pageState.loading = true;
    const res =
      await apis.postBdClientPingAnServicePackageQueryServicePackage();
    if (res.code === "1") {
      const list = res.data as IRecord[];
      list?.forEach((packageItem: IRecord) => {
        packageItem?.servicePackageList?.forEach(
          (service: IServicePackageListRecord | any) => {
            service.quantity = 0;
          },
        );
      });

      const defaultList = list?.at(0);
      console.log("👬", defaultList);
      packageMap.value = defaultList;
      pageState.list = list;
    }
    pageState.loading = false;
  } catch (err) {
    pageState.loading = false;
    console.log("===>", err);
  }
};

const handleOk = async () => {
  try {
    const result = packageMap.value?.servicePackageList?.filter((item: any) =>
      selectedKeys.value?.includes(item?.serviceCode),
    );
    // 有效套餐
    const effectiveList = result?.filter(
      (service: { quantity: any }) => service?.quantity,
    );
    if (!effectiveList?.length) {
      Message.warning("请选择套餐和数量后下单");
      return false;
    }
    // 总价
    const totalAmount = effectiveList?.reduce((acc: any, pre: any) => {
      const value = Number(pre.unitPrice);
      if (!isNaN(value) && typeof pre.quantity === "number") {
        const product = value * 100 * pre.quantity;
        return acc + product;
      }
      return acc;
    }, 0);
    const infos = effectiveList?.map((item: any) => ({
      serviceCode: item?.serviceCode,
      quantity: item?.quantity,
    }));
    const params = {
      userId: userInfo.value.patientId,
      cardType: userInfo.value?.certType,
      name: userInfo.value?.name,
      cardNo: userInfo.value?.idCard,
      sex: userInfo.value?.gender,
      birthday: userInfo.value?.birthdate,
      phone: userInfo.value.contactMobile,
      amount: totalAmount,
      contract: {
        contractId: packageMap?.value.contractId,
        infos,
      },
    };
    placeOrderLoading.value = true;
    const res =
      await apis.postBdClientServiceOperationAddServicePackage(params);
    if (res?.code === "1") {
      Message.success("购买成功");
      return true;
    }
  } catch (error) {
    console.log("===>", error);
    return true;
  }
};

const handleAddAndSub = (type: AddAndSubType, record: any) => {
  const { serviceCode } = record || {};
  if (type === "prepend" && record.quantity > 0) {
    record.quantity -= 1;
    if (record.quantity === 0 && selectedKeys.value?.includes(serviceCode)) {
      const result = selectedKeys.value?.filter(code => code !== serviceCode);
      selectedKeys.value = result;
    }
  }
  if (type === "append") {
    record.quantity += 1;
    if (record.quantity > 0 && !selectedKeys.value?.includes(serviceCode)) {
      selectedKeys.value = [...selectedKeys.value, serviceCode];
    }
  }
};

watch(visible, newVal => {
  if (newVal) {
    getList();
  }
});
</script>

<template>
  <a-modal
    v-model:visible="visible"
    unmount-on-close
    :width="800"
    :mask-closable="false"
    title="保险服务购买"
    ok-text="确认下单"
    :on-before-ok="handleOk"
    @cancel="() => (visible = false)"
  >
    <a-spin class="container" tip="加载中...">
      <div class="table-wrapper">
        <a-form-item label="服务套餐">
          <a-select
            v-model="packageMap"
            value-key="contractId"
            placeholder="请选择套餐"
            allow-search
            @change="selectedKeys = []"
          >
            <a-option
              v-for="item in pageState.list"
              :key="item.contractId"
              :value="item"
              >{{ item?.contractName }}</a-option
            >
          </a-select>
        </a-form-item>
        <a-table
          v-model:selected-keys="selectedKeys"
          :loading="pageState.loading"
          row-key="serviceCode"
          :columns="columns"
          :data="packageMap?.servicePackageList"
          :row-selection="rowSelection"
          :pagination="false"
        >
          <template #quantity="{ record }">
            <div style="width: 130px; display: flex; justify-content: center">
              <a-input-number
                v-model="record.quantity"
                :style="{ width: '200px' }"
                mode="embed"
                size="large"
                class="input-demo"
                hide-button
                :min="0"
              >
                <template #append>
                  <icon-plus-circle
                    style="color: #0052d9"
                    size="16"
                    @click="handleAddAndSub('append', record)"
                  />
                </template>
                <template #prepend>
                  <icon-minus-circle
                    style="color: #0052d9"
                    size="16"
                    @click="handleAddAndSub('prepend', record)"
                  />
                </template>
              </a-input-number>
            </div>
          </template>
          <template #unitPrice="{ record }">
            <span class="amountText">{{
              record?.unitPrice ? `¥ ${record?.unitPrice}.00` : "-"
            }}</span>
          </template>
        </a-table>
      </div>
    </a-spin>
  </a-modal>
</template>

<style lang="scss" scoped>
.container {
  width: 100%;
}
.table-wrapper {
  width: 100%;
  background-color: white;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  .table-operate {
    display: flex;
    gap: 8px;
  }
  .table-container {
    overflow-y: scroll;
  }
  :deep(.arco-input-wrapper) {
    width: 46px;
    border-radius: 4px;
    height: 28px;
  }
  :deep(.arco-input-prepend) {
    background: transparent;
    border: none;
    cursor: pointer;
  }
  :deep(.arco-input-append) {
    background: transparent;
    border: none;
    cursor: pointer;
  }

  .amountText {
    color: #ff5252;
  }
}
</style>
