<!--
 * @Description: 单独进行省市区选择的组件
 * @Author: yangrong<PERSON>
 * @Date: 2025-04-11 11:11:14
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-04-14 17:46:12
-->
<template>
  <div
    :style="{
      display: 'flex',
      columnGap: '16px'
    }"
  >
    <a-select
      v-model="form.province"
      value-key="code"
      placeholder="请选择"
    >
      <a-option
        v-for="item in provinceList"
        :key="item.code"
        :value="item"
      >
        {{ item.codeName }}
      </a-option>
    </a-select>
    <a-select
      v-model="form.city"
      value-key="code"
      placeholder="请选择"
    >
      <a-option
        v-for="item in cityList"
        :key="item.code"
        :value="item"
      >
        {{ item.codeName }}
      </a-option>
    </a-select>
    <a-select
      v-model="form.county"
      value-key="code"
      placeholder="请选择"
    >
      <a-option
        v-for="item in countyList"
        :key="item.code"
        :value="item"
      >
        {{ item.codeName }}
      </a-option>
    </a-select>
  </div>
</template>

<script setup lang="ts">
import { postBdManageApiHealthInsuranceGetBaseCodesForApp, postBdManageApiHealthInsuranceGetBaseCodesForAppResponse, postBdManageApiHealthInsuranceQueryHospitalInfoForOuter, postBdManageApiHealthInsuranceQueryLayerDepartmentInfoList, QueryHospitalInfoForOuterUsingPOSTHospitalInfoForOuterPageRespVo, QueryLayerDepartmentInfoListUsingPOSTLayerDepartmentInfoRespVo } from '@/batchApi';
import to from 'await-to-js';
import {
  onMounted,
  reactive,
  ref,
  watch
} from 'vue'

const provinceList = ref<postBdManageApiHealthInsuranceGetBaseCodesForAppResponse['data']>([]);
const cityList = ref<postBdManageApiHealthInsuranceGetBaseCodesForAppResponse['data']>([]);
const countyList = ref<postBdManageApiHealthInsuranceGetBaseCodesForAppResponse['data']>([]);

const {
  modelValue
} = defineProps(['modelValue'])

const emit = defineEmits(['update:modelValue'])

const form = reactive<TAreaData>({
  // 预约医院的省数据
  province: undefined,
  // provinceCode: '',
  // provinceName: '',
  // 预约医院的市数据
  city: undefined,
  // cityCode: '',
  // cityName: '',
  // 预约医院的区数据
  county: undefined,
  // countyCode: '',
  // countyName: '',
})

watch(() => form.province, async () => {
  cityList.value = await queryAreaList(form.province?.code, 'province')
  emit('update:modelValue', form)
})

watch(() => form.city?.code, async () => {
  countyList.value = await queryAreaList(form.city?.code, 'city')
  emit('update:modelValue', form)
})

watch(() => form.county?.code, async () => {
  emit('update:modelValue', form)
})

const queryAreaList = async (
  _code: string | undefined,
  _scopeType: string
) => {
  if (
    !_code
  ) {
    return [];
  }
  const [err, res] = await to(postBdManageApiHealthInsuranceGetBaseCodesForApp({
    code: _code,
    scopeType: _scopeType
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    res.data
  ) {
    return res.data
  }
  console.log('res', res)
  return []
}

onMounted(async () => {
  provinceList.value = await queryAreaList('156', 'country');
})

watch(() => modelValue, () => {
  if (
    typeof modelValue.province?.code === 'string' && modelValue.province?.code !== '' &&
    typeof modelValue.city?.code === 'string' && modelValue.city?.code !== '' &&
    typeof modelValue.county?.code === 'string' && modelValue.county?.code !== ''
  ) {
    Object.assign(form, modelValue);
  }
})

</script>

<script lang="ts">

export type TAreaData = {
  province: {
    code: string,
    codeName: string
  } | undefined
  city: {
    code: string,
    codeName: string
  } | undefined
  county: {
    code: string,
    codeName: string
  } | undefined
}

</script>

<style lang="scss" scoped>

</style>