<template>
  <a-form
    ref="contentFormModalRef"
    :model="form"
    label-align="left"
    :label-col-props="{span: 5}"
    :wrapper-col-props="{span: 19}"
    :disabled="isView"
  >
    <div class="gridTwoForm">
      <a-form-item
        field="relationName"
        :label-col-props="{span: 10}"
        :wrapper-col-props="{span: 14}"
        label="联系人姓名"
        show-colon
        :rules="[
          {
            required: true,
            message: '请输入联系人姓名'
          }
        ]"
      >
        <a-input
          v-model="form.relationName"
          placeholder="请输入"
          allow-clear
        />
      </a-form-item>
      <a-form-item
        field="relationPhone"
        :label-col-props="{span: 10}"
        :wrapper-col-props="{span: 14}"
        label="联系人手机号"
        show-colon
        :rules="[
          {
            required: true,
            message: '请输入联系人手机号'
          }
        ]"
      >
        <a-input
          v-model="form.relationPhone"
          placeholder="请输入"
          allow-clear
        />
      </a-form-item>
    </div>
    <!-- 服务细项 -->
    <!-- <a-form-item
      field="serviceItemList"
      label="服务细项"
      show-colon
      :rules="[
        {
          required: true,
          message: '请输入联系人手机号'
        }
      ]"
    >
      <a-select
        v-model="form.serviceItemList"
        value-key="serviceItemCode"
        placeholder="请选择"
        multiple
      >
        <a-option
          v-for="item in rightsItemList"
          :key="item.serviceItemCode"
          :value="item"
        >
          {{ item.serviceItemName }}
        </a-option>
      </a-select>
    </a-form-item> -->
    <a-form-item
      field="county"
      label="期望住院城市"
      show-colon
      :rules="[
        {
          required: true,
          message: '请选择期望住院城市'
        }
      ]"
    >
      <div
        :style="{
          display: 'flex',
          columnGap: '16px'
        }"
      >
        <a-select
          v-model="form.province"
          value-key="code"
          placeholder="请选择"
          :loading="loadingContent.loadingProvince"
        >
          <a-option
            v-for="item in provinceList"
            :key="item.code"
            :value="item"
          >
            {{ item.codeName }}
          </a-option>
        </a-select>
        <a-select
          v-model="form.city"
          value-key="code"
          placeholder="请选择"
          :loading="loadingContent.loadingCity"
        >
          <a-option
            v-for="item in cityList"
            :key="item.code"
            :value="item"
          >
            {{ item.codeName }}
          </a-option>
        </a-select>
        <a-select
          v-model="form.county"
          value-key="code"
          placeholder="请选择"
          :loading="loadingContent.loadingCounty"
        >
          <a-option
            v-for="item in countyList"
            :key="item.code"
            :value="item"
          >
            {{ item.codeName }}
          </a-option>
        </a-select>
      </div>
    </a-form-item>
    <div class="gridTwoForm">
      <a-form-item
        field="appointHospital"
        :label-col-props="{span: 10}"
        :wrapper-col-props="{span: 14}"
        label="期望医院"
        show-colon
        :rules="[
          {
            required: true,
            message: '请选择期望医院'
          }
        ]"
      >
        <a-select
          v-model="form.appointHospital"
          value-key="hospitalId"
          placeholder="请选择"
          :loading="loadingContent.loadingHospital"
        >
          <a-option
            v-for="item in hospitalList"
            :key="item.hospitalId"
            :value="item"
          >
            {{ item.hospitalName }}
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item
        field="appointDepartment"
        :label-col-props="{span: 10}"
        :wrapper-col-props="{span: 14}"
        label="期望科室"
        show-colon
        :rules="[
          {
            required: true,
            message: '请选择期望科室'
          }
        ]"
      >
        <a-tree-select
          v-model="form.appointDepartment"
          :field-names="{
            key: 'departmentName',
            title: 'departmentName',
            children: 'children'
          }"
          :data="departmentList"
          :loading="loadingContent.loadingDepartment"
          placeholder="请选择科室"
        ></a-tree-select>
      </a-form-item>
    </div>
    <div class="gridTwoForm">
      <!-- 入院日期、预计出院日期 -->
      <a-form-item
        v-if="!isView || form.appointDate"
        field="appointDate"
        :label-col-props="{span: 10}"
        :wrapper-col-props="{span: 14}"
        label="入院日期"
        show-colon
      >
        <a-date-picker
          v-model="form.appointDate"
          show-time
          format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
      </a-form-item>
      <a-form-item
        v-if="!isView || form.appointDateEnd"
        field="appointDateEnd"
        :label-col-props="{span: 10}"
        :wrapper-col-props="{span: 14}"
        label="预计出院日期"
        show-colon
      >
        <a-date-picker
          v-model="form.appointDateEnd"
          show-time
          format="YYYY-MM-DD HH:mm:ss"
          style="width: 100%"
        />
      </a-form-item>
    </div>
    <a-form-item
      field="userArea"
      label="家庭住址"
      show-colon
      :rules="[
        {
          required: true,
          message: '请选择家庭住址'
        }
      ]"
    >
      <area-select
        v-model="form.userArea"
      />
    </a-form-item>
    
    <!-- 详细地址 -->
    <a-form-item
      field="userAddress"
      label="详细地址"
      show-colon
      :rules="[
        {
          required: true,
          message: '请选择详细地址'
        }
      ]"
    >
      <a-input
        v-model="form.userAddress"
        placeholder="请输入"
        allow-clear
      />
    </a-form-item>
    <a-form-item
      field="diseaseDescription"
      label="症状描述"
      show-colon
      :rules="[
        {
          required: true,
          message: '请输入症状描述'
        }
      ]"
    >
      <a-textarea
        v-model="form.diseaseDescription"
        placeholder="请输入"
        allow-clear
        :max-length="500"
        show-word-limit
        auto-size
      />
    </a-form-item>
    <a-form-item
      v-if="!isView || form.applyMark"
      field="applyMark"
      label="备注"
      show-colon
    >
      <a-input
        v-model="form.applyMark"
        placeholder="请输入"
        allow-clear
      />
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import { postBdManageApiHealthInsuranceGetBaseCodesForApp, postBdManageApiHealthInsuranceGetBaseCodesForAppResponse, postBdManageApiHealthInsuranceQueryHospitalInfoForOuter, postBdManageApiHealthInsuranceQueryLayerDepartmentInfoList, QueryHospitalInfoForOuterUsingPOSTHospitalInfoForOuterPageRespVo, QueryHospitalInfoForOuterUsingPOSTHospitalInfoForOuterRespVo, QueryLayerDepartmentInfoListUsingPOSTLayerDepartmentInfoRespVo } from '@/batchApi';
import { FormInstance } from '@arco-design/web-vue';
import to from 'await-to-js';
import { onMounted, reactive, ref, watch } from 'vue';
import { TForm, TRightsItem } from '../reservation.vue';
import areaSelect, { TAreaData } from './area-select.vue';

const {
  rightsItem,
  rightsItemList,
  isView
} = defineProps<{
  rightsItem: TForm['project'],
  rightsItemList: TRightsItem[],
  isView: boolean
}>()

const contentFormModalRef = ref<FormInstance>();

const loadingContent = reactive({
  loadingCounty: false,
  loadingProvince: false,
  loadingCity: false,
  loadingHospital: false,
  loadingDepartment: false,
})


const provinceList = ref<postBdManageApiHealthInsuranceGetBaseCodesForAppResponse['data']>([]);
const cityList = ref<postBdManageApiHealthInsuranceGetBaseCodesForAppResponse['data']>([]);
const countyList = ref<postBdManageApiHealthInsuranceGetBaseCodesForAppResponse['data']>([]);
const hospitalList = ref<QueryHospitalInfoForOuterUsingPOSTHospitalInfoForOuterPageRespVo['dataList']>([])
const departmentList = ref<QueryLayerDepartmentInfoListUsingPOSTLayerDepartmentInfoRespVo[]>();

const form = reactive<TKfhlForm>({
  relationName: '',
  relationPhone: '',
  // serviceItemList: [],
  // 预约医院的省数据
  province: undefined,
  // 期望预约医院的省市区
  // provinceCode: '',
  // provinceName: '',
  city: undefined,
  // cityCode: '',
  // cityName: '',
  county: undefined,
  // countyCode: '',
  // countyName: '',
  // 预约医院相关信息
  appointHospital: undefined,
  // 预约科室
  appointDepartment: '',
  // 入院日期
  appointDate: '',
  // 预计出院日期
  appointDateEnd: '',
  // 用户的地址信息
  userArea: undefined,
  // 症状描述
  diseaseDescription: '',
  // 备注信息
  applyMark: '',
  // 详细地址
  userAddress: ''
})

watch(() => form.province, async () => {
  loadingContent.loadingCity = true;
  cityList.value = await queryAreaList(form.province?.code, 'province')
  loadingContent.loadingCity = false;
})

watch(() => form.city?.code, async () => {
  loadingContent.loadingCounty = true;
  countyList.value = await queryAreaList(form.city?.code, 'city')
  loadingContent.loadingCounty = false;
})

watch(() => form.county?.code, async () => {
  loadingContent.loadingHospital = true;
  hospitalList.value = await queryHospital();
  loadingContent.loadingHospital = false;
})

watch(() => form.appointHospital, async () => {
  loadingContent.loadingDepartment = true;
  departmentList.value = await queryDepartment();
  loadingContent.loadingDepartment = false;
})

watch(() => form.userArea, () => {
  console.log('form.userArea', form.userArea)
}, {
  deep: true
})

const queryAreaList = async (
  _code: string | undefined,
  _scopeType: string
) => {
  if (
    !_code
  ) {
    return [];
  }
  const [err, res] = await to(postBdManageApiHealthInsuranceGetBaseCodesForApp({
    code: _code,
    scopeType: _scopeType
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    res.data
  ) {
    return res.data
  }
  console.log('res', res)
  return []
}

const queryHospital = async () => {
  console.log('rightsItem', rightsItem)
  const [err, res] = await to(postBdManageApiHealthInsuranceQueryHospitalInfoForOuter({
    cityCode: form.city?.code,
    countyCode: form.county?.code,
    provinceCode: form.province?.code,
    serviceItemCode: rightsItem?.serviceItemCode ?? '',
    pageNo: '1',
    pageSize: '999'
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    Array.isArray(res?.data?.dataList)
  ) {
    return res.data.dataList
  }
  console.log('res', res)
  return []
}

const queryDepartment = async () => {
  const [err, res] = await to(postBdManageApiHealthInsuranceQueryLayerDepartmentInfoList({
    // 省市区
    hospitalId: '10370'
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    Array.isArray(res?.data?.layerDepartmentInfoList)
  ) {
    return res.data?.layerDepartmentInfoList
  }
  console.log('department_res', res)
  return []
}

const setFormData = (data: Partial<typeof form>) => {
  Object.assign(form, data);
};

onMounted(async () => {
  provinceList.value = await queryAreaList('156', 'country');
})

defineExpose({
  formInstance: contentFormModalRef,
  setFormData
})

</script>

<script lang="ts">
export type TKfhlForm = {
  relationName: string
  relationPhone: string
  // serviceItemList: TRightsItem[]
  province: {
    code: string,
    codeName: string
  } | undefined
  city: {
    code: string,
    codeName: string
  } | undefined
  county: {
    code: string,
    codeName: string
  } | undefined
  appointHospital: QueryHospitalInfoForOuterUsingPOSTHospitalInfoForOuterRespVo | undefined
  appointDepartment: string
  appointDate: string
  appointDateEnd: string
  userArea: TAreaData | undefined
  diseaseDescription: string
  applyMark: string
  userAddress: string
}


</script>

<style scoped>
.gridTwoForm {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}
</style>