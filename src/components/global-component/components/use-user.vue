<!--
 * @Description: 获取使用人的公共信息
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-04-10 15:59:08
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-27 10:23:33
-->
<template>
  <a-form
    ref="contentFormModalRef"
    :model="form"
    :label-col-props="{span: 10}"
    :wrapper-col-props="{span: 14}"
    label-align="left"
    :disabled="isView"
    class="useUserForm"
  >
    <a-form-item
      field="userName"
      label="使用人姓名"
      show-colon
      :rules="[
        {
          required: true,
          message: '请填写使用人姓名'
        }
      ]"
    >
      <a-input
        v-model="form.userName"
        placeholder="请搜索并选择患者"
        allow-clear
      />
    </a-form-item>
    <a-form-item
      field="userMobile"
      label="使用人电话"
      show-colon
      :rules="validationRules.mobile"
    >
      <a-input
        v-model="form.userMobile"
        placeholder="请搜索并选择患者"
        allow-clear
      />
    </a-form-item>
    <a-form-item
      field="userIdType"
      label="证件类型"
      show-colon
      :rules="[
        {
          required: true,
          message: '请选择使用人证件类型'
        },
      ]"
    >
      <a-select
        v-model="form.userIdType"
        placeholder="请搜索并选择患者"
      >
        <!-- 
            身份证1
            护照2
            军官证/士兵证3
            少儿证4
            户口本L
            出生证A
            港澳台回乡证6
            驾驶证7
            港澳台居住证K
            外国人永久居留身份证O
        -->
        <a-option
          v-for="item in idTypeList"
          :key="item.dictValue"
          :value="item.dictValue"
        >
          {{ item.dictLabel }}
        </a-option>
      </a-select>
    </a-form-item>
    <a-form-item
      field="userIdNo"
      label="使用人证件号"
      show-colon
      :rules="[
        {
          required: true,
          message: '请填写使用人证件号'
        },
        { validator: (
            value,
            callback
          ) => {
            validateIdCard(value, callback, form.userIdType !== '1') 
          } 
        }
      ]"
    >
      <a-input
        v-model="form.userIdNo"
        placeholder="请搜索并选择患者"
        allow-clear
      />
    </a-form-item>
    <a-form-item
      field="userSex"
      label="使用人性别"
      show-colon
      :rules="[
        {
          required: true,
          message: '请选择使用人性别'
        }
      ]"
    >
      <a-select
        v-model="form.userSex"
        placeholder="请搜索并选择患者"
      >
        <a-option
          value="M"
        >
          男
        </a-option>
        <a-option
          value="F"
        >
          女
        </a-option>
      </a-select>
    </a-form-item>
    <a-form-item
      field="userBirthday"
      label="出生年月"
      show-colon
      :rules="[
        {
          required: true,
          message: '请选择使用人出生年月'
        }
      ]"
    >
      <a-date-picker
        v-model="form.userBirthday"
        :disabled="typeof form.userBirthday === 'string' && form.userBirthday !== ''"
        style="width: 100%"
        placeholder="请搜索并选择患者"
      />
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import { getBdManageApiSystemDictDataTypeByDictType, getBdManageApiSystemDictDataTypeByDictTypeResponse, getBdManageApiSystemDictTypeListResponse } from '@/batchApi';
import { FormInstance } from '@arco-design/web-vue';
import to from 'await-to-js';
import { onMounted, reactive, ref, watch } from 'vue';
import {
  validationRules,
  validateIdCard
} from '@/utils/validator'

const form = reactive<TUseUserForm>({
  userName: '',
  // 使用人电话
  userMobile: '',
  // 使用人证件号
  userIdNo: '',
  // 使用人出生年月日
  userBirthday: '',
  // 使用人性别
  userSex: '',
  // 使用人证件类型
  userIdType: ''
})

const contentFormModalRef = ref<FormInstance>();
const idTypeList = ref<getBdManageApiSystemDictDataTypeByDictTypeResponse['data']>([])

const {
  isView
} = defineProps(['isView'])

const handleDictData = async (
  _dictType: string
) => {
  const [err, res] = await to(getBdManageApiSystemDictDataTypeByDictType({
    dictType: _dictType
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    res.data
  ) {
    return res.data
  }
  return []
}

// 提供设置表单数据的方法
const setFormData = (data: Partial<typeof form>) => {
  Object.assign(form, data);
};


onMounted(async () => {
  idTypeList.value = await handleDictData('cert_type');
  console.log('contentFormModalRef', contentFormModalRef)
})

defineExpose({
  formInstance: contentFormModalRef,
  setFormData
})
</script>

<script lang="ts">
export type TUseUserForm = {
  userName: string
  userMobile: string
  userBirthday: string
  userSex: string
  userIdNo: string
  userIdType: string
}

</script>


<style scoped lang="scss">
.useUserForm {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}
</style>