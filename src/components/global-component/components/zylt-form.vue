<!--
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2025-04-10 11:19:51
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-06-06 15:15:22
-->
<!-- 住院绿通的表单服务内容 -->
<template>
  <a-form
    ref="contentFormModalRef"
    :model="form"
    label-align="left"
    :label-col-props="{span: 5}"
    :wrapper-col-props="{span: 19}"
    :disabled="isView"
  >
    <div class="gridTwoForm">
      <a-form-item
        field="relationName"
        :label-col-props="{span: 10}"
        :wrapper-col-props="{span: 14}"
        label="联系人姓名"
        show-colon
        :rules="[
          {
            required: true,
            message: '请输入联系人姓名'
          }
        ]"
      >
        <a-input
          v-model="form.relationName"
          placeholder="请输入"
          allow-clear
        />
      </a-form-item>
      <a-form-item
        field="relationPhone"
        :label-col-props="{span: 10}"
        :wrapper-col-props="{span: 14}"
        label="联系人手机号"
        show-colon
        :rules="[
          {
            required: true,
            message: '请输入联系人手机号'
          }
        ]"
      >
        <a-input
          v-model="form.relationPhone"
          placeholder="请输入"
          allow-clear
        />
      </a-form-item>
    </div>
    <a-form-item
      field="county"
      label="期望住院城市"
      show-colon
      :rules="[
        {
          required: true,
          message: '请选择期望住院城市'
        }
      ]"
    >
      <div
        :style="{
          display: 'flex',
          columnGap: '16px'
        }"
      >
        <a-select
          v-model="form.province"
          value-key="code"
          placeholder="请选择"
          :loading="loadingContent.loadingProvince"
        >
          <a-option
            v-for="item in provinceList"
            :key="item.code"
            :value="item"
          >
            {{ item.codeName }}
          </a-option>
        </a-select>
        <a-select
          v-model="form.city"
          value-key="code"
          placeholder="请选择"
          :loading="loadingContent.loadingCity"
        >
          <a-option
            v-for="item in cityList"
            :key="item.code"
            :value="item"
          >
            {{ item.codeName }}
          </a-option>
        </a-select>
        <a-select
          v-model="form.county"
          value-key="code"
          placeholder="请选择"
          :loading="loadingContent.loadingCounty"
        >
          <a-option
            v-for="item in countyList"
            :key="item.code"
            :value="item"
          >
            {{ item.codeName }}
          </a-option>
        </a-select>
      </div>
    </a-form-item>
    <div class="gridTwoForm">
      <a-form-item
        field="appointHospital"
        :label-col-props="{span: 10}"
        :wrapper-col-props="{span: 14}"
        label="期望医院"
        show-colon
        :rules="[
          {
            required: true,
            message: '请选择期望医院'
          }
        ]"
      >
        <a-select
          v-model="form.appointHospital"
          value-key="hospitalId"
          placeholder="请选择"
          :loading="loadingContent.loadingHospital"
        >
          <a-option
            v-for="item in hospitalList"
            :key="item.hospitalId"
            :value="item"
          >
            {{ item.hospitalName }}
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item
        field="appointDepartment"
        :label-col-props="{span: 10}"
        :wrapper-col-props="{span: 14}"
        label="期望科室"
        show-colon
        :rules="[
          {
            required: true,
            message: '请选择期望科室'
          }
        ]"
      >
        <a-tree-select
          v-model="form.appointDepartment"
          :field-names="{
            key: 'departmentName',
            title: 'departmentName',
            children: 'children'
          }"
          :data="departmentList"
          :loading="loadingContent.loadingDepartment"
          placeholder="请选择科室"
        ></a-tree-select>
      </a-form-item>
    </div>
    <a-form-item
      v-if="!isView || form.appointDate"
      field="appointDate"
      label="预约开始时间"
      show-colon
      :rules="[
        {
          required: [EProjectType.PH, EProjectType.PZ].includes(serviceCategory as EProjectType),
          message: '请选择预约开始时间'
        }
      ]"
    >
      <a-date-picker
        v-model="form.appointDate"
        show-time
        format="YYYY-MM-DD HH:mm:ss"
        style="width: 100%"
      />
    </a-form-item>
    <a-form-item
      v-if="!isView || form.fileType"
      field="applyOrderFileComponentDTO"
      label="住院单"
    >
      <div :style="{ width: '100%' }">
        <a-select
          v-model="form.fileType"
          :style="{
            width: '100%',
            marginBottom: '16px'
          }"
          placeholder="请选择单据类型"
          :allow-search="{ retainInputValue: true }"
        >
          <a-option
            v-for="item in fileTypeList"
            :key="item.dictValue"
            :value="item.dictValue"
          >
            {{ item.dictLabel }}
          </a-option>
        </a-select>
        <upload-file
          v-if="form.fileType"
          v-model="form.applyOrderFileComponentDTO"
          :file-type="form.fileType"
        />
      </div>
    </a-form-item>
    <a-form-item
      v-if="!isView || form.diseaseDescription"
      field="diseaseDescription"
      label="症状描述"
      show-colon
    >
      <a-textarea
        v-model="form.diseaseDescription"
        placeholder="请输入"
        allow-clear
        :max-length="500"
        show-word-limit
        auto-size
      />
    </a-form-item>
    <a-form-item
      v-if="!isView || form.applyMark"
      field="applyMark"
      label="备注"
      show-colon
    >
      <a-input
        v-model="form.applyMark"
        placeholder="请输入"
        allow-clear
      />
    </a-form-item>
  </a-form>
</template>

<script
  setup
  lang="ts"
>

import { getBdManageApiSystemDictDataTypeByDictType, getBdManageApiSystemDictDataTypeByDictTypeResponse, getBdManageApiSystemDictTypeList, getBdManageApiSystemDictTypeListResponse, postBdManageApiHealthInsuranceGetBaseCodesForApp, postBdManageApiHealthInsuranceGetBaseCodesForAppResponse, postBdManageApiHealthInsuranceQueryHospitalInfoForOuter, postBdManageApiHealthInsuranceQueryLayerDepartmentInfoList, postBdManageApiHealthInsuranceUploadFileResponse, QueryHospitalInfoForOuterUsingPOSTHospitalInfoForOuterPageRespVo, QueryHospitalInfoForOuterUsingPOSTHospitalInfoForOuterRespVo, QueryLayerDepartmentInfoListUsingPOSTLayerDepartmentInfoRespVo } from '@/batchApi';
import { FormInstance } from '@arco-design/web-vue';
import to from 'await-to-js';
import { onMounted, reactive, ref, watch } from 'vue';
import uploadFile from './upload-file.vue';
import type {
  TForm
} from '../reservation.vue'
import {
  EProjectType
} from '../reservation.vue'

const {
  rightsItem,
  isView,
  serviceCategory
} = defineProps<{
  rightsItem: TForm['project']
  isView: boolean,
  serviceCategory: string
}>()

const loadingContent = reactive({
  loadingCounty: false,
  loadingProvince: false,
  loadingCity: false,
  loadingHospital: false,
  loadingDepartment: false,
})

const contentFormModalRef = ref<FormInstance>();

const provinceList = ref<postBdManageApiHealthInsuranceGetBaseCodesForAppResponse['data']>([]);
const cityList = ref<postBdManageApiHealthInsuranceGetBaseCodesForAppResponse['data']>([]);
const countyList = ref<postBdManageApiHealthInsuranceGetBaseCodesForAppResponse['data']>([]);
const hospitalList = ref<QueryHospitalInfoForOuterUsingPOSTHospitalInfoForOuterPageRespVo['dataList']>([])
const departmentList = ref<QueryLayerDepartmentInfoListUsingPOSTLayerDepartmentInfoRespVo[]>();
const fileTypeList = ref<getBdManageApiSystemDictDataTypeByDictTypeResponse['data']>([]);


const form = reactive<TZyltForm>({
  relationName: '',
  relationPhone: '',
  // 预约医院的省数据
  province: undefined,
  // 期望预约医院的省市区
  // provinceCode: '',
  // provinceName: '',
  city: undefined,
  // cityCode: '',
  // cityName: '',
  county: undefined,
  // countyCode: '',
  // countyName: '',
  // 预约开始时间
  appointDate: '',
  // 预约医院相关信息
  appointHospital: undefined,
  // 预约科室
  appointDepartment: '',
  // 文件类型
  fileType: '',
  // 资料信息
  applyOrderFileComponentDTO: undefined,
  // 症状描述
  diseaseDescription: '',
  // 备注信息
  applyMark: ''
})

watch(() => form.province, async () => {
  loadingContent.loadingCity = true;
  cityList.value = await queryAreaList(form.province?.code, 'province')
  loadingContent.loadingCity = false;
})

watch(() => form.city?.code, async () => {
  loadingContent.loadingCounty = true;
  countyList.value = await queryAreaList(form.city?.code, 'city')
  loadingContent.loadingCounty = false;
})

watch([
  () => form.county?.code,
  () => rightsItem
], async () => {
  if (
    rightsItem
  ) {
    loadingContent.loadingHospital = true;
    hospitalList.value = await queryHospital();
    loadingContent.loadingHospital = false;
  }
})

watch(() => form.appointHospital, async () => {
  loadingContent.loadingDepartment = true;
  departmentList.value = await queryDepartment();
  loadingContent.loadingDepartment = false;
})

const queryAreaList = async (
  _code: string | undefined,
  _scopeType: string
) => {
  if (
    !_code
  ) {
    return [];
  }
  const [err, res] = await to(postBdManageApiHealthInsuranceGetBaseCodesForApp({
    code: _code,
    scopeType: _scopeType
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    res.data
  ) {
    return res.data
  }
  console.log('res', res)
  return []
}

const queryHospital = async () => {
  console.log('rightsItem', rightsItem)
  const [err, res] = await to(postBdManageApiHealthInsuranceQueryHospitalInfoForOuter({
    cityCode: form.city?.code,
    countyCode: form.county?.code,
    provinceCode: form.province?.code,
    serviceItemCode: rightsItem?.serviceItemCode ?? '',
    pageNo: '1',
    pageSize: '999'
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    Array.isArray(res?.data?.dataList)
  ) {
    return res.data.dataList
  }
  console.log('res', res)
  return []
}

const queryDepartment = async () => {
  const [err, res] = await to(postBdManageApiHealthInsuranceQueryLayerDepartmentInfoList({
    // 省市区
    hospitalId: '10370'
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    Array.isArray(res?.data?.layerDepartmentInfoList)
  ) {
    return res.data?.layerDepartmentInfoList
  }
  console.log('department_res', res)
  return []
}

const handleDictData = async (
  _dictType: string
) => {
  const [err, res] = await to(getBdManageApiSystemDictDataTypeByDictType({
    dictType: _dictType
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    res.data
  ) {
    return res.data
  }
  return []
}

const setFormData = (data: Partial<typeof form>) => {
  Object.assign(form, data);
};

onMounted(async () => {
  loadingContent.loadingProvince = true;
  provinceList.value = await queryAreaList('156', 'country');
  loadingContent.loadingProvince = false;
  fileTypeList.value = await handleDictData('three_file_type');
})

defineExpose({
  formInstance: contentFormModalRef,
  setFormData
})
</script>

<script lang="ts">
export type TZyltForm = {
  relationName: string
  relationPhone: string
  province: {
    code: string,
    codeName: string
  } | undefined
  city: {
    code: string,
    codeName: string
  } | undefined
  county: {
    code: string,
    codeName: string
  } | undefined
  appointDate: string
  appointHospital: QueryHospitalInfoForOuterUsingPOSTHospitalInfoForOuterRespVo | undefined
  appointDepartment: string
  // 文件类型
  fileType: string
  // 文件上传的对象 内容需要单独进行定义
  applyOrderFileComponentDTO: postBdManageApiHealthInsuranceUploadFileResponse['data'][] | undefined
  diseaseDescription: string
  applyMark: string
}

</script>

<style lang="scss">
.gridTwoForm {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}
</style>