<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2025-04-10 17:35:38
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-11 10:30:44
-->
<template>
  <div
    class="file-upload-wrapper"
    :style="{
      display: 'flex',
      flexWrap: 'wrap',
      columnGap: '16px',
      rowGap: '16px',
    }"
  >
    <!-- <a-image
      v-for="(item, index) in pageState.uploadImageList"
      :key="`${index}`"
      width="80px"
      height="80px"
      :src="item?.uri"
      class="image-wrapper"
    >
      <template #extra>
        <div v-if="!disabled" class="actions">
          <span class="iconStyle" @click="onDeleteImage(index)"
            ><icon-delete
          /></span>
        </div>
      </template>
    </a-image> -->
    <template
      v-for="(item, index) in pageState.uploadImageList"
      :key="`${index}`"
    >
      <a-image
        v-if="imageExtensions?.includes(item?.ext)"
        width="80px"
        height="80px"
        :src="item?.uri"
        :preview-visible="imgVisble"
        class="image-wrapper"
        @preview-visible-change="
          () => {
            imgVisble = false;
          }
        "
      >
        <template #extra>
          <div v-if="!disabled" class="actions">
            <span class="action" @click="imgVisble = true"><icon-eye /></span>
            <span class="iconStyle" @click="onDeleteImage(index)"
              ><icon-delete
            /></span>
          </div>
        </template>
      </a-image>
      <a-image
        v-if="item?.ext === 'pdf'"
        width="80px"
        height="80px"
        :src="pdfPng"
        class="image-wrapper"
      >
        <template #extra>
          <div v-if="!disabled" class="actions">
            <span class="action" @click="handlePreview(item)"
              ><icon-eye
            /></span>
            <span class="iconStyle" @click="onDeleteImage(index)"
              ><icon-delete
            /></span>
          </div>
        </template>
      </a-image>
    </template>
    <a-spin :loading="pageState.uploadLoading">
      <a-upload
        v-if="pageState.uploadImageList.length < limit"
        accept="image/*,application/pdf"
        image-preview
        :show-file-list="false"
        :custom-request="customRequest"
        :disabled="disabled"
        @before-upload="beforeUpload"
      >
        <template #upload-button>
          <div v-if="!disabled">
            <div class="arco-upload-picture-card">
              <div class="arco-upload-picture-card-text">
                <IconPlus />
                <div style="margin-top: 10px; font-weight: 600">上传</div>
              </div>
            </div>
          </div>
        </template>
      </a-upload>
    </a-spin>
    <!-- 单独pdf预览的组件 -->
    <a-modal
      v-model:visible="pdfVisible"
      title="pdf预览"
      width="800px"
      :mask-closable="false"
      :footer="false"
      unmount-on-close
      @cancel="pdfVisible = false"
    >
      <div style="height: 80vh; overflow: auto">
        <vue-pdf-embed ref="pdfRef" :source="pdfSrc" />
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { getToken } from "@/utils/auth";
import { Message, useFormItem } from "@arco-design/web-vue";
import { onMounted, ref, watch } from "vue";
import pdfPng from "@/assets/images/<EMAIL>";
import VuePdfEmbed from "vue-pdf-embed";

const imageExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "svg"];

const {
  modelValue,
  limit = 99,
  fileType,
  maxSize = 10,
} = defineProps(["modelValue", "limit", "fileType", "maxSize"]);

const pdfVisible = ref(false);
const pdfSrc = ref();
const imgVisble = ref<boolean>(false);

const { mergedDisabled } = useFormItem();

const disabled = mergedDisabled.value;

const emit = defineEmits(["update:modelValue"]);

const pageState = ref({
  uploadImageList: [],
  uploadLoading: false,
});

const handlePreview = (file: any) => {
  pdfSrc.value = file.uri;
  pdfVisible.value = true;
};

const beforeUpload = (file: File) => {
  const isLt20M = file.size / 1024 / 1024 < (maxSize ?? 1);
  if (!isLt20M) {
    Message.error(`文件大小不能超过${maxSize}M`);
    return false;
  }
  return true;
};

onMounted(() => {
  if (Array.isArray(modelValue) && modelValue.length !== 0) {
    pageState.value.uploadImageList = modelValue;
  }
});

watch([() => modelValue, () => fileType], () => {
  if (Array.isArray(modelValue) && modelValue.length !== 0) {
    pageState.value.uploadImageList = modelValue;
  }
});

const customRequest = (option: any) => {
  pageState.value.uploadLoading = true;

  const token = getToken();
  // pageState.value.uploadLoading = true;
  const { onProgress, onError, onSuccess, fileItem, name } = option;
  const xhr = new XMLHttpRequest();
  if (xhr.upload) {
    xhr.upload.onprogress = function (event) {
      let percent;
      if (event.total > 0) {
        // 0 ~ 1
        percent = event.loaded / event.total;
      }
      onProgress(percent, event);
    };
  }
  xhr.onerror = function error(e) {
    onError(e);
  };
  xhr.onload = function onload() {
    if (xhr.status < 200 || xhr.status >= 300) {
      onError(xhr.responseText);
    } else {
      const res = JSON.parse(xhr.response);
      if (res.code === "1") {
        pageState.value.uploadImageList =
          pageState.value.uploadImageList.concat({
            uri: res.data.threeFilePath,
            ...res.data,
          });
        emit("update:modelValue", pageState.value.uploadImageList);
        onSuccess(xhr.response);
      } else {
        onError(xhr.responseText);
      }
    }
    pageState.value.uploadLoading = false;
  };

  const formData = new FormData();
  formData.append(name || "file", fileItem.file);
  if (fileType) {
    formData.append("fileType", fileType);
  }
  // xhr.open('post', `${import.meta.env.VITE_API_BASE_URL}/base/upload/uploadImgMin`, true);
  xhr.open(
    "post",
    `${import.meta.env.VITE_API_BASE_URL}/base/upload/uploadImgMin`,
    true,
  );

  xhr.setRequestHeader("authorization", `Bearer ${token}`);
  xhr.setRequestHeader("accesstoken", `${token}`);
  xhr.send(formData);

  return {
    abort() {
      xhr.abort();
    },
  };
};

const onDeleteImage = (index: number) => {
  pageState.value.uploadImageList = pageState.value.uploadImageList.filter(
    (item, i) => i !== index,
  );
  emit("update:modelValue", pageState.value.uploadImageList);
};
</script>

<style lang="scss" scoped>
:global(.arco-image-footer-extra) {
  padding-left: 0 !important;
}

.file-upload-wrapper {
  display: "flex";
  flex-wrap: "wrap";
  column-gap: "16px";
  row-gap: "16px";
  .image-wrapper {
    &:hover {
      .actions {
        visibility: visible;
        opacity: 1;
      }
    }
  }

  .upload-item {
    display: inline-block;
    width: 80px;
    // height: 100px;
    overflow: hidden;
    margin-right: var(--spacing-7);
    .file-name {
      display: block;
      width: 80px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.actions {
  width: 100%;
  height: 100%;
  display: flex;
  padding: 9px 16px;
  background: rgba(29, 33, 41, 0.6);
  justify-content: space-between;
  align-items: center;
  visibility: hidden;
  opacity: 0;
  .iconStyle {
    font-size: 20px;
    cursor: pointer;
  }
}
</style>
