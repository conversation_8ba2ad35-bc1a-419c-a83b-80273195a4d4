<!--
 * @Description: 医生信息选择表
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-07-15 18:44:34
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-26 20:34:07
-->
<template>
  <!-- @vue-expect-error 类型不匹配的错误 -->
  <a-table
    v-model:selected-keys="selectedKeys"
    row-key="templateId"
    :columns="columns"
    :data="tableData"
    :row-selection="rowSelection"
    :pagination="false"
    @selection-change="handleSelectionChange"
  >
    <template #name="{ record }">
      <span>{{ record.docType === EDocType.Question ? record.docName : record.typeName }}</span>
    </template>
    <template #operation="{ record }">
      <div class="opr">
        <!-- <a-button type="outline" size="mini" @click="handlePush(record)">推送</a-button> -->
        <a-button v-if="quickAccessStore.patientIds.length === 1" type="outline" size="mini" @click="handleCopy(record)">复制</a-button>
      </div>
    </template>
  </a-table>
</template>

<script setup lang="ts">
import { postBdManageApiDocQueryDocTemplateList, postBdManageApiFlowPush, QueryDocTemplateListUsingPOSTQueryTemplateListRespVo } from '@/batchApi';
import { useQuickAccessStore } from '@/store';
import { EDocType } from '@/types/enums';
import { getToken } from '@/utils/auth';
import getPdfPath from '@/utils/form';
import { Message } from '@arco-design/web-vue';
import { computed, onMounted, reactive, ref } from 'vue';

const emits = defineEmits(['select'])

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});

const {
  selectIds = []
} = defineProps<{
  selectIds?: (string | number)[]
}>()

const quickAccessStore = useQuickAccessStore();

const selectedKeys = ref<(string | number)[]>([]);

const columns = [
  {
    title: '表单名称',
    dataIndex: 'name',
    slotName: 'name'
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
  {
    title: '操作',
    dataIndex: 'userTitle',
    slotName: "operation"
  },
]

const tableData = ref<QueryDocTemplateListUsingPOSTQueryTemplateListRespVo[]>([]);

onMounted(() => {
  getServiceMember();
})

// const handlePush = (_record: QueryDocTemplateListUsingPOSTQueryTemplateListRespVo) => {
//   postBdManageApiFlowPush({
//     channelCode: 'PATIENT_IOS',
//     patientIds: quickAccessStore.patientIds,
//     // @ts-expect-error
//     templateIds: [_record.templateId]
//   }).then(_res => {
//     console.log('_res', _res)
//     Message.success("推送成功")
//   })
// }

const handleCopy = (_record: QueryDocTemplateListUsingPOSTQueryTemplateListRespVo) => {
  // 这里需要把文本复制到系统的剪贴板上面
  const str = handleFormAddress({
    formId: _record.formId as unknown as string,
    schemaId: _record.schemaId as unknown as string,
    formName: _record.docName as unknown as string,
  });
  if (navigator && navigator.clipboard && navigator.clipboard.writeText) {
    navigator.clipboard.writeText(str).then(() => {
      Message.success('复制成功');
    }).catch(() => {
      Message.error('复制失败，请手动复制');
    });
  } else {
    // 兼容性处理：使用旧的 document.execCommand
    const input = document.createElement('input');
    input.value = str;
    document.body.appendChild(input);
    input.select();
    try {
      document.execCommand('copy');
      Message.success('复制成功');
    } catch (e) {
      Message.error('复制失败，请手动复制');
    }
    document.body.removeChild(input);
  }
}

const handleFormAddress = ({
  formId,
  schemaId,
  formName
}: {
  formId: string,
  schemaId: string,
  formName: string
}) => {
  const urlSearch = new URLSearchParams()
  let address = "";
  urlSearch.append('thirdPlatform', 'bd'); // 定义当前是第三方平台 北大使用编辑页面
  urlSearch.append('formId', formId); // 定义当前是第三方平台 北大使用编辑页面
  urlSearch.append('schemaId', schemaId);
  urlSearch.append('formVersion', "1");
  urlSearch.append('formName', formName);
  urlSearch.append('patientId', quickAccessStore.patientIds[0]);
  urlSearch.append('token', getToken());
  // address = `${import.meta.env.VITE_FORM_DESIGN_BASE_URL}/#/design?${urlSearch.toString()}`
  address = `${window.location.origin}/bd-form-engine/#/formInstance?${urlSearch.toString()}`
  return address;
}

const handleSelectionChange = (
  _rowKeys: (string | number)[]
) => {
  selectedKeys.value = _rowKeys;
  emits("select", _rowKeys);
  console.log('_rowKeys', _rowKeys)
}

const getServiceMember = () => {
  postBdManageApiDocQueryDocTemplateList({
    docType: EDocType.Question
  }).then(_res => {
    console.log('_res', _res)
    if (
      Array.isArray(_res.data)
    ) {
      tableData.value = _res.data;
    }
  })
}
</script>

<style scoped lang="scss">
.opr {
  display: flex;
  gap: 8px;
}

</style>