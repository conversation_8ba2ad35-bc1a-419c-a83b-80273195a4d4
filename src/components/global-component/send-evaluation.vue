<!--
 * @Description: 推送表单的内容
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-07-17 23:37:28
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 20:03:58
-->
<template>
  <a-drawer
    unmount-on-close
    :width="980"
    :visible="visible"
    :mask-closable="false"
    :on-before-ok="handleOnOk"
    @cancel="handleCancel"
  >
    <template #title>
      推送问卷
    </template>
    <template #footer>
      <div class="footer">
        <div class="count">
          <span>已选择</span>
          <span>{{ selectKeys.length }}</span>
          <span>项</span>
        </div>
        <div class="btn">
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handlePush">推送</a-button>
        </div>
      </div>
    </template>
    <!-- 服务预约的内容 -->
    <a-spin
      :style="{
        width: '100%',
      }"
      :loading="false"
    >
      <evaluation-table
        @select="handleSelect"
      />
    </a-spin>
  </a-drawer>
</template>

<script setup lang="ts">
import useQuickAccessStore from "@/store/modules/quick-access";
import { useAppStore } from '@/store';
import { ref, watch } from 'vue';
import { postBdManageApiFlowPush } from '@/batchApi';
import { Message } from '@arco-design/web-vue';
import evaluationTable from './components/evaluation-table.vue';

const visible = ref(false);
const selectKeys = ref<string[]>([]);
const quickAccessStore = useQuickAccessStore();

watch(
  () => quickAccessStore.quickAccessState.showSendEvaluation,
  () => {
    visible.value = quickAccessStore.quickAccessState.showSendEvaluation;
  },
);

const handleSelect = (_selectKey: string[]) => {
  selectKeys.value = _selectKey;
}

const handleOnOk = async (): Promise<boolean> => {
  return Promise.resolve(false);
}

const handleCancel = () => {
  // 重置所有的表单数据
  quickAccessStore.toggleSendEvaluation(false);
};

const handlePush = () => {
  postBdManageApiFlowPush({
    channelCode: 'PATIENT_IOS',
    patientIds: quickAccessStore.patientIds,
    templateIds: selectKeys.value as unknown as number[]
  }).then(_res => {
    console.log('_res', _res)
    Message.success("推送成功")
    handleCancel();
  })
}

</script>

<style scoped lang="scss">
.footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  >.count {

  }
  >.btn {
    display: flex;
    gap: 8px;
  }
}
</style>