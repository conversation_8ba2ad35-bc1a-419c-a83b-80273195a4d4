<!--
* @description 发送短信全局
* @fileName sendSMS.vue
* <AUTHOR>
* @date 2025/07/10 10:07:44
!-->
<script setup lang="ts">
import { FormInstance, Message } from "@arco-design/web-vue";
import { ref, reactive, onUpdated, nextTick, watch } from "vue";
import useQuickAccessStore from "@/store/modules/quick-access";
import * as apis from "@/batchApi";
import to from "await-to-js";
import { storeToRefs } from "pinia";
import { CustomTemplateIdEnum } from "@/views/sms-logs/config";

type ISMSRecord = apis.ListUsingPOST_2SmsTemplate;
type IUsefulRecord = apis.ListUsingPOST_3SmsUsefulPhrases;

interface IState {
  loading: boolean;
  editStatus: boolean;
  editText: string | undefined;
  form: apis.postBdManageApiSmsRecordSendSmsBatchRequest;
  smsTemplateList: ISMSRecord[];
  usefulList: IUsefulRecord[];
  variableList: {
    text: string;
    isEdit: boolean;
    value?: string; // 添加变量值字段
  }[];
  variableValues: Record<string, string>; // 存储变量值对象
}

const quickAccessStore = useQuickAccessStore();
const { showSendSms } = storeToRefs(quickAccessStore);
const modalFormRef = ref<FormInstance>();
const textareaRef = ref(); // 直接引用textarea组件

// 存储光标位置
const cursorPosition = ref(0);

const pageState = reactive<IState>({
  loading: false,
  editStatus: false,
  editText: undefined,
  form: {},
  smsTemplateList: [],
  usefulList: [],
  variableList: [],
  variableValues: {}, // 存储变量值的对象
});

// 更新变量值
const handleVariableChange = (variable: string, value: string) => {
  pageState.variableValues[variable] = value;
  // 强制触发更新
  pageState.variableList = [...pageState.variableList];
};

// 更新光标位置的方法
const updateCursorPosition = () => {
  nextTick(() => {
    try {
      const textarea = textareaRef.value?.$el.querySelector("textarea");
      if (textarea) {
        cursorPosition.value = textarea.selectionEnd || 0;
      }
    } catch (e) {
      console.error("获取光标位置失败", e);
    }
  });
};

// 获取处理后的短信内容
const getProcessedContent = (): string => {
  // 根据原始模板和变量值生成最终内容
  const template =
    pageState.smsTemplateList.find(
      item => item?.code === pageState.form.templateId,
    )?.tempContent || "";

  let content = template;

  // 替换所有变量
  Object.entries(pageState.variableValues).forEach(([key, value]) => {
    const regex = new RegExp(`{{${key}}}`, "g");
    content = content.replace(regex, value || "");
  });

  return content;
};

const handleOk = async (): Promise<boolean> => {
  const modalError = await modalFormRef.value?.validate();
  return new Promise(resolve => {
    if (modalError) {
      resolve(false);
      return;
    }
    if (!quickAccessStore.patientIdSMSList?.length) {
      Message.info("请选择患者");
      return;
    }

    // 检查是否有未填写的变量
    const editableVariables = pageState.variableList.filter(
      item => item.isEdit,
    );
    const missingVariables = editableVariables.filter(
      item => !pageState.variableValues[item.text],
    );

    if (missingVariables.length > 0) {
      Message.warning(
        `请填写以下变量: ${missingVariables.map(v => v.text).join(", ")}`,
      );
      resolve(false);
      return;
    }
    const params = {
      ...pageState.form,
      params: pageState.variableValues, // 使用处理后的内容
      patients: quickAccessStore.patientIdSMSList,
    } as apis.postBdManageApiSmsRecordSendSmsBatchRequest;
    apis
      .postBdManageApiSmsRecordSendSmsBatch(params)
      .then(res => {
        if (res?.code === "1") {
          const resultTest = `发送成功数量:${res?.data?.successCount ?? 0};发送失败数量${res?.data?.failCount ?? 0}`;
          cancel();
          Message.success({
            content: resultTest,
            onClose: () => {
              quickAccessStore.toggleSendSMS(false, []);
              resolve(true);
            },
          });
        }
      })
      .catch(() => {
        resolve(false);
      });
  });
};

const handleDel = async (record: IUsefulRecord) => {
  const [err, res] = await to(
    apis.getBdManageApiUsefulDelete({ id: record?.id as number }),
  );
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    getUsefulList();
  }
};

const handleClick = (item: IUsefulRecord) => {
  if (pageState.editStatus) return;
  // 复制内容到剪贴板
  try {
    const text = item?.value || "";
    navigator.clipboard
      .writeText(text)
      .then(() => {
        Message.success("复制成功");
      })
      .catch(() => {
        Message.error("复制失败");
      });
  } catch (error) {
    Message.error("复制失败");
  }
};

const handleAddTag = async () => {
  if (!pageState.editText) {
    Message.info("内容不能为空");
    return;
  }
  const [err, res] = await to(
    apis.postBdManageApiUsefulAdd({
      value: pageState.editText,
    }),
  );
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    getUsefulList();
    pageState.editText = undefined;
  }
};

/* 获取模版 */
const getSMSTemplate = async () => {
  const [err, res] = await to(apis.postBdManageApiSmsTemplateList());
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    pageState.smsTemplateList = res?.data as ISMSRecord[];
  }
};

/* 常用语 */
const getUsefulList = async () => {
  const [err, res] = await to(apis.postBdManageApiUsefulList());
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    pageState.usefulList = res?.data as ISMSRecord[];
  }
};

const handleChange = (value: any) => {
  const record = pageState.smsTemplateList?.find(
    (item: ISMSRecord) => item?.code === value,
  );
  const templateContent = record?.tempContent || "";
  const strList = templateContent
    ? templateContent
        ?.match(/{{([^}]+)}}|[^{{]+/g)
        ?.map((item: string) => item?.trim())
        .filter(Boolean)
    : [];

  const variableList = strList?.map((item: string) => {
    const isEdit = item.startsWith("{{") && item.endsWith("}}");
    const text = isEdit ? item.slice(2, -2) : item;
    return {
      text,
      isEdit, // 仅当是变量时isEdit为true
      value: "",
    };
  });
  pageState.variableList = variableList || [];

  // 重置变量值对象
  pageState.variableValues = {};

  // 初始化变量值对象
  pageState.variableList.forEach(item => {
    if (item.isEdit) {
      pageState.variableValues[item.text] = "";
    }
  });

  cursorPosition.value = 0;
};

const cancel = () => {
  modalFormRef.value?.resetFields();
  pageState.variableList = [];
  pageState.smsTemplateList = [];
  pageState.usefulList = [];
  pageState.variableValues = {};
  quickAccessStore.toggleSendSMS(false, []);
};

onUpdated(() => {
  if (showSendSms.value) {
    // 模版列表
    getSMSTemplate();
    // 常用语
    getUsefulList();
  }
});
</script>

<template>
  <a-modal
    v-model:visible="showSendSms"
    :width="680"
    title="短信编辑"
    @before-ok="handleOk"
    @cancel="cancel"
  >
    <div>
      <a-form
        ref="modalFormRef"
        :model="pageState.form"
        :label-col-props="{ span: 5 }"
        :wrapper-col-props="{ span: 18 }"
        label-align="right"
        layout="vertical"
      >
        <a-form-item
          field="templateId"
          label="选择模板"
          :rules="[
            {
              required: true,
              message: '请选择',
            },
          ]"
        >
          <a-select
            v-model="pageState.form.templateId"
            placeholder="请选择"
            @change="handleChange"
          >
            <a-option
              v-for="item in pageState.smsTemplateList"
              :key="item?.id"
              :value="item?.code"
              >{{ item?.name }}</a-option
            >
          </a-select>
        </a-form-item>
        <a-form-item
          v-if="pageState.variableList.length"
          field="params"
          label="编辑短信"
          :rules="[
            {
              required: false,
              message: '请输入短信内容',
            },
          ]"
        >
          <div class="template-content">
            <template
              v-for="(item, index) in pageState.variableList"
              :key="index"
            >
              <span v-if="!item.isEdit">{{ item.text }}</span>
              <a-typography-paragraph
                v-else
                v-model:edit-text="pageState.variableValues[item.text]"
                editable
                class="variable-paragraph"
              >
                {{
                  pageState.variableValues[item.text] || `\{\{${item.text}\}\}`
                }}
              </a-typography-paragraph>
            </template>
          </div>
          <!-- 移除预览内容 -->
        </a-form-item>
        <!-- <a-form-item
          v-if="
            (pageState.form.templateId as unknown as string) ===
            CustomTemplateIdEnum.custom
          "
          field="addUrl"
          label="是否为短链接"
          :rules="[
            {
              required: false,
              message: '是否为短链接',
            },
          ]"
        >
          <a-switch v-model="pageState.form.addUrl" />
        </a-form-item> -->
      </a-form>
      <div class="commonPhrasesView">
        <div class="title">
          <span>常用语</span>
          <span @click="pageState.editStatus = !pageState.editStatus">
            <icon-edit />{{ pageState.editStatus ? "收起" : "编辑" }}</span
          >
        </div>
        <div class="tagView">
          <span
            v-for="item in pageState.usefulList"
            :key="item.id"
            class="tag"
            @click.stop="handleClick(item)"
            >{{ item?.value }}&nbsp;
            <icon-copy v-if="!pageState.editStatus" />
            <icon-close
              v-if="pageState.editStatus"
              @click.stop="handleDel(item)"
          /></span>
        </div>
      </div>
      <a-input
        v-if="pageState.editStatus"
        v-model="pageState.editText"
        placeholder="请输入常用语"
        allow-clear
      >
        <template #suffix>
          <span class="addText" @click="handleAddTag"
            ><icon-plus /> &nbsp;添加</span
          >
        </template>
      </a-input>
    </div>
  </a-modal>
</template>

<style lang="scss" scoped>
:deep(.arco-typography-edit-content) {
  margin-bottom: 0 !important;
  margin-left: 12px;
}
.commonPhrasesView {
  .title {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #3d3d3d;
    margin-bottom: 8px;
    > span:last-child {
      color: #86909c;
    }
  }
  .tagView {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px 12px;
    margin-bottom: 16px;

    .tag {
      display: flex;
      align-items: center;
      height: 24px;
      padding: 0 8px;
      border-radius: 12px;
      background: #f2f3f5;
      font-size: 12px;
      color: #000;
      cursor: pointer;
      > svg {
        cursor: pointer;
      }
    }
  }
}
.addText {
  height: 24px;
  display: flex;
  align-items: center;
  background: #fff;
  padding: 0px 8px;
  font-size: 12px;
  color: #0052d9;
  border-radius: 12px;
}

/* 模板内容样式 */
.template-content {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  padding: 8px;
  min-height: 120px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  line-height: 1.5;
  color: #1d2129;
}

.variable-wrapper {
  display: inline;
  margin: 0;
}

.variable-paragraph {
  display: inline;
  min-width: auto;
  padding: 0;
  margin: 0;
  border: none;
  background-color: transparent;
  cursor: pointer;

  &:hover {
    background-color: transparent;
    text-decoration: underline;
  }
}

/* 移除预览内容样式 */
</style>
