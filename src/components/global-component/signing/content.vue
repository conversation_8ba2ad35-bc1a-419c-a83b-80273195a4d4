<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2025-03-28 10:31:59
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-19 09:59:04
-->
<template>
  <div class="signing-container">
    <!-- 左边数据录入的内容 -->
    <!-- :wrapper-col-props="{ span: 17, offset: 0 }" -->
    <a-form
      ref="contentFormRef"
      class="base-info"
      :model="form"
      :label-col-props="{ span: 8, offset: 0 }"
      :wrapper-col-props="{ span: 16, offset: 0 }"
    >
      <div class="title">
        签约信息
      </div>
      <a-row :gutter="35">
        <!-- 展示身份证的头像信息 -->
        <a-col
          :span="4"
        >
          <a-button
            type="primary"
            @click="handleReadCard"
          >
            读取身份证
          </a-button>
          <!-- 展示当前身份证上的图片 -->
          <div
            :style="{
              backgroundColor: 'gray',
              height: '136px'
            }"
          >
            <img v-if="form.avatar" :src="form.avatar" width='100%' height='136px' alt="">
          </div>
        </a-col>
        <a-col :span="20">
          <div class="grid-container">
            <a-form-item
              field="idCard"
              label="身份证号"
              :rules="validationRules.idCard(true)"
            >
              <a-input
                ref="cardRef"
                v-model="form.idCard"
                placeholder="请输入身份证号"
                @blur="handleIdCardBlur"
              >
                <template #suffix>
                  <icon-idcard />
                </template>
              </a-input>
            </a-form-item>
            <a-form-item
              field="birthdate"
              label="出生日期"
              :rules="[
                { required: true, message: '请输入出生日期' },
              ]"
              :disabled="disabledEdit"
            >
              <a-date-picker
                v-model="form.birthdate"
                :disabled-date="(current) => dayjs(current).isAfter(dayjs())"
                style="width: 100%"
                placeholder="请输入出生日期"
                
              />
            </a-form-item>
            <a-form-item
              field="name"
              label="姓名"
              :rules="[
                { required: true, message: '请输入姓名' },
              ]"
            >
              <a-input
                v-model="form.name"
                :tabindex="1"
                placeholder="请输入姓名"
              >
                <template #suffix>
                  <icon-user />
                </template>
              </a-input>
            </a-form-item>
            <a-form-item
              field="gender"
              label="性别"
              :rules="[
                { required: true, message: '请选择性别' },
              ]"
              :disabled="disabledEdit"
            >
              <a-select
                v-model="form.gender"
                :tabindex="2"
                placeholder="请选择性别"
              >
                <a-option :value="1">男</a-option>
                <a-option :value="2">女</a-option>
                <a-option :value="3">未知</a-option>
              </a-select>
            </a-form-item>
            
            <a-form-item
              field="nation"
              label="民族"
              :rules="[
                { required: true, message: '请选择民族' },
              ]"
            >
              <a-select
                v-model="form.nation"
                placeholder="请选择民族"
              >
                <a-option
                  v-for="item, index in nationList"
                  :key="index"
                  :value="item.dictValue"
                >
                  {{ item.dictLabel }}
                </a-option>
              </a-select>
            </a-form-item>
            
            <a-form-item
              field="contactMobile"
              label="本人电话"
              :rules="validationRules.mobile"
            >
              <a-input
                v-model="form.contactMobile"
                placeholder="请输入本人电话"
              >
                <template #suffix>
                  <icon-phone />
                </template>
              </a-input>
            </a-form-item>
          </div>
          <a-form-item
            field="address"
            label="户籍地址"
            :label-col-props="{ span: 4 }"
            :wrapper-col-props="{ span: 20 }"
          >
            <a-input
              v-model="form.address"
              placeholder="请输入户籍地址"
            />
          </a-form-item>
          <a-form-item
            field="addressDetail"
            label="地址详情"
            :label-col-props="{ span: 4 }"
            :wrapper-col-props="{ span: 20 }"
          >
            <a-input
              v-model="form.addressDetail"
              placeholder="请输入地址详情"
            />
          </a-form-item>
          <a-row>
            <a-col :span="12">
              <a-form-item
                field="teamId"
                label="医生团队"
                :rules="[
                  { required: true, message: '请选择医生团队' },
                ]"
              >
                <a-select
                  v-model="form.teamId"
                  placeholder="请选择医生团队"
                >
                  <a-option
                    v-for="item in teamList"
                    :key="item.teamId"
                    :value="item.teamId"
                  >
                    {{ item.name }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                field="doctorId"
                label="签约医生"
                :rules="[
                  { required: true, message: '请选择签约医生' },
                ]"
              >
                <a-select
                  v-model="form.doctorId"
                  placeholder="请选择签约医生"
                >
                  <a-option
                    v-for="item in doctorList"
                    :key="item.userId"
                    :value="item.userId"
                  >
                    {{ item.name }}
                  </a-option>
                </a-select>
              </a-form-item>        
            </a-col>
          </a-row>
        </a-col>
      </a-row>
    </a-form>
    <!-- 右边展示服务包选择的内容 -->
    <div class="right-info">
      <div class="title">
        新签/续签病种
      </div>
      <div class="package-info">
        <package-item
          v-for="item, index in packageList"
          :key="index"
          :package-name="item.name"
          :package-code="item.serviceCode?.replace('JY_FOLLOW_', '')"
          :package-status="item.serviceStatus"
          :checked="packageSelected.includes(item.serviceCode as string)"
          @click="() => handleClick(item.serviceCode as string)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  nextTick,
  onMounted,
  onUnmounted,
  reactive,
  ref,
  watch,
  computed
} from 'vue';
import { FormInstance, Message } from '@arco-design/web-vue';
import dayjs from 'dayjs';
import to from 'await-to-js';
import {
  validationRules,
  validateIdCard
} from '@/utils/validator'
import { parseIdCard } from '@/utils/common';
import { getBdManageApiPackageList, getBdManageApiPackageListResponse, getBdManageApiUserTeamList, getBdManageApiUserTeamListResponse, getBdManageApiUserUserList, getBdManageApiUserUserListResponse, getBdManageApiSystemDictDataTypeByDictType, getBdManageApiSystemDictDataTypeByDictTypeResponse, postBdManageApiSignGetSignedInfoByIdCard, postBdManageApiPackageListByPatientId, GetPackageListUsingGETRightsItem } from '@/batchApi';
import {
  useUserStore
} from '@/store'
import packageItem from './package-item.vue';


interface IPackageItem extends GetPackageListUsingGETRightsItem {
  serviceStatus?: number
}

const disabledEdit = ref(false);
const cardRef = ref();
const contentFormRef = ref<FormInstance>()
const packageSelected = ref<string[]>([])
const packageList = ref<IPackageItem[]>([])
const webSocketRef = ref<WebSocket>();
const userStore = useUserStore();

const {
  idCard
} = defineProps<{
  idCard: string
}>()

const teamList = ref<getBdManageApiUserTeamListResponse['data']>([])
const doctorList = ref<getBdManageApiUserUserListResponse['data']>([])
const nationList = ref<getBdManageApiSystemDictDataTypeByDictTypeResponse['data']>([])
const genderList = ref<getBdManageApiSystemDictDataTypeByDictTypeResponse['data']>([])

const form = reactive({
  patientId: undefined,
  name: '',
  birthdate: '',
  idCard: '',
  gender: '',
  nation: '',
  contactMobile: '',
  address: '',
  addressDetail: '',
  teamId: '',
  doctorId: '',
  avatar: '',
})

// 根据选中的团队数据进行变化
watch(() => form.teamId, () => {
  getUserListByTeamId(form.teamId)
});

// 失去焦点的时候 通过身份证获取当前患者的签约数据
const handleIdCardBlur = () => {
  console.log('blur_idCard', form.idCard);
  if (
    form.idCard
  ) {
    postBdManageApiSignGetSignedInfoByIdCard({
      idCard: form.idCard
    }).then(_res => {
      const userInfo = _res.data;
      console.log('_res', _res, userInfo?.rightsItemList)
      if (
        userInfo?.patientId
      ) {
        // 基本信息的填充
        form.name = userInfo.name ?? ''
        form.birthdate = userInfo.birthdate ?? ''
        form.gender = userInfo.gender as unknown as string
        form.contactMobile = userInfo.contactMobile ?? ''
        form.address = userInfo.address ?? ''
        form.addressDetail = userInfo.addressDetail ?? ''
        form.teamId = userInfo.teamId as unknown as string
        form.doctorId = userInfo.doctorUserId as unknown as string
        form.nation = userInfo.nation ?? '';
        form.avatar = userInfo.avatar ?? '';
        if (
          form.birthdate && form.gender
        ) {
          // 通过解析身份证得到的 出生日期 和 性别 不能进行下修改
          disabledEdit.value = true;
        }
        if (
          userInfo.patientId
        ) {
          // @ts-ignore 
          form.patientId = userInfo.patientId;
        }
        // 填充患者已经 签约的信息
      } else {
        // 没有患者的签约信息 根据现在的身份证解析基础的信息
        const idCardInfo = parseIdCard(form.idCard);
        if (idCardInfo) {
          form.birthdate = idCardInfo.birthDay;
          form.gender = Number(idCardInfo.sex) as unknown as string;
          if (
            form.birthdate && form.gender
          ) {
            // 通过解析身份证得到的 出生日期 和 性别 不能进行下修改
            disabledEdit.value = true;
          }
        } else {
          Message.error('身份证号格式不正确');
          form.birthdate = '';
          form.gender = '';
        }
      }
      // 使用患者id获取服务包的状态
      if (
        userInfo?.patientId
      ) {
        postBdManageApiPackageListByPatientId({
          patientId: userInfo?.patientId
        }).then(_res => {
          console.log('_res', _res)
          if (
            _res.data
          ) {
            // 给当前的 packageList 中的数据 增加一个状态
            _res.data.forEach(_item => {
              if (
                _item.packageCode
              ) {
                packageSelected.value.push(_item.packageCode)
              }
              const packageIndex = packageList.value?.findIndex(_fi => _fi.serviceCode === _item.packageCode) ?? -1;
              if (
                packageIndex !== -1 && packageList.value
              ) {
                const newPackageList = JSON.parse(JSON.stringify(packageList.value))
                newPackageList[packageIndex] = {
                  ...newPackageList[packageIndex],
                  serviceStatus: _item.packageStatus
                }
                packageList.value = newPackageList;
                console.log('newPackageList', newPackageList)
              }
            })
            console.log('packageList', packageList.value, _res.data)
          }
        })
      }
    })
  }

}

const handleClick = (_packageCode: string) => {
  const selectIndex = packageSelected.value.findIndex(_fi => _fi === _packageCode)
  if (
    selectIndex !== -1
  ) {
    packageSelected.value.splice(selectIndex, 1);
  } else {
    packageSelected.value.push(_packageCode);
  }
  console.log('packageSelected.value', packageSelected.value)
}

// 点击调用读卡器 读取身份证信息
const handleReadCard = () => {
  if(!window.WebSocket) {
    Message.info("该版本浏览器不支持WebSocket"); 
    return 
  }
  let webSocket: WebSocket;
  if (
    webSocketRef.value
  ) {
    webSocket = webSocketRef.value;
    webSocket.send("{ \"MethodName\": \"OpenDevice\" }");
    webSocket.send("{ \"MethodName\": \"ReadIDCard\"}");
  } else {
    webSocket = new WebSocket("ws://127.0.0.1:6932/")
    webSocket.onmessage = (evt) => {
      let res = evt.data;
      try {
        res = JSON.parse(res)
        if (
          res?.MethodName === 'ReadIDCard' &&
          res?.RespCode === 0
        ) {
          const cardInfo = res?.RespData;
          // CardType 读卡器返回的有卡片类型
          form.name = cardInfo?.Name;
          form.birthdate = dayjs(cardInfo?.Birthday).format('YYYY-MM-DD');
          form.idCard = cardInfo?.CardId;
          form.address = cardInfo?.Address;
          form.avatar = cardInfo?.PictureData;
          // 读取身份证之后 相关的数据 应该是空的 需要重新设置
          form.gender = '';
          form.nation = '';
          form.contactMobile = '';
          form.addressDetail = '';
          nextTick(() => {
            // 设置完当前的值 之后给身份证一个焦点
            cardRef.value?.focus()
          })
          console.log('cardInfo', cardInfo)
          // form.gender = cardInfo?.Sex;
          // form.nation = cardInfo?.Nation;
          // 用完之后点击关闭
          // websocket.close()
        }
      } catch (error) {
        console.error('error', error)
      }
    }
    webSocket.onopen = () => {
      console.log('WebSocket connection established');
      webSocket.send("{ \"MethodName\": \"OpenDevice\" }");
      webSocket.send("{ \"MethodName\": \"ReadIDCard\"}");
    };
  }
  // Message.info("身份证设备未对接")
}

const getUserListByTeamId = async (
  _teamId: string
) => {
  const [err, res] = await to(getBdManageApiUserUserList({ teamId: _teamId as unknown as number }))
  if (
    err
  ) {
    throw err;
  }
  if (
    Array.isArray(res.data)
  ) {
    doctorList.value = res.data;
  }
  console.log('userList', res)
}

const initData = async () => {
  const [err, res] = await to(getBdManageApiUserTeamList({}))
  if (
    err
  ) {
    throw err;
  }
  if (
    Array.isArray(res.data)
  ) {
    teamList.value = res.data;
  }
  console.log('TeamListRes', res)
}

const handleDictData = async (
  _dictType: string
) => {
  const [err, res] = await to(getBdManageApiSystemDictDataTypeByDictType({
    dictType: _dictType
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    res.data
  ) {
    return res.data
  }
  return []
}

const getPackageList = async () => {
  const [err, res] = await to(getBdManageApiPackageList())
  if (
    err
  ) {
    throw err;
  }
  if (
    Array.isArray(res.data)
  ) {
    packageList.value = res.data;
  }
  console.log('_res', res)
}

onMounted(async () => {
  cardRef.value?.focus()
  await initData();
  // 获取没有状态的服务包列表
  await getPackageList();
  // 获取民族的枚举
  nationList.value = await handleDictData('nation_type')
  // 获取性别的枚举
  genderList.value = await handleDictData('gender_type')
  // 如果从上层组件 过来是有身份证的 给当前的表单设置身份证
  if (
    typeof idCard === 'string' &&
    idCard !== ''
  ) {
    form.idCard = idCard;
  }
  // 赋值基础的团队和医生的信息
  if (
    typeof userStore.accountInfo.teamId === 'string' &&
    userStore.accountInfo.teamId !== ''
  ) {
    form.teamId = userStore.accountInfo.teamId;
  }
  if (
    typeof userStore.accountInfo.userId === 'string' &&
    userStore.accountInfo.userId !== ''
  ) {
    form.doctorId = userStore.accountInfo.userId;
  }
})

onUnmounted(() => {
  if (
    webSocketRef.value
  ) {
    webSocketRef.value.close();
  }
})

// 定义导出的内容
defineExpose({
  formInstance: contentFormRef,
  rightsCodeList: packageSelected.value
})

</script>

<style lang="scss" scoped>
.signing-container {
  width: 1100px;
  display: flex;
  column-gap: 16px;
  .grid-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }
  // 基本信息 840px
  >.base-info {
    flex: 1;
    button {
      width: 100%;
      margin-bottom: 16px;
    }
  }
  >.right-info {
    padding-left: 10px;
    border-left: 1px dotted #000;
    // 服务包信息
    >.package-info {
      width: 250px;
      height: 300px;
      overflow-y: scroll;
    }
  }
  .title {
    font-weight: bold;
    margin-bottom: var(--spacing-7);
  }
}
</style>