<!--
 * @Description: 每一个服务包的组件
 * @Author: yangrong<PERSON>
 * @Date: 2025-03-28 14:01:12
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-04-25 09:36:30
-->
<template>
  <div
    class="package-item-container"
    @click="$emit('click')"
  >
    <!-- 当前服务包的签约状态 -->
    <div
      v-if="packageStatus === 3"
      class="sign-type un-sign-type"
    >
      未签约
    </div>
    <div
      v-if="packageStatus === 1"
      class="sign-type signed-type"
    >
      已签约
    </div>
    <div
      v-if="packageStatus === 2"
      class="sign-type renewal"
    >
      待续签
    </div>
    <!-- 服务包的名称 -->
    <div class="package-info">
      <!-- {{ packageCode }} -->
      {{ packageName }}
    </div>
    <!-- 当前选中的标识 -->
    <div
      v-if="checked"
      class="package-checked"
    >
      <icon-check />
    </div>
  </div>
</template>

<script setup lang="ts">

const {
  packageName = '糖尿病',
  packageCode = 'TNB',
  packageStatus = 3,
  checked = false
} = defineProps([
  'packageName',
  'packageCode',
  'packageStatus',
  'checked'
])

const emits = defineEmits([
  'click'
])

</script>

<style lang="scss" scoped>
.package-item-container {
  position: relative;
  cursor: pointer;
  padding: var(--spacing-3) var(--spacing-6);
  background-color: var(--disabled-bg-color);
  border-radius: var(--border-radius-small);
  margin-bottom: var(--spacing-7);
  >.sign-type {
    position: absolute;
    top: 0px;
    left: 0px;
    font-size: var(--font-size-caption);
    background-color: var(--a-package-bgc);
    border-radius: var(--border-radius-small);
    // writing-mode: tb;
    padding: 7px var(--spacing-3);
    color: white;
  }
  >.un-sign-type {
    background-color: var(--dark-gray-4);
  }
  >.signed-type {
    background-color: var(--a-package-bgc);
  }
  >.renewal {
    background-color: var(--text-warn-color);
  }
  >.package-info {
    font-weight: bold;
    text-indent: var(--spacing-13);
  }
  // 服务包信息
  >.package-checked {
    color: white;
    width: 20px;
    height: 20px;
    text-align: center;
    position: absolute;
    right: 0;
    bottom: 0;
    background-color: var(--d-package-bgc);
    border-top-left-radius: var(--border-radius-large);
    border-bottom-right-radius: var(--border-radius-small);
  }
}
</style>