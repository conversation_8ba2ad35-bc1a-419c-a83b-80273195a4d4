<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2025-03-28 10:16:16
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-05-28 18:06:36
-->
<!-- @ok="handleOk" -->
<template>
  <a-modal
    v-model:visible="visible"
    title-align="start"
    width="auto"
    draggable
    unmount-on-close
    :on-before-ok="handleBeforeOk"
    @cancel="handleCancel"
  >
    <template #title>
      客户签约
    </template>
    <content
      ref="contentRef"
      :id-card="idCard"
    />
  </a-modal>
</template>

<script setup lang="ts">
import useQuickAccessStore from '@/store/modules/quick-access';
import useTargetPopulation from "@/store/modules/target-population";
import {
  watch,
  ref,
  onMounted,
} from 'vue'
import { Message, Modal } from '@arco-design/web-vue';
import { postBdManageApiSignAdd, postBdManageApiSignUpdate } from '@/batchApi';
// 获取签约的发布订阅模式
import { setSignedEmitter } from '@/utils/signed-listener';
import content from './content.vue';

const visible = ref(false);

const contentRef = ref();

const idCard = ref();

const quickAccessStore = useQuickAccessStore()
const targetPopulation = useTargetPopulation()

// const isView = computed(() => {
//   return quickAccessStore.quickAccessState.serviceReservationId !== ''
// })

watch([
  () => quickAccessStore.quickAccessState.showTheSigningInterface,
  () => quickAccessStore.quickAccessState.showTheSigningIdCard
], (
  [
    newShowTheSigningInterface,
    newShowTheSigningIdCard
  ]
) => {
  visible.value = newShowTheSigningInterface
  idCard.value = newShowTheSigningIdCard
  if (
    visible.value
  ) {
    console.log('_contentRef_', contentRef)
  }
})


const handleOk = () => {
  quickAccessStore.toggleSigningInterface(false);
}

// 如果当前页面 是在目标人群的页面 刷新对应的数据
const reloadData = () => {
  setTimeout(() => {
    // 更新当前列表的数据
    const mainButton = document.querySelector("#mainButton button")
    if (
      mainButton
    ) {
      (mainButton as HTMLButtonElement).click()
    }
    // 更新当前患者分组的数据
    targetPopulation.changeReloadServiceGroup(true);
  }, 500)
}

const handleBeforeOk = (): Promise<boolean> => {
  return new Promise((
    resolve,
  ) => {
    const {
      formInstance,
      rightsCodeList
    } = contentRef.value;
    if (
      !Array.isArray(rightsCodeList) ||
      rightsCodeList.length === 0
    ) {
      Message.error('请选中需要签约的服务包！');
      resolve(false)
      return 
    }
    console.log('values', formInstance)
    formInstance?.validate()
      .then((errors: any) => {
        if (
          errors === undefined
        ) {
          // 获取表单的数据 进行提交
          const formData = formInstance.model
          console.log('formData', formData)
          if (
            formData.patientId
          ) {
            postBdManageApiSignUpdate({
              ...formData,
              rightsCodeList
            }).then(_res => {
              console.log('signed_res', _res)
              resolve(true)
              reloadData()
              quickAccessStore.toggleSigningInterface(false);
              setSignedEmitter(formData)
            }, (error) => {
              Modal.error({
                title: '提示',
                content: error.message,
              })
              console.log('error', error)
              resolve(false)
            })
          } else {
            postBdManageApiSignAdd({
              ...formData,
              rightsCodeList
            }).then(_res => {
              console.log('signed_res', _res)
              resolve(true)
              reloadData()
              quickAccessStore.toggleSigningInterface(false);
              setSignedEmitter(formData)
            }, (error) => {
              Modal.error({
                title: '提示',
                content: error.message,
              })
              resolve(false)
            })
          }

        } else {
          resolve(false)
        }
      })
  })  
}

const handleCancel = () => {
  quickAccessStore.toggleSigningInterface(false);
}

</script>

<style lang="scss" scoped>

</style>