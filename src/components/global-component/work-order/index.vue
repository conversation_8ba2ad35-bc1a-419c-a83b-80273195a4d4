<!--
 * @Description: 工单全局组件
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-12-17 17:38:07
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-05-19 17:07:32
-->
<template>
  <a-drawer
    unmount-on-close
    :width="620"
    :visible="quickAccessStore.showWorkOrder"
    :mask-closable="true"
    body-class="appointmentView"
    :title="quickAccessStore.ticketId ? '编辑工单' : '创建工单'"
    @cancel="handleCancel"
  >
    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOnOk(false, false)"
          >暂存</a-button
        >
        <a-button type="primary" @click="handleOnOk(true, true)"
          >提交工单</a-button
        >
      </a-space>
    </template>
    <a-spin
      :loading="loading"
      :style="{
        width: '100%',
      }"
    >
      <a-form
        ref="modalFormRef"
        :model="form"
        :label-col-props="{ span: 4 }"
        :wrapper-col-props="{ span: 20 }"
        label-align="left"
        layout="vertical"
        scroll-to-first-error
        class="serviceAppointmentForm"
      >
        <!-- 基础信息 -->
        <BaseinfoForm
          v-model:form="form"
          :dictionary-state="dictionaryState"
          :data="detailsData"
        />
        <!-- 工单详情 -->
        <WorkOrderDetailsForm
          v-model:form="form"
          :dictionary-state="dictionaryState"
        />
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<script setup lang="ts">
import useQuickAccessStore from "@/store/modules/quick-access";
import { nextTick, onUpdated, reactive, ref, watch } from "vue";
import { FormInstance, Message } from "@arco-design/web-vue";
import to from "await-to-js";
import dayjs from "dayjs";
import * as apis from "@/batchApi";
import { storeToRefs } from "pinia";
import { useWorkOrder } from "@/store";
import BaseinfoForm from "./baseInfoForm.vue";
import WorkOrderDetailsForm from "./workOrderDetailsForm.vue";

const modalFormRef = ref<FormInstance>();
const workOrderStore = useWorkOrder();
const quickAccessStore = useQuickAccessStore();
const { ticketId } = storeToRefs(quickAccessStore);

const dictionaryState = reactive<IDictionaryState>({
  // 工单类型
  workOrderTypeLoading: false,
  workOrderTypeList: [],
  // 服务项目
  loadingServiceItems: false,
  serviceItems: [],
  // 服务包
  servicePackageLoading: false,
  servicePackage: [],
  // 处理人
  doctorListLoading: false,
  doctorList: [],
});

const form = reactive<IOrderForm>({
  userInfo: undefined,
  typeItem: undefined,
  packageCodeItem: undefined,
  serviceRecord: undefined,
});

const loading = ref<boolean>(false);
const detailsData = ref<IData | undefined>(undefined);

watch(
  () => form.userInfo,
  () => {
    console.log("👳‍♂️", form.userInfo);
    // modalFormRef.value?.resetFields();
  },
);

const handleOnOk = async (
  submit: boolean,
  isCheck?: boolean,
): Promise<boolean> => {
  if (!isCheck) {
    modalFormRef?.value?.clearValidate();
  }
  const modalError = isCheck ? await modalFormRef.value?.validate() : false;
  return new Promise(resolve => {
    if (!isCheck && !form.userInfo) {
      Message.error("没有客户信息，不支持暂存");
      resolve(false);
      return;
    }
    if (modalError) {
      resolve(false);
      return;
    }
    const {
      packageCodeItem,
      serviceRecord,
      typeItem,
      userInfo,
      fulfillmentMode,
      plannedExecuteTime,
      ...otherParams
    } = form;
    const params = {
      ...otherParams,
      customerId: userInfo?.patientId,
      type: typeItem?.dictValue,
      rightsPackage:
        (typeItem?.dictCode as any) === TypeEnum.dictCode
          ? (serviceRecord?.serviceId as unknown as number)
          : null,
      servicePackage:
        (typeItem?.dictCode as any) === TypeEnum.dictCode
          ? (packageCodeItem?.serviceId as unknown as number)
          : null,
      plannedExecuteTime,
      fulfillmentMode: fulfillmentMode ?? (null as any),
      submit,
      xid: quickAccessStore.ticketId ? quickAccessStore.ticketId : undefined,
    };
    resolve(false);
    apis
      .postBdManageApiTicketCreateDraft(
        params as apis.postBdManageApiTicketCreateDraftRequest,
      )
      .then((res: any) => {
        if (res?.code === "1") {
          Message.success("成功");
          handleCancel();
          workOrderStore.getTicketMyList();
        }
      })
      .catch(err => {
        resolve(false);
      });
  });
};

const handleCancel = () => {
  // 重置所有的表单数据
  modalFormRef?.value?.resetFields();
  detailsData.value = undefined;
  quickAccessStore.toggleWorkOrderVisibleStatus(false);
};

// 字典
const handleDictData = async (_dictType: string) => {
  const [err, res] = await to(
    apis.getBdManageApiSystemDictDataTypeByDictType({
      dictType: _dictType,
    }),
  );
  if (err) {
    throw err;
  }
  if (res.data) {
    return res.data;
  }
  return [];
};

// 权益包
watch(
  () => [form.userInfo, form.type],
  ([newUserInfo, newPaytype]) => {
    if (newUserInfo?.patientId) {
      getServicePackage();
    }
  },
);

// 服务项目
watch(
  () => [form.userInfo, form.type, form.packageCodeItem],
  ([newUserInfo, newPaytype, newPackageCodeItem]) => {
    if (newUserInfo?.patientId && newPackageCodeItem?.serviceCode) {
      form.serviceRecord = undefined;
      getServiceItems();
      getSignDoctorList({
        patientId: newUserInfo?.patientId,
        serviceCode: newPackageCodeItem?.serviceCode,
      });
    }
  },
);

// 工单类型变更
watch(
  () => form.typeItem,
  (newVal: any) => {
    if (newVal && newVal?.dictCode !== TypeEnum.dictCode) {
      getSignDoctorList({});
    }
  },
);

/* 获取服务项目 */
const getServiceItems = async () => {
  dictionaryState.loadingServiceItems = true;
  const [err, res] = await to(
    apis.getBdManageApiServiceReservationGetServiceItem({
      patientId: form.userInfo?.patientId,
      serviceId: form.packageCodeItem?.serviceId as string,
    }),
  );
  dictionaryState.loadingServiceItems = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    dictionaryState.serviceItems = res?.data;
    if (detailsData.value?.rightsPackage) {
      form.serviceRecord = dictionaryState.serviceItems.find(
        item => item.serviceId === detailsData.value?.rightsPackage,
      );
    }
  }
};

/* 获取患者服务包 */
const getServicePackage = async () => {
  dictionaryState.servicePackageLoading = true;
  const [err, res] = await to(
    apis.getBdManageApiServiceReservationGetServicePackage({
      patientId: form.userInfo?.patientId,
    }),
  );
  dictionaryState.servicePackageLoading = false;
  if (err) {
    throw err;
  }
  if (res?.data) {
    dictionaryState.servicePackage = res?.data;
    if (detailsData.value?.servicePackage) {
      form.packageCodeItem = dictionaryState.servicePackage.find(
        item => item.serviceId === detailsData.value?.servicePackage,
      );
    }
  }
};

// 签约医生
const getSignDoctorList = async ({
  patientId,
  serviceCode,
}: {
  patientId?: number;
  serviceCode?: string;
}) => {
  dictionaryState.doctorListLoading = true;
  const [err, res] = await to(
    apis.postBdManageApiServiceReservationGetTeamDoctor({
      patientId,
      serviceCode,
    }),
  );
  dictionaryState.doctorListLoading = false;
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    dictionaryState.doctorList = res?.data as IDictionaryState["doctorList"];
    if (detailsData.value?.assignees?.length) {
      const ids = detailsData.value?.assignees
        ?.map(item => item?.execUserId)
        ?.filter(Boolean);
      form.assigneeIds = ids as number[];
      form.fulfillmentMode = detailsData.value?.fulfillmentMode;
    }
  }
};

// 获取详情
const getManagementUserAdminDetail = async () => {
  loading.value = true;
  const [err, res] = await to(
    apis.getBdManageApiTicketById({ id: ticketId.value as number }),
  );
  loading.value = false;
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    const { data } = res || {};
    const transformedData: any = {};
    if (data) {
      Object.keys(data).forEach(key => {
        transformedData[key] = { value: data[key as keyof IData] };
      });
    }
    modalFormRef?.value?.setFields(Object.assign(transformedData));
    form.typeItem = dictionaryState.workOrderTypeList.find(
      item => item.dictValue === data?.type,
    );
    detailsData.value = data as IData;
    form.userInfo = { patientId: data?.customerId };
  }
};

onUpdated(async () => {
  if (quickAccessStore.showWorkOrder) {
    dictionaryState.workOrderTypeLoading = true;
    dictionaryState.workOrderTypeList = await handleDictData("ticket_type");
    dictionaryState.workOrderTypeLoading = false;

    if (ticketId.value) {
      getManagementUserAdminDetail();
    }
  }
});
</script>

<script lang="ts">
export enum TypeEnum {
  dictCode = "20101", // 权益使用
}

export type IData = apis.GetTicketUsingGETTicketDetailResVo;
// 字典数据
export type IDictionaryState = {
  // 工单类型
  workOrderTypeLoading: boolean;
  workOrderTypeList: apis.DictTypeUsingGETSysDictData[];
  // 权益包
  servicePackageLoading: boolean;
  servicePackage: apis.GetServicePackageUsingGETDocBusinessDto[];
  // 服务项目
  loadingServiceItems: boolean;
  serviceItems: apis.QueryServiceItemsUsingPOSTServiceItemsDto[];
  // 处理人（医生）
  doctorListLoading: boolean;
  doctorList: apis.GetTeamDoctorUsingPOSTDoctorInfoDto[];
};

export type IOrderForm = apis.postBdManageApiTicketCreateDraftRequest & {
  userInfo: any;
  typeItem: apis.DictTypeUsingGETSysDictData | undefined;
  packageCodeItem: apis.GetServicePackageUsingGETDocBusinessDto | undefined;
  serviceRecord: apis.QueryServiceItemsUsingPOSTServiceItemsDto | undefined;
};
</script>

<style lang="scss">
// 禁用样式之下的输入框处理
:deep(.arco-input-disabled) {
  color: #000 !important;
  background-color: white !important;
  padding-left: 0 !important;
  input:disabled {
    -webkit-text-fill-color: #000 !important;
  }
}

// 禁用状态下的只读样式处理
:deep(.arco-select-view-disabled) {
  color: #000 !important;
  background-color: white !important;
  padding-left: 0 !important;
  input:disabled {
    -webkit-text-fill-color: #000 !important;
  }
  .arco-select-view-suffix {
    display: none;
  }
}

:deep(.arco-picker-disabled) {
  color: #000 !important;
  background-color: white !important;
  padding-left: 0 !important;
  input {
    &:disabled {
      padding-left: 0 !important;
    }
  }
  .arco-picker-suffix {
    display: none;
  }
}

:deep(.arco-picker-disabled) {
  input:disabled {
    -webkit-text-fill-color: #000 !important;
  }
}

:deep(.arco-textarea-disabled) {
  color: #000 !important;
  background-color: white !important;
  padding-left: 0 !important;
  textarea {
    -webkit-text-fill-color: #000 !important;
    padding-left: 0;
    padding-right: 0;
  }
  div.arco-textarea-word-limit {
    display: none;
  }
}

.serviceAppointmentForm {
  width: 100%;
  > .serviceAppointmentFormItems {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }
}

.titleView {
  font-size: 16px;
  font-weight: bold;
  color: #000000;
  margin-bottom: 8px;
}
</style>
