<!--
 * @Description: 选中用户进行展示的组件
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-12-18 09:40:05
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-04-27 15:42:36
-->
<template>
  <div class="bd-user-select-container" :class="{ disabled: disabled }">
    <a-trigger
      v-if="!disabled"
      :popup-visible="resultVisible"
      :trigger="['click', 'contextMenu']"
      :click-outside-to-close="true"
      :click-to-close="false"
      auto-fit-popup-width
      @popup-visible-change="visible => (resultVisible = visible)"
    >
      <a-input-search
        v-model="searchValue"
        :style="{ width: '100%' }"
        placeholder="请搜索手机号/姓名"
        search-button
        @search="handleSearch"
        @press-enter="handleSearch"
      />
      <template #content>
        <!-- 用于展示获取到的表格数据 -->
        <a-spin
          :loading="associationLoading"
          tip="正在拉取相相关患者，请稍后..."
        >
          <a-table
            :loading
            :columns="columns"
            :data="tableData"
            :pagination="false"
            :scroll="{ y: 200 }"
            @row-dblclick="handleTableDbClick"
          />
        </a-spin>
      </template>
    </a-trigger>
    <div v-if="patientInfo" class="majorView">
      <div class="content">
        <img
          :src="
            getImageSrc(
              patientInfo?.gender === 1 ? 'peopleMan.png' : 'peopleGirl.png',
            )
          "
        />

        <span class="title"
          >{{ patientInfo?.name }}&nbsp;
          {{ patientInfo?.gender === 1 ? "男" : "女" }}&nbsp;
          {{ patientInfo?.age ?? "-" }}岁</span
        >
      </div>
      <div class="phone">
        <span>联系方式：</span>
        <span>{{ patientInfo?.contactMobile }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js";
import { useFormItem } from "@arco-design/web-vue";
import { ref, watch } from "vue";
import * as apis from "@/batchApi";
import { getImageSrc } from "@/utils";

type IRecord = apis.QueryPatientByParamsUsingPOSTHuanZheXinXiChaXunFanCan;

const searchValue = ref();
const resultVisible = ref(false);
const patientInfo = ref<IRecord>();
const tableData = ref<IRecord[]>([]);
const loading = ref(false);
const associationLoading = ref(false);

const columns = [
  {
    title: "姓名",
    dataIndex: "name",
    align: "center",
  },
  {
    title: "性别",
    align: "center",
    dataIndex: "gender",
    render: ({ record }: { record: any }) => {
      return record.gender === 1 ? "男" : "女";
    },
  },
  {
    title: "年龄",
    dataIndex: "age",
    align: "center",
  },
  {
    width: 140,
    title: "手机号",
    dataIndex: "contactMobile",
  },
  {
    width: 190,
    title: "证件号",
    dataIndex: "idCard",
  },
];

const { mergedDisabled, eventHandlers } = useFormItem();

const disabled = mergedDisabled.value;

const { modelValue } = defineProps(["modelValue"]);

const emits = defineEmits(["update:modelValue"]);

// 双击表格行的时候 触发的方法
const handleTableDbClick = (record: IRecord, ev: Event) => {
  // 然后设置新的数据
  handleInput(record);
  resultVisible.value = false;
  patientInfo.value = record;
  searchValue.value = undefined;
  // 设置当前选中的数据
};

// 点击搜索的时候 获取对应的数据
const handleSearch = () => {
  if (typeof searchValue.value === "string" && searchValue.value !== "") {
    resultVisible.value = true;
    getPatientList(searchValue.value);
  }
};

// 设置当前选中的患者数据
const handleInput = (record: IRecord) => {
  const ev = {
    target: {
      value: record,
    },
  };
  emits("update:modelValue", ev.target.value);
  eventHandlers.value?.onChange?.(ev as unknown as InputEvent);
};

// 初始获取患者的数据
const getPatientList = async (
  _keyword: string | undefined,
  patientId?: string,
) => {
  loading.value = true;
  const [err, res] = await to(
    apis.postBdManageApiPatientQueryPatientByParams({
      params: _keyword,
      patientId: patientId as unknown as string,
    }),
  );
  loading.value = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    tableData.value = res.data;
  }
  return res.data;
};

watch(
  () => modelValue,
  () => {
    // 如果modelValue 发生了变化 根据这个数据来更新本地的数据
    if (typeof modelValue?.idCard === "string" && modelValue?.idCard !== "") {
      patientInfo.value = modelValue;
    }
  },
);
</script>

<style lang="scss" scoped>
.bd-user-select-container {
  width: 100%;
  &.disabled {
    pointer-events: none;
  }
}

.majorView {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(0, 82, 217, 0.06);
  border-radius: 8px;
  margin-top: 8px;
}

.content {
  display: flex;
  align-items: center;
  > img {
    width: 28px;
    height: 28px;
    margin-right: 12px;
  }

  .title {
    font-size: 16px;
    font-weight: bold;
    color: #000000;
  }
}
.phone {
  display: flex;
  font-size: 14px;
  > span:first-child {
    color: #86909c;
  }
}
</style>
