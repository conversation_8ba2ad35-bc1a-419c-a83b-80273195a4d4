<!--
* @description 编辑时用户信息
* @fileName userInfo.vue
* <AUTHOR>
* @date 2025/07/15 21:05:12
!-->
<script setup lang="ts">
import { ref } from "vue";
import { getImageSrc } from "@/utils";
import dayjs from "dayjs";
import { IData } from "./index.vue";

defineProps<{ data: IData | undefined }>();
</script>

<template>
  <div class="majorView">
    <div class="content">
      <img
        :src="
          getImageSrc(data?.gender === 1 ? 'peopleMan.png' : 'peopleGirl.png')
        "
      />

      <span class="title"
        >{{ data?.customerName }}&nbsp;
        {{ data?.gender === 1 ? "男" : "女" }}&nbsp;
        {{
          data?.birthdate ? `${dayjs().diff(data?.birthdate, "year")}岁` : ""
        }}</span
      >
    </div>
    <div v-if="data?.contactMobile" class="phone">
      <span>联系方式：</span>
      <span>{{ data?.contactMobile ?? "-" }}</span>
    </div>
  </div>
</template>

<style lang="less" scoped>
.majorView {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(0, 82, 217, 0.06);
  border-radius: 8px;
  margin-top: 8px;
  margin-bottom: 16px;
}

.content {
  display: flex;
  align-items: center;
  > img {
    width: 28px;
    height: 28px;
    margin-right: 12px;
  }

  .title {
    font-size: 16px;
    font-weight: bold;
    color: #000000;
  }
}
.phone {
  display: flex;
  font-size: 14px;
  > span:first-child {
    color: #86909c;
  }
}
</style>
