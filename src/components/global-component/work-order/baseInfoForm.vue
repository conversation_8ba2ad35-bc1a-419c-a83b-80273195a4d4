<!--
* @description 基本信息
* @fileName baseinfoForm.vue
* <AUTHOR>
* @date 2025/07/11 10:43:57
!-->
<script setup lang="ts">
import { computed } from "vue";
import { PriorityMap } from "@/views/work-order-manage/config";
import useQuickAccessStore from "@/store/modules/quick-access";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import CardView from "../formContent/cardView.vue";
import userSelect from "./userSelect.vue";
import { IDictionaryState, IOrderForm, TypeEnum, IData } from "./index.vue";
import userInfo from "./userInfo.vue";

type Props = {
  dictionaryState: IDictionaryState;
  data: IData | undefined;
};
const quickAccessStore = useQuickAccessStore();
const { ticketId } = storeToRefs(quickAccessStore);
defineProps<Props>();
const form = defineModel<IOrderForm>("form", { required: true });
const isShow = computed(() => {
  return form.value?.typeItem?.dictCode === (TypeEnum.dictCode as any);
});

const handleChange = () => {
  form.value.packageCodeItem = undefined;
  form.value.serviceRecord = undefined;
  form.value.assigneeIds = undefined;
};
</script>

<template>
  <CardView title="基础信息">
    <userInfo v-if="ticketId" :data="data" />
    <a-form-item
      v-else
      field="userInfo"
      :label-col-props="{ span: 0 }"
      :wrapper-col-props="{ span: 24 }"
      :rules="[{ required: true, message: '请选择客户信息' }]"
    >
      <userSelect v-model="form.userInfo" />
    </a-form-item>

    <a-form-item
      field="title"
      label="工单标题"
      :rules="[
        {
          required: true,
          message: '请输入工单标题',
        },
      ]"
    >
      <a-input
        v-model="form.title"
        placeholder="请输入"
        :max-length="100"
        show-word-limit
      />
    </a-form-item>
    <a-form-item
      field="typeItem"
      label="工单类型"
      :rules="[
        {
          required: true,
          message: '请选择工单类型',
        },
      ]"
    >
      <a-select
        v-model="form.typeItem"
        placeholder="请选择"
        value-key="dictValue"
        :loading="dictionaryState.workOrderTypeLoading"
        @change="handleChange"
      >
        <a-option
          v-for="item in dictionaryState.workOrderTypeList"
          :key="item.dictValue"
          :value="item"
        >
          {{ item?.dictLabel }}
        </a-option>
      </a-select>
    </a-form-item>
    <a-form-item
      v-if="isShow"
      field="packageCodeItem"
      label="权益包"
      :rules="[
        {
          required: true,
          message: '请选择权益包',
        },
      ]"
    >
      <a-select
        v-model="form.packageCodeItem"
        placeholder="请选择"
        value-key="serviceId"
        :loading="dictionaryState.servicePackageLoading"
      >
        <a-option
          v-for="item in dictionaryState.servicePackage"
          :key="item.serviceId"
          :value="item"
        >
          {{ item.serviceName }}
        </a-option>
      </a-select>
    </a-form-item>
    <a-form-item
      v-if="isShow"
      field="serviceRecord"
      label="服务内容"
      :rules="[
        {
          required: true,
          message: '请选择服务内容',
        },
      ]"
    >
      <a-select
        v-model="form.serviceRecord"
        :loading="dictionaryState.loadingServiceItems"
        placeholder="请选择"
        value-key="serviceId"
      >
        <a-option
          v-for="item in dictionaryState.serviceItems"
          :key="item.serviceId"
          :value="item"
        >
          {{ item.serviceName }}
        </a-option>
      </a-select>
    </a-form-item>
    <a-form-item
      field="priority"
      label="优先级"
      :rules="[
        {
          required: true,
          message: '请选择优先级',
        },
      ]"
    >
      <a-radio-group v-model="form.priority">
        <a-radio v-for="key in PriorityMap.keys()" :key="key" :value="key">
          <template #radio="{ checked }">
            <a-tag :checked="checked" checkable>{{
              PriorityMap.get(key)?.title
            }}</a-tag>
          </template>
        </a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item
      field="plannedExecuteTime"
      label="执行时间"
      :rules="[
        {
          required: true,
          message: '请选择执行时间',
        },
      ]"
    >
      <a-date-picker
        v-model="form.plannedExecuteTime"
        :style="{ width: '100%' }"
        show-time
        :disabled-date="current => dayjs(current).isBefore(dayjs())"
        placeholder="请选择"
      />
    </a-form-item>
  </CardView>
</template>

<style lang="scss" scoped>
:deep(.arco-tag-checkable) {
  width: 64px;
  display: flex;
  justify-content: center;
}
:deep(.arco-tag-checked) {
  width: 64px;
  display: flex;
  justify-content: center;
  text-align: center;
  background-color: #0052d9 !important;
  color: #ffffff;
}
:deep(.arco-radio-group .arco-radio) {
  text-align: center;
  background-color: #fff;
  border: 1px solid rgba(134, 144, 156, 0.5);
  padding-left: 0px !important;
  border-radius: 4px;
}
</style>
