<!--
* @description 
* @fileName workOrderDetailsForm.vue
* <AUTHOR>
* @date 2025/07/11 16:15:10
!-->
<script setup lang="ts">
import { UlfillmentModeMap } from "@/views/work-order-manage/config";
import { computed } from "vue";
import CardView from "../formContent/cardView.vue";
import { IDictionaryState, IOrderForm } from "./index.vue";

type Props = {
  dictionaryState: IDictionaryState;
};
defineProps<Props>();
const form = defineModel<IOrderForm>("form", { required: true });

const isShow = computed(() => {
  const len = form.value?.assigneeIds?.length;
  if (!len) return false;
  return len > 1;
});
const handleChange = () => {
  form.value.fulfillmentMode = undefined;
};
</script>

<template>
  <CardView title="工单详情">
    <a-form-item
      field="assigneeIds"
      label="处理人"
      :rules="[
        {
          required: true,
          message: '请选择处理人',
        },
      ]"
    >
      <a-select
        v-model="form.assigneeIds"
        placeholder="请选择"
        :loading="dictionaryState.doctorListLoading"
        :fallback-option="false"
        multiple
        @change="handleChange"
      >
        <a-optgroup v-if="dictionaryState.doctorList?.length">
          <template #label
            ><span class="optgroupStyle">签约医生</span></template
          >
          <a-option
            v-for="item in dictionaryState.doctorList"
            :key="item.doctorUserId"
            :value="item?.doctorUserId"
            >{{ item?.doctorName }}</a-option
          >
        </a-optgroup>
      </a-select>
    </a-form-item>
    <a-form-item
      v-if="isShow"
      field="fulfillmentMode"
      label="处理方式"
      :rules="[
        {
          required: true,
          message: '请选择处理方式',
        },
      ]"
    >
      <a-radio-group v-model="form.fulfillmentMode">
        <a-radio
          v-for="key in UlfillmentModeMap.keys()"
          :key="key"
          :value="key"
          class="tagView"
        >
          <template #radio="{ checked }">
            <a-tag :checked="checked" checkable>{{
              UlfillmentModeMap.get(key)?.title
            }}</a-tag>
          </template>
        </a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item
      field="description"
      label="问题描述"
      :rules="[{ required: true, message: '请填写问题描述' }]"
    >
      <a-textarea
        v-model="form.description"
        placeholder="请详细描述您遇到的问题...."
        :auto-size="{ minRows: 5, maxRows: 10 }"
      />
    </a-form-item>
  </CardView>
</template>

<style lang="scss" scoped>
:deep(.arco-tag-checkable) {
  width: 64px;
  display: flex;
  justify-content: center;
}
.tagView {
  :deep(.arco-tag-checked) {
    width: 64px;
    display: flex;
    justify-content: center;
    text-align: center;
    background-color: #0052d9 !important;
    color: #ffffff;
  }
}

:deep(.arco-radio-group .arco-radio) {
  text-align: center;
  background-color: #fff;
  border: 1px solid rgba(134, 144, 156, 0.5);
  padding-left: 0px !important;
  border-radius: 4px;
}
</style>
