<!--
* @description 既往就诊信息
* @fileName pastVisit.vue
* <AUTHOR>
* @date 2025/06/07 23:59:28
!-->
<script setup lang="ts">
import { ref, watch } from "vue";
import to from "await-to-js";
import * as apis from "@/batchApi";
import CardView from "./cardView.vue";
import { TForm } from "../service-appointment.vue";

const form = defineModel<TForm>("form", { required: true });
const visitRecordList = ref<
  apis.postBdManageApiServiceReservationGetVisitRecordResponse["data"]
>([]);
const loading = ref<boolean>(false);

/* 本院就诊信息 */
const getVisitRecord = async () => {
  const patientId = form.value.userInfo?.patientId;
  loading.value = true;
  const [err, res] = await to(
    apis.postBdManageApiServiceReservationGetVisitRecord({
      patientId,
    }),
  );
  loading.value = false;
  if (err) {
    throw err;
  }
  if (res.code === "1") {
    visitRecordList.value =
      res?.data as apis.postBdManageApiServiceReservationGetVisitRecordResponse["data"];
  }
};

watch(
  () => form.value.isInHospital,
  nevVal => {
    if (nevVal) {
      getVisitRecord();
    } else {
      form.value.pastDiagnosis = undefined;
    }
  },
);
</script>

<template>
  <CardView title="既往就诊信息">
    <a-form-item
      field="isInHospital"
      label="近半年是否本院有就诊"
      :label-col-props="{ span: 7 }"
      :wrapper-col-props="{ span: 17 }"
      :rules="[
        {
          required: true,
          message: '请选择近半年是否本院有就诊',
        },
      ]"
    >
      <a-radio-group v-model="form.isInHospital">
        <a-radio :value="true">有</a-radio>
        <a-radio :value="false">无</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item
      v-if="form.isInHospital"
      field="pastDiagnosis"
      label="本院就诊信息"
      :label-col-props="{ span: 7 }"
      :wrapper-col-props="{ span: 17 }"
      :rules="[
        {
          required: form.isInHospital,
          message: '请选择本院就诊信息',
        },
      ]"
    >
      <a-select
        v-model="form.pastDiagnosis"
        :loading="loading"
        placeholder="请选择"
      >
        <a-option
          v-for="item in visitRecordList"
          :key="item.pid"
          :value="item.diagnosis_name"
        >
          {{ item.diagnosis_name }}
        </a-option>
      </a-select>
    </a-form-item>
  </CardView>
</template>

<style lang="scss" scoped></style>
