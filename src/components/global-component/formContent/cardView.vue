<!--
* @description 卡片
* @fileName CardView.vue
* <AUTHOR>
* @date 2025/06/08 17:42:20
!-->
<script setup lang="ts">
import { CSSProperties } from "vue";

type Props = {
  title?: string;
  tips?: string;
  cardStyle?: CSSProperties;
  required?: boolean;
};

withDefaults(defineProps<Props>(), {
  title: "标题",
});
</script>

<template>
  <div class="cardView" :style="cardStyle">
    <div class="titleView">
      <span
        ><label
          v-if="required"
          style="color: #f53f3f; font-size: 12px; margin-right: 2px"
          >*</label
        >{{ title }}</span
      >
      <span class="tips">{{ tips }}</span>
    </div>
    <slot />
  </div>
</template>

<style lang="scss" scoped>
.cardView {
  background: #fff;
  padding: 20px;
  padding-bottom: 0;
  margin-bottom: 12px;

  .titleView {
    font-size: 16px;
    font-weight: bold;
    color: #000000;
    margin-bottom: 8px;

    .tips {
      color: rgba(0, 0, 0, 0.3);
      font-size: 12px;
      margin-left: 4px;
      font-weight: normal;
    }
  }
}
</style>
