<!--
* @description 首诊信息
* @fileName firstVisit.vue
* <AUTHOR>
* @date 2025/06/08 09:47:02
!-->
<script setup lang="ts">
import { computed, watch } from "vue";
import { getImageSrc } from "@/utils";
import CardView from "./cardView.vue";
import uploadFile from "../components/upload-file.vue";
import { TForm } from "../service-appointment.vue";

const form = defineModel<TForm>("form", { required: true });

interface Props {
  index: number;
}

const props = defineProps<Props>();

// 计算当前索引对应的数据
// const currentData = computed({
//   get() {
//     if (props.index === 0) {
//       return form.value.userInfo || {};
//     }
//     return form.value.originatorsInfo?.[props.index - 1] || {};
//   },
//   set(newValue) {
//     if (props.index === 0) {
//       form.value.userInfo = { ...form.value.userInfo, ...newValue };
//     } else {
//       if (!form.value.originatorsInfo) form.value.originatorsInfo = [];
//       form.value.originatorsInfo[props.index - 1] = {
//         ...form.value.originatorsInfo[props.index - 1],
//         ...newValue,
//       };
//     }
//   },
// });

// 更新当前索引对应的数据
const currentData = computed({
  get() {
    return form.value.firstVisitList?.[props.index] || {};
  },
  set(newValue) {
    if (!form.value.firstVisitList) form.value.firstVisitList = [];
    form.value.firstVisitList[props.index] = {
      ...form.value.firstVisitList[props.index],
      ...newValue,
    };
  },
});
const updateCurrentData = (field: string, value: any) => {
  if (!form.value.firstVisitList) form.value.firstVisitList = [];
  if (!form.value.firstVisitList[props.index])
    form.value.firstVisitList[props.index] = {};
  form.value.firstVisitList[props.index][field] = value;
};
// const updateCurrentData = (field: string, value: any) => {
//   if (props.index === 0) {
//     if (!form.value.userInfo) form.value.userInfo = {};
//     form.value.userInfo[field] = value;
//   } else {
//     if (!form.value.originatorsInfo) form.value.originatorsInfo = [];
//     if (!form.value.originatorsInfo[props.index - 1])
//       form.value.originatorsInfo[props.index - 1] = {};
//     form.value.originatorsInfo[props.index - 1][field] = value;
//   }
// };

// 监听 currentData 的变化，确保数据同步
watch(
  currentData,
  newValue => {
    if (newValue && Object.keys(newValue).length > 0) {
      updateCurrentData("reports", newValue.reports || []);
      updateCurrentData("consentFiles", newValue.consentFiles || []);
      updateCurrentData("description", newValue.description || "");
    }
  },
  { deep: true },
);
</script>

<template>
  <div>
    <div class="iconView">
      <img :src="getImageSrc('quanyi.png')" class="" />
      <span
        >{{ currentData?.isMainPatient ? "主权益人" : "共享权益人" }}：{{
          currentData?.patientName
        }}</span
      >
    </div>
    <CardView required title="情况描述" :style="{ paddingBottom: '1px' }">
      <a-form-item
        :field="`firstVisitList.${index}.description`"
        :label-col-props="{ span: 0 }"
        :wrapper-col-props="{ span: 24 }"
        :rules="[{ required: true, message: '请填写情况描述' }]"
      >
        <a-textarea
          :value="currentData.description"
          placeholder="您是否曾患过什么疾病或者既往健康状况，请输入...."
          :auto-size="{ minRows: 5, maxRows: 10 }"
          @input="value => updateCurrentData('description', value)"
        />
      </a-form-item>
    </CardView>

    <CardView
      title="上传报告"
      tips="（限10M，可上传报告、患处照片，支持PDF/图片，最多上传9张）"
      :style="{ paddingBottom: '1px' }"
    >
      <a-form-item
        :field="`firstVisitList.${index}.reports`"
        :label-col-props="{ span: 0 }"
        :wrapper-col-props="{ span: 24 }"
        :rules="[{ required: false, message: '请上传相关报告' }]"
      >
        <upload-file
          v-model="currentData.reports"
          :file-type="currentData.fileType || 'image'"
          :max-count="9"
        />
      </a-form-item>
    </CardView>

    <CardView
      title="上传知情同意书"
      tips="（限10M，非必填，支持上传知情同意截图或者录音）"
      :style="{ paddingBottom: '1px' }"
    >
      <a-form-item
        :field="`firstVisitList.${index}.consentFiles`"
        :label-col-props="{ span: 0 }"
        :wrapper-col-props="{ span: 24 }"
      >
        <upload-file
          v-model="currentData.consentFiles"
          :file-type="currentData.fileType || 'image'"
          :max-count="5"
        />
      </a-form-item>
    </CardView>
  </div>
</template>

<style lang="scss" scoped>
.iconView {
  height: 30px;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #0052d9;
  background: #f9fbff;
  padding: 0 20px;
  margin-bottom: 20px;
  > img {
    width: 12px;
    height: 15px;
    margin-right: 10px;
  }
}
</style>
