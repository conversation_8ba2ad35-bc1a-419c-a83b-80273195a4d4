<!--
* @description 预约信息
* @fileName reservationInformationForm.vue
* <AUTHOR>
* @date 2025/06/08 17:57:41
!-->
<script setup lang="ts">
import { computed } from "vue";
import CardView from "./cardView.vue";
import rightsholder from "./rightsholder.vue";
import datePicker from "./datePicker.vue";
import {
  IDictionaryState,
  TForm,
  ServiceIdCategory,
} from "../service-appointment.vue";
import userSelect from "./userSelect.vue";

type Props = {
  dictionaryState: IDictionaryState;
};
defineProps<Props>();
const form = defineModel<TForm>("form", { required: true });

const maxNum = computed(() => {
  if (form.value?.serviceIdRecord?.serviceCode === ServiceIdCategory.SZYY) {
    return 9;
  }
  return 1;
});

const handleChange = (e: any) => {
  if (e.target.value === "0") {
    form.value.firstVisitOrgId = undefined;
    form.value.isReservedParking = undefined;
  }
};
const handleCheckChange = (value: any, event: any) => {
  const currentOperate = (event.target as unknown as any)?._value;
  const isSZYY =
    form.value.serviceIdRecord?.serviceCode === ServiceIdCategory.SZYY;
  const userInfo = form.value.userInfo || [];
  const mainPatient = userInfo.find(
    (item: { isMainPatient: boolean }) => item.isMainPatient,
  );

  if (isSZYY) {
    // 是否勾选了主权益人
    const hasMain = value?.some(
      (item: any) =>
        item && mainPatient && item.patientId === mainPatient.patientId,
    );
    // 是否勾选了共享权益人
    const hasShare = value?.some((item: any) => item && !item.isMainPatient);

    if (
      currentOperate &&
      mainPatient &&
      currentOperate.patientId === mainPatient.patientId &&
      !hasMain
    ) {
      form.value.originatorsInfo = [];
      return;
    }
    if (
      currentOperate &&
      !currentOperate.isMainPatient &&
      hasShare &&
      !hasMain
    ) {
      const result = [
        mainPatient,
        ...value.filter(
          (item: any) => item.patientId !== mainPatient.patientId,
        ),
      ];
      form.value.originatorsInfo = result;
      return;
    }
    form.value.originatorsInfo = value;
  } else {
    // 非首诊场景，随便选
    form.value.originatorsInfo = value;
  }
};
</script>

<template>
  <CardView title="预约信息">
    <!-- <a-form-item
      field="userInfo"
      label="选择权益人"
      tooltip="注意：勾选权益人与服务项目关联，非首诊只能选择一个权益人"
      :label-col-props="{ span: 5 }"
      :wrapper-col-props="{ span: 19 }"
      :rules="[{ required: true, message: '请选择选择权益人' }]"
    >
      <userSelect
        v-model="form.userInfo"
        @clear-originators="form.originatorsInfo = []"
      />
    </a-form-item> -->
    <!-- <div v-if="!form.userInfo" class="emptyUser">
      暂无信息，请先进行搜索并选择权益人
    </div> -->
    <a-form-item
      field="originatorsInfo"
      :label-col-props="{ span: 0 }"
      :wrapper-col-props="{ span: 24 }"
    >
      <rightsholder
        v-model="form.originatorsInfo"
        :max="maxNum"
        :user-info="form.userInfo"
        value-key="patientIdCard"
        @change="handleCheckChange"
      />
    </a-form-item>

    <a-form-item
      v-if="form.serviceIdRecord?.serviceCode === ServiceIdCategory.SZYY"
      field="firstVisitType"
      label="首诊类型"
      :rules="[
        {
          required: true,
          message: '请选择首诊类型',
        },
      ]"
      @change="handleChange"
    >
      <a-radio-group v-model="form.firstVisitType">
        <a-radio value="0">线上首诊</a-radio>
        <a-radio value="1">线下首诊</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item
      v-if="form.firstVisitType === '1'"
      field="firstVisitOrgId"
      label="首诊机构"
      :rules="[
        {
          required: form.firstVisitType === '1',
          message: '请选择首诊机构',
        },
      ]"
    >
      <a-select
        v-model="form.firstVisitOrgId"
        :loading="dictionaryState.organLoading"
        placeholder="请选择"
        allow-search
      >
        <a-option
          v-for="item in dictionaryState?.organList"
          :key="item.organId"
          :value="item.organId"
        >
          {{ item.name }}
        </a-option>
      </a-select>
    </a-form-item>
    <a-form-item
      v-if="form.originatorsInfo?.length && form.serviceIdRecord"
      field="reservationTime"
      label="预约时间"
      :rules="[
        {
          required: true,
          message: '请选择预约时间',
        },
      ]"
    >
      <datePicker v-model="form.reservationTime" :form="form" />
    </a-form-item>
    <a-form-item
      v-if="form.firstVisitType === '1'"
      field="isReservedParking"
      label="预留车位"
      :rules="[
        {
          required: form.firstVisitType === '1',
          message: '请选择首诊类型',
        },
      ]"
    >
      <a-radio-group v-model="form.isReservedParking">
        <a-radio :value="true">是</a-radio>
        <a-radio :value="false">否</a-radio>
      </a-radio-group>
    </a-form-item>
  </CardView>
</template>

<style lang="scss" scoped></style>
