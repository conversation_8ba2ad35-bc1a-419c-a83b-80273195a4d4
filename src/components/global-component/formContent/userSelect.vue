<!--
 * @Description: 选中用户进行展示的组件
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-12-18 09:40:05
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-04-27 15:42:36
-->
<template>
  <div class="bd-user-select-container" :class="{ disabled: disabled }">
    <a-trigger
      v-if="!disabled"
      :popup-visible="resultVisible"
      :click-outside-to-close="true"
      :click-to-close="false"
      auto-fit-popup-width
      :scroll-to-close="true"
      @popup-visible-change="visible => (resultVisible = visible)"
    >
      <a-input-search
        v-model="searchValue"
        :style="{ width: '100%' }"
        placeholder="请输入患者名称进行搜索"
        search-button
        @search="handleSearch"
        @press-enter="handleSearch"
      />
      <template #content>
        <!-- 用于展示获取到的表格数据 -->
        <a-spin
          style="width: 100%"
          :loading="associationLoading"
          tip="正在拉取相关权益人，请稍后..."
        >
          <a-table
            :loading
            :columns="columns"
            :data="tableData"
            :pagination="false"
            :scroll="{ y: 200 }"
            @row-dblclick="handleTableDbClick"
          />
        </a-spin>
      </template>
    </a-trigger>
    <div v-if="patientInfo" class="majorView">
      <div class="tips">主权益人</div>
      <a-checkbox>
        <template #checkbox="{ checked }">
          <a-space
            align="start"
            class="custom-checkbox-card"
            :class="{ 'custom-checkbox-card-checked': checked }"
          >
            <div class="content">
              <img
                :src="
                  getImageSrc(
                    patientInfo?.sex === '女' ? 'woman.png' : 'man.png',
                  )
                "
              />
              <div class="patindUserInfo">
                <span class="title">{{ patientInfo?.patientName }}</span>
                <span
                  >{{ patientInfo?.age ?? "-" }}岁 {{ patientInfo?.sex }}</span
                >
              </div>
            </div>
            <!-- <div className="custom-checkbox-card-mask">
              <div className="custom-checkbox-card-mask-dot" />
            </div> -->
          </a-space>
        </template>
      </a-checkbox>
    </div>
    <a-alert v-if="orderData.orderId" type="warning" class="alertSty">
      <span class="tipText">温馨提示：您选择的权益人当前还有未结束的订单</span>
      <template #action>
        <a-trigger
          trigger="click"
          :unmount-on-close="false"
          :popup-translate="[-266, 10]"
        >
          <span>点击查看&nbsp;<icon-right /></span>
          <template #content>
            <div class="tipContent">
              <div class="title">
                主权人： {{ patientInfo?.patientName ?? "-" }}
              </div>
              <a-table
                :columns="columnsOrder"
                :data="[orderData]"
                :pagination="false"
              />
            </div>
          </template>
        </a-trigger>
      </template>
    </a-alert>
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js";
import { useFormItem, TableColumnData, TableData } from "@arco-design/web-vue";
import { ref, watch, onMounted } from "vue";
import * as apis from "@/batchApi";
import { getImageSrc } from "@/utils";
import { useQuickAccessStore } from "@/store";
import { storeToRefs } from "pinia";

type IRecord = apis.QueryMainAndSharePatientUsingPOSTMainAndSharePatientDto;

const quickAccessStore = useQuickAccessStore();
const { mainPatientId } = storeToRefs(quickAccessStore);
const searchValue = ref();
const resultVisible = ref(false);
const patientInfo = ref<IRecord>();
const tableData = ref<IRecord[]>([]);
const loading = ref(false);
const associationLoading = ref(false);
const orderData = ref<apis.GetNoPayOrderUsingPOSTNoPayOrderResp>({});

const columns: Array<TableColumnData> = [
  {
    title: "姓名",
    dataIndex: "patientName",
  },
  {
    width: 80,
    title: "性别",
    dataIndex: "sex",
  },
  {
    width: 65,
    title: "年龄",
    dataIndex: "age",
  },
  {
    width: 150,
    title: "手机号",
    dataIndex: "patientPhone",
  },
  {
    width: 200,
    title: "证件号",
    dataIndex: "patientIdCard",
  },
];

const columnsOrder: Array<TableColumnData> = [
  {
    title: "订单号",
    dataIndex: "orderId",
    ellipsis: true,
  },
  {
    title: "服务项目",
    dataIndex: "service",
  },
  {
    title: "预约时间",
    dataIndex: "aptTime",
    render: ({ record }: { record: TableData }) => {
      return record?.aptTime ?? "-";
    },
  },
];

const { mergedDisabled, eventHandlers } = useFormItem();

const disabled = mergedDisabled.value;

const { modelValue } = defineProps(["modelValue"]);

const emits = defineEmits(["update:modelValue", "clearOriginators"]);

// 双击表格行的时候 触发的方法
const handleTableDbClick = (record: IRecord, ev: Event) => {
  // 先清空父组件的多选框数据
  emits("clearOriginators");

  // 然后设置新的数据
  handleInput(record);
  getNoPayOrder(record?.patientId as number);
  resultVisible.value = false;
  patientInfo.value = record;
  searchValue.value = undefined;
  // 设置当前选中的数据
};

onMounted(async () => {
  if (mainPatientId.value) {
    emits("clearOriginators");
    const data = await getPatientList(undefined, mainPatientId.value);
    if (data?.length) {
      const mainRecord = data?.at(0);
      handleInput(mainRecord as IRecord);
      patientInfo.value = mainRecord;
      getNoPayOrder(mainRecord?.patientId as number);
    }
  }
});

// 点击搜索的时候 获取对应的数据
const handleSearch = () => {
  if (typeof searchValue.value === "string" && searchValue.value !== "") {
    resultVisible.value = true;
    getPatientList(searchValue.value);
  }
};

// 获取订单是否存在
const getNoPayOrder = async (patientId: number) => {
  const [err, res] = await to(
    apis.postBdManageApiServiceReservationGetNoPayOrder({
      patientId,
    }),
  );
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    orderData.value = res?.data as apis.GetNoPayOrderUsingPOSTNoPayOrderResp;
  }
};

// 设置当前选中的患者数据
const handleInput = (record: IRecord) => {
  // associationLoading.value = true;
  // const [err, res] = await to(
  //   apis.postBdManageApiPatientQuerySelectedPatient({
  //     isMainPatient: record?.isMainPatient,
  //     mainPatientId: record?.mainPatientId,
  //   }),
  // );
  // associationLoading.value = false;
  // if (err) {
  //   throw err;
  // }
  // if (res.data) {
  //   const ev = {
  //     target: {
  //       value: res.data,
  //     },
  //   };
  //   emits("update:modelValue", ev.target.value);
  //   eventHandlers.value?.onChange?.(ev as unknown as InputEvent);
  // }

  const ev = {
    target: {
      value: record,
    },
  };
  emits("update:modelValue", ev.target.value);
  eventHandlers.value?.onChange?.(ev as unknown as InputEvent);
};

// 初始获取患者的数据
const getPatientList = async (
  _keyword: string | undefined,
  patientId?: string,
) => {
  loading.value = true;
  const [err, res] = await to(
    apis.postBdManageApiPatientQueryMainAndSharePatient({
      keyWord: _keyword,
      patientId: patientId as unknown as number,
    }),
  );
  loading.value = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    tableData.value = res.data;
  }
  return res.data;
};

watch(
  () => modelValue,
  () => {
    // 如果modelValue 发生了变化 根据这个数据来更新本地的数据
    if (typeof modelValue?.idCard === "string" && modelValue?.idCard !== "") {
      patientInfo.value = modelValue;
    }
  },
);
</script>

<style lang="scss" scoped>
.bd-user-select-container {
  width: 100%;
  &.disabled {
    pointer-events: none;
  }
}
// 患者选中数据的展示
:deep(.arco-checkbox) {
  padding-left: 0px;
}
.custom-checkbox-card {
  padding: 8px 12px 16px 16px;
  border-radius: 8px;
  width: 176px;
  box-sizing: border-box;
  background: rgba(0, 82, 217, 0.06);
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.majorView {
  display: flex;
  margin-top: 16px;

  .tips {
    width: 20px;
    height: 80px;
    background: #0052d9;
    color: #ffffff;
    font-size: 12px;
    line-height: 14px;
    text-align: center;
    padding: 12px 0;
    margin-right: 12px;
  }
  .scrollView {
    width: calc(650px - 68px);
    display: flex;
    overflow-x: auto;
  }
}

.alertSty {
  border-radius: 8px;
  font-size: 14px;
  color: #ff7f0b;

  .tipText {
    font-size: 14px;
    color: #ff7f0b;
    font-weight: 350;
  }
}

.tipContent {
  padding: 16px;
  width: 640px;
  background-color: var(--color-bg-popup);
  border-radius: 4px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

  .title {
    font-size: 14px;
    font-weight: bold;
    color: #0052d9;
    margin-bottom: 16px;
  }
}

.content {
  padding-top: 8px;
  display: flex;
  align-items: center;
  > img {
    width: 48px;
    height: 48px;
    margin-right: 12px;
  }

  .patindUserInfo {
    display: flex;
    flex-direction: column;
    font-weight: normal;
    line-height: 14px;
    color: #86909c;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #000000;
      margin-bottom: 8px;
    }
  }
}

.custom-checkbox-card-mask {
  height: 14px;
  width: 14px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  border: 2px solid #e5e6eb;
  box-sizing: border-box;
}

.custom-checkbox-card-mask-dot {
  width: 8px;
  height: 8px;
  border-radius: 2px;
  background: #fff;
}

.custom-checkbox-card:hover,
.custom-checkbox-card-checked,
.custom-checkbox-card:hover .custom-checkbox-card-mask,
.custom-checkbox-card-checked .custom-checkbox-card-mask {
  border-color: rgb(var(--primary-6));
}

.custom-checkbox-card-checked {
  background-color: var(--color-primary-light-1);
  background: rgba(0, 82, 217, 0.06);
}

.custom-checkbox-card-checked .custom-checkbox-card-mask-dot {
  background-color: rgb(var(--primary-6));
}
</style>
