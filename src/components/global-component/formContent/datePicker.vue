<!--
* @description 日期组件
* @fileName datePicker.vue
* <AUTHOR>
* @date 2025/06/08 16:19:41
!-->
<script setup lang="ts">
import { Message } from "@arco-design/web-vue";
import { ref, computed, onMounted, onUnmounted, watch } from "vue";
import { to } from "await-to-js";
import * as apis from "@/batchApi";
import dayjs from "dayjs";
import { IconCalendar } from "@arco-design/web-vue/es/icon";
import { firstVisitComparisonCodeMap } from "@/constant";
import {
  PayTypeEnum,
  ServiceIdCategory,
  TForm,
} from "../service-appointment.vue";

type IRecord =
  apis.QueryAvailableServiceReservationsUsingPOSTServiceReservationAvailableDto;

const loading = ref<boolean>(false);
const timeList = ref<IRecord[]>([]);
const selectKeys = ref<string[]>([]);
const selectTime = ref<string>();
const showDataPicker = ref(false);
const hasDateChanged = ref<boolean>(false); // 跟踪用户是否切换了日期
const doctorInfoID = ref<string>(); // 下个版本要删除

const props = defineProps<{
  modelValue?: any;
  form: TForm;
  serviceIdList: apis.QueryServiceItemsUsingPOSTServiceItemsDto[];
}>();

const emit = defineEmits(["update:modelValue", "change"]);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit("update:modelValue", value);
    emit("change", value);
  },
});

// 计算显示的日期时间信息
const displayDateTime = computed(() => {
  if (modelValue.value && modelValue.value.date && modelValue.value.startTime) {
    return `${modelValue.value.date} ${modelValue.value.startTime}-${modelValue.value.endTime}`;
  }
  if (selectTime.value && selectKeys.value.length > 0) {
    const selectedSchedule = timeList.value?.find((item: IRecord) =>
      selectKeys.value.includes(item.scheduleId as string),
    );
    if (selectedSchedule) {
      return `${selectTime.value} ${selectedSchedule.startTime}-${selectedSchedule.endTime}`;
    }
  }
  return "";
});

const selectedScheduleObject = computed(() => {
  if (!selectTime.value || !selectKeys.value.length) {
    return undefined;
  }

  // const selectedSchedule = timeList.value?.find((item: any) =>
  //   selectKeys.value.includes(item.scheduleId),
  // );
  const obj = timeList.value?.at(0);
  const selectedIntervalIdObj = obj?.classesDetailList?.find(item =>
    selectKeys.value.includes(item?.intervalId as string),
  );

  if (obj) {
    return {
      ...obj,
      ...selectedIntervalIdObj,
      date: selectTime.value,
      doctorId: doctorInfoID.value,
    };
  }

  return undefined;
});

const doctorId = computed(() => {
  const { form } = props;
  return form?.doctorItem?.doctorId;
});

const serviceId = computed(() => {
  const { firstVisitList } = props.form;
  if (!props.serviceIdList?.length) return;
  const count = firstVisitList.length;
  // if (serviceIdRecord?.serviceCode === ServiceIdCategory.SZYY) {
  //   const count = firstVisitList.length;
  //   const result = serviceIdRecord?.child?.find(
  //     (item: any) =>
  //       item?.serviceCode ===
  //       firstVisitComparisonCodeMap.get(count)?.serviceCode,
  //   );
  //   return result?.serviceId;
  // }
  const result = props.serviceIdList?.find(
    item =>
      item?.serviceCode === firstVisitComparisonCodeMap.get(count)?.serviceCode,
  );
  return result?.serviceId;
});

const getTimeList = async (date: string) => {
  // const { form } = props;
  loading.value = true;
  // if (form?.payType === PayTypeEnum.QYHX) {
  //   const [errs, doctorInfo] = await to(
  //     apis.getBdManageApiServiceReservationGetPatientDoctorId({
  //       patientId: form?.userInfo?.patientId,
  //       serviceCode: form?.packageCodeItem?.serviceCode as string,
  //     }),
  //   );
  //   doctorInfoID.value = doctorInfo?.data;
  // }

  const [err, res] = await to(
    apis.postBdManageApiServiceReservationQueryAvailableServiceReservations({
      doctorId: doctorId.value as string,
      // doctorId:
      //   form?.payType === PayTypeEnum.QYHX
      //     ? (doctorInfoID?.value as string)
      //     : doctorId.value,
      serviceId: serviceId.value as string,
      date, // 使用传入的日期参数
    }),
  );
  loading.value = false;
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    timeList.value = res?.data as IRecord[];
  }
};

const disabledDete = (current: any) => {
  const today = new Date();
  const preDate = new Date().setDate(today.getDate() - 1);
  const sevenDaysLater = new Date(today);
  sevenDaysLater.setDate(today.getDate() + 13);

  return current < preDate || current > sevenDaysLater;
};

const handleOpenPicker = (visible: boolean) => {
  showDataPicker.value = visible;
};

const handleInputClick = () => {
  showDataPicker.value = true;
};

const handleSelect = async (time: string) => {
  // 如果用户选择了不同的日期，清空之前的时间
  if (selectTime.value && selectTime.value !== time) {
    modelValue.value = undefined;
  }
  selectTime.value = time;
  hasDateChanged.value = true; // 标记用户切换了日期
  selectKeys.value = [];
  await getTimeList(time);
};

const handleChecked = (scheduleId: string) => {
  if (selectKeys.value?.includes(scheduleId)) {
    selectKeys.value = selectKeys.value?.filter(key => key !== scheduleId);
  } else {
    // 只允许选择一个时间段
    selectKeys.value = [scheduleId];
    hasDateChanged.value = false; // 用户选择了时间段，重置标记
  }
};

const handleOk = () => {
  if (!selectKeys?.value?.length) {
    Message.info("您还没有选择时间段");
    return;
  }
  if (selectTime.value && selectKeys.value.length > 0) {
    // 绑定对象
    const finalValue = selectedScheduleObject.value;
    modelValue.value = finalValue;
    reservationOccupy(finalValue?.intervalId as string);
  }

  showDataPicker.value = false;
};

const patientId = computed(() => {
  const { form } = props;
  return form?.userInfo?.patientId;
});

/* 占号 */
const reservationOccupy = async (intervalId: string) => {
  const [err, res] = await to(
    apis.postBdManageApiServiceReservationOccupy({
      patientId: patientId.value,
      intervalId,
    }),
  );
  if (err) {
    throw err;
  }
};

// 点击外部关闭弹窗
const handleClickOutside = (event: Event) => {
  if (!showDataPicker.value) return;

  const target = event.target as HTMLElement;
  const isClickOutside =
    !target.closest(".arco-picker") &&
    !target.closest(".arco-picker-popup") &&
    !target.closest(".arco-trigger-popup") &&
    !target.closest(".date-picker-wrapper") &&
    !target.closest(".datePickerView");

  if (isClickOutside) {
    // 如果用户切换了日期但没有选择时间段，清空之前的时间
    if (hasDateChanged.value && !selectKeys.value.length) {
      selectTime.value = "";
      modelValue.value = undefined;
    }
    showDataPicker.value = false;
    hasDateChanged.value = false;
  }
};

// 移除滚动关闭逻辑
// const handleScroll = () => {
//   if (showDataPicker.value) {
//     showDataPicker.value = false;
//     hasDateChanged.value = false;
//   }
// };

// 监听全局点击和滚动事件
onMounted(() => {
  document.addEventListener("click", handleClickOutside);
  // 移除滚动事件监听
  // window.addEventListener("scroll", handleScroll, true);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
  // window.removeEventListener("scroll", handleScroll, true);
});

watch(
  () => props.modelValue,
  newValue => {
    if (newValue && typeof newValue === "object" && newValue.date) {
      // 如果外部传入了时间段对象，解析日期和时间段
      selectTime.value = newValue.date as string;
      // 设置选中的时间段
      selectKeys.value = [newValue.scheduleId];
      // 获取该日期的时间段
      getTimeList(selectTime.value);
    } else if (newValue && typeof newValue === "string") {
      // 如果外部传入了日期字符串，解析日期
      const dateObj = dayjs(newValue);
      selectTime.value = dateObj.format("YYYY-MM-DD");
      selectKeys.value = [];
      getTimeList(selectTime.value);
    } else {
      selectTime.value = undefined;
      selectKeys.value = [];
    }
  },
  {
    immediate: true,
  },
);
</script>

<template>
  <div class="date-picker-wrapper">
    <!-- 隐藏的日期选择器，用于弹窗定位 -->
    <a-date-picker
      ref="datePickerRef"
      style="position: absolute; opacity: 0; pointer-events: none; width: 530px"
      :trigger-props="{
        popupStyle: { width: '530px' },
        contentClass: 'datePickerView',
        trigger: 'click',
        clickOutsideToClose: true,
        scrollToClose: false,
        autoFitPosition: true,
        position: 'bottom',
        updateAtScroll: true,
      }"
      :model-value="
        modelValue?.date
          ? dayjs(modelValue.date)
          : selectTime
            ? dayjs(selectTime)
            : undefined
      "
      :disabled-date="disabledDete"
      :popup-visible="showDataPicker"
      show-confirm-btn
      @popup-visible-change="handleOpenPicker"
      @select="handleSelect"
    >
      <template #extra>
        <a-spin style="width: 100%" :loading="loading">
          <div @click.stop>
            <div class="title">可预约时间</div>
            <div v-if="!timeList?.length" class="empty">
              暂无号源,请选择其他日期
            </div>
            <div v-else class="timeView">
              <div
                v-for="time_item in timeList?.[0]?.classesDetailList"
                :key="time_item?.intervalId"
                :class="[
                  'time',
                  { disabledTime: time_item?.status === '1' },
                  {
                    acticeTime: selectKeys?.includes(
                      time_item?.intervalId as string,
                    ),
                  },
                ]"
                @click="handleChecked(time_item?.intervalId as string)"
              >
                {{ time_item?.startTime }}-{{ time_item?.endTime }}
              </div>
            </div>
            <div class="sureBtnView">
              <div class="sureBtn" @click="handleOk">确定</div>
            </div>
          </div>
        </a-spin>
      </template>
    </a-date-picker>

    <!-- 自定义输入框，用于显示和触发 -->
    <a-input
      :model-value="displayDateTime"
      placeholder="请选择预约时间"
      readonly
      style="width: 534px"
      @click.stop="handleInputClick"
    >
      <template #suffix>
        <IconCalendar />
      </template>
    </a-input>
  </div>
</template>

<style lang="scss" scoped>
.date-picker-wrapper {
  position: relative;
  display: inline-block;
  width: 534px;

  .arco-input {
    cursor: pointer;

    &:hover {
      border-color: var(--color-primary-light-4);
    }
  }

  // 隐藏的日期选择器样式
  .arco-picker {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    pointer-events: none;
    z-index: -1;
  }
}

.title {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 16px;
}

.empty {
  text-align: center;
  color: rgba(0, 0, 0, 0.5);
}
.timeView {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;

  .time {
    padding: 5px 16px;
    background: #ededed;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    color: #000;
    cursor: pointer;
  }
  .acticeTime {
    background: #0052d9;
    color: #ffffff;
  }

  .disabledTime {
    pointer-events: none;
    opacity: 0.5;
  }
}

.sureBtnView {
  display: flex;
  justify-content: flex-end;
  cursor: pointer;
  .sureBtn {
    width: 60px;
    margin-top: 20px;
    background: #0052d9;
    color: #ffffff;
    padding: 8px 16px;
    border-radius: 20px;
  }
}
</style>
