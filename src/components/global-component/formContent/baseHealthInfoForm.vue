<!--
* @description 基本健康信息
* @fileName baseHealthInfo.vue
* <AUTHOR>
* @date 2025/06/08 09:26:00
!-->
<script setup lang="ts">
import CardView from "./cardView.vue";
import { TForm } from "../service-appointment.vue";

const form = defineModel<TForm>("form", { required: true });
type TFormKeys = keyof TForm;
const handleChange = (value: string, field: TFormKeys) => {
  if (value === "false") {
    (form.value[field] as any) = undefined;
  }
};
</script>

<template>
  <CardView title="基本健康信息">
    <a-form-item
      field="hasCurrentHistory"
      label="现病史"
      :rules="[
        {
          required: true,
          message: '请选择现病史',
        },
      ]"
      @change="(e: any) => handleChange(e.target.value, 'currentHistory')"
    >
      <a-radio-group v-model="form.hasCurrentHistory">
        <a-radio :value="true">有</a-radio>
        <a-radio :value="false">无</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item
      v-if="form.hasCurrentHistory"
      field="currentHistory"
      :rules="[
        {
          required: form.hasCurrentHistory,
          message: '请输入现病史',
        },
      ]"
    >
      <a-textarea
        v-model="form.currentHistory"
        placeholder="您现在有患过什么疾病吗？如糖尿病、高血压等，请输入..."
        allow-clear
      />
    </a-form-item>
    <a-form-item
      field="hasPastDisease"
      label="过往病史"
      :rules="[
        {
          required: true,
          message: '请选择过往病史',
        },
      ]"
      @change="(e: any) => handleChange(e.target.value, 'pastDisease')"
    >
      <a-radio-group v-model="form.hasPastDisease">
        <a-radio :value="true">有</a-radio>
        <a-radio :value="false">无</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item
      v-if="form.hasPastDisease"
      field="pastDisease"
      :rules="[
        {
          required: form.hasPastDisease,
          message: '请填写',
        },
      ]"
    >
      <a-textarea
        v-model="form.pastDisease"
        placeholder="您是否曾患过什么疾病或者既往健康状况，请输入...."
        allow-clear
      />
    </a-form-item>
    <a-form-item
      field="hasAllergyHistory"
      label="过敏史"
      :rules="[
        {
          required: true,
          message: '请选择过敏史',
        },
      ]"
      @change="(e: any) => handleChange(e.target.value, 'allergyHistory')"
    >
      <a-radio-group v-model="form.hasAllergyHistory">
        <a-radio :value="true">有</a-radio>
        <a-radio :value="false">无</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item
      v-if="form.hasAllergyHistory"
      field="allergyHistory"
      :rules="[
        {
          required: form.hasAllergyHistory,
          message: '请填写',
        },
      ]"
    >
      <a-textarea
        v-model="form.allergyHistory"
        placeholder="您是否有某些过敏原过敏的病史，请输入...."
        allow-clear
      />
    </a-form-item>
  </CardView>
</template>

<style lang="scss" scoped></style>
