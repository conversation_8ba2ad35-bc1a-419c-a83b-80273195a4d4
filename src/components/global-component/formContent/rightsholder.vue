<!--
* @description 权益人
* @fileName rightsholder.vue
* <AUTHOR>
* @date 2025/06/08 10:35:16
!-->
<script setup lang="ts">
import { getImageSrc } from "@/utils";
import { ref } from "vue";
import { QuerySelectedPatientUsingPOSTMainAndSharePatientDto } from "@/batchApi";
import { TForm } from "../service-appointment.vue";

type Props = {
  data?: QuerySelectedPatientUsingPOSTMainAndSharePatientDto[];
};
defineProps<Props>();
</script>

<template>
  <a-checkbox-group v-bind="$attrs">
    <div class="majorView">
      <div class="tips" style="background: #07b9b9; line-height: 12px">
        共享权益人
      </div>
      <div class="scrollView">
        <template v-for="item in data" :key="item">
          <a-checkbox :value="item">
            <template #checkbox="{ checked }">
              <a-space
                align="start"
                class="custom-checkbox-card"
                :class="{ 'custom-checkbox-card-checked': checked }"
              >
                <div class="content">
                  <img :src="getImageSrc('woman.png')" />
                  <div class="patindUserInfo">
                    <span class="title">{{ item.patientName }}</span>
                    <span>{{ item?.age ?? "-" }}岁 {{ item?.sex }}</span>
                  </div>
                </div>
                <div className="custom-checkbox-card-mask">
                  <div className="custom-checkbox-card-mask-dot" />
                </div>
              </a-space>
            </template>
          </a-checkbox>
        </template>
      </div>
    </div>
  </a-checkbox-group>
</template>

<style lang="scss" scoped>
:deep(.arco-checkbox) {
  padding-left: 0px;
}
.custom-checkbox-card {
  padding: 8px 12px 16px 16px;
  border-radius: 8px;
  width: 176px;
  box-sizing: border-box;
  background: rgba(0, 82, 217, 0.06);
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.majorView {
  display: flex;

  .tips {
    width: 20px;
    height: 80px;
    background: #0052d9;
    color: #ffffff;
    font-size: 12px;
    line-height: 14px;
    text-align: center;
    padding: 12px 0;
    margin-right: 12px;
  }
  .scrollView {
    width: calc(650px - 68px);
    display: flex;
    overflow-x: auto;
  }
}

.content {
  padding-top: 8px;
  display: flex;
  align-items: center;
  > img {
    width: 48px;
    height: 48px;
    margin-right: 12px;
  }

  .patindUserInfo {
    display: flex;
    flex-direction: column;
    font-weight: normal;
    line-height: 14px;
    color: #86909c;

    .title {
      font-size: 16px;
      font-weight: bold;
      color: #000000;
      margin-bottom: 8px;
    }
  }
}

.custom-checkbox-card-mask {
  height: 14px;
  width: 14px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  border: 2px solid #e5e6eb;
  box-sizing: border-box;
}

.custom-checkbox-card-mask-dot {
  width: 8px;
  height: 8px;
  border-radius: 2px;
  background: #fff;
}

.custom-checkbox-card:hover,
.custom-checkbox-card-checked,
.custom-checkbox-card:hover .custom-checkbox-card-mask,
.custom-checkbox-card-checked .custom-checkbox-card-mask {
  border-color: rgb(var(--primary-6));
}

.custom-checkbox-card-checked {
  background-color: var(--color-primary-light-1);
  background: rgba(0, 82, 217, 0.06);
}

.custom-checkbox-card-checked .custom-checkbox-card-mask-dot {
  background-color: rgb(var(--primary-6));
}
</style>
