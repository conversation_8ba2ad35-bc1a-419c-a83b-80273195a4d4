<!--
* @description 预约信息
* @fileName serviceInformationForm.vue
* <AUTHOR>
* @date 2025/06/08 17:41:17
!-->
<script setup lang="ts">
import { computed, watch, ref } from "vue";
import { to } from "await-to-js";
import * as apis from "@/batchApi";
import { SelectOptionData } from "@arco-design/web-vue";
import CardView from "./cardView.vue";
import {
  TForm,
  IDictionaryState,
  ServiceIdCategory,
  PayTypeEnum,
  FirstVisitEnum,
} from "../service-appointment.vue";
import rightsholder from "./rightsholder.vue";
import datePicker from "./datePicker.vue";

type Props = {
  dictionaryState: IDictionaryState;
};

type DoctorRecord = apis.GetTeamDoctorUsingPOSTDoctorInfoDto;
const form = defineModel<TForm>("form", { required: true });
const props = defineProps<Props>();
const loading = ref<boolean>(false);
const searchVal = ref();
const searchLoading = ref<boolean>(false);
const searchDoctorList = ref<Array<DoctorRecord>>([]);
const emits = defineEmits(["clearDictionaryState"]);

const sharedPatientList = ref<
  apis.QuerySelectedPatientUsingPOSTMainAndSharePatientDto[]
>([]);

const serviceIdRecordList = computed(() => {
  if (form.value.payType === PayTypeEnum.QYHX) {
    return props.dictionaryState.serviceItems1;
  }
  if (form.value.payType === PayTypeEnum.HZZF) {
    return props.dictionaryState.serviceItems;
  }
  return [];
});

const handleChange = (e: any) => {
  if (e.target.value === FirstVisitEnum.XSSZ) {
    form.value.firstVisitOrgId = undefined;
    form.value.isReservedParking = undefined;
  }
};

const handlePayTypeChange = (e: any) => {
  emits("clearDictionaryState");
  form.value.packageCodeItem = undefined;
  form.value.serviceIdRecord = undefined;
  form.value.doctorItem = undefined;
};

/* 获取共享权益人 */
const querySharedPatient = async (mainPatientId: number) => {
  loading.value = true;
  const [err, res] = await to(
    apis.postBdManageApiPatientQuerySelectedPatient({ mainPatientId }),
  );
  loading.value = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    const result = res?.data?.filter(item => !item?.isMainPatient);
    sharedPatientList.value = result;
  }
};

const showSharePatient = computed(() => {
  if (
    sharedPatientList.value?.length &&
    form.value.userInfo &&
    form.value.serviceIdRecord?.serviceCode === ServiceIdCategory.SZYY
  ) {
    return true;
  }
  return false;
});

const getOtherDoctorList = async () => {
  searchLoading.value = true;
  const [err, res] = await to(
    apis.postBdManageApiServiceReservationGetInternetDoctorList({
      name: searchVal.value,
    }),
  );
  searchLoading.value = false;
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    const result = res?.data?.map(item => ({
      doctorId: item?._id,
      doctorName: item?.name,
      doctorPhone: item?.phone,
      doctorUserId: undefined,
      majorName: item?.major_name,
      orgName: item?.org_name,
      titleName: item?.title,
    }));
    searchDoctorList.value =
      result as apis.GetTeamDoctorUsingPOSTDoctorInfoDto[];
  }
};

// 根据选择的人，清空时间
watch(
  () => [form.value.originatorsInfo, form.value.doctorItem],
  () => {
    form.value.reservationTime = undefined;
  },
);

// 有主权益人ID，或者服务项目为首诊预约
watch(
  () => [form.value.userInfo, form.value.serviceIdRecord],
  ([newUserInfo, newServiceIdRecord]) => {
    if (
      newUserInfo?.mainPatientId &&
      newServiceIdRecord?.serviceCode === ServiceIdCategory.SZYY
    ) {
      querySharedPatient(newUserInfo.mainPatientId);
    }
  },
);
</script>

<template>
  <CardView title="预约信息">
    <a-form-item
      field="payType"
      label="支付类型"
      :rules="[
        {
          required: true,
          message: '请选择支付类型',
        },
      ]"
      @change="handlePayTypeChange"
    >
      <a-radio-group v-model="form.payType">
        <a-radio :value="PayTypeEnum.QYHX">权益核销</a-radio>
        <a-radio :value="PayTypeEnum.HZZF">患者支付</a-radio>
      </a-radio-group>
    </a-form-item>
    <!-- <a-form-item
      field="channelId"
      label="预约渠道"
      :rules="[
        {
          required: true,
          message: '请选择预约渠道',
        },
      ]"
    >
      <a-select
        v-model="form.channelId"
        placeholder="请选择"
        :loading="dictionaryState.loadingReservationChannel"
      >
        <a-option
          v-for="item in dictionaryState.reservationChannel"
          :key="item.channelId"
          :value="item.channelId"
        >
          {{ item.name }}
        </a-option>
      </a-select>
    </a-form-item> -->
    <a-form-item
      v-if="form.payType === PayTypeEnum.QYHX"
      field="packageCodeItem"
      label="权益包"
      :rules="[
        {
          required: true,
          message: '请选择权益包',
        },
      ]"
    >
      <a-select
        v-model="form.packageCodeItem"
        placeholder="请选择"
        value-key="serviceId"
        :loading="dictionaryState.servicePackageLoading"
      >
        <a-option
          v-for="item in dictionaryState.servicePackage"
          :key="item.serviceId"
          :value="item"
        >
          {{ item.serviceName }}
        </a-option>
      </a-select>
    </a-form-item>
    <a-form-item
      field="serviceIdRecord"
      label="服务项目"
      :rules="[
        {
          required: true,
          message: '请选择服务项目',
        },
      ]"
    >
      <a-select
        v-model="form.serviceIdRecord"
        :loading="dictionaryState.loadingServiceItems"
        placeholder="请选择"
        value-key="serviceId"
        :format-label="
          (data: SelectOptionData | any) => data?.value?.serviceName
        "
      >
        <a-option
          v-for="item in serviceIdRecordList"
          :key="item.serviceId"
          :value="item"
          :disabled="
            form.payType === PayTypeEnum.QYHX
              ? !(item?.serviceItemCount > 0)
              : false
          "
        >
          <span class="optionSty">
            <label>{{ item.serviceName }}</label>
            <label v-if="form.payType === PayTypeEnum.QYHX" class="tag"
              >可用次数： {{ item?.serviceItemCount ?? "0" }}</label
            >
          </span>
        </a-option>
        <!-- <a-option value="1">首诊预约</a-option>
        <a-option value="2">互联网医院服务项目</a-option>
        <a-option value="3">体检报告解读</a-option>
        <a-option value="4">营养评估</a-option>
        <a-option value="5">体检报告解读</a-option> -->
      </a-select>
    </a-form-item>
    <a-spin
      :style="{
        width: '100%',
      }"
      :loading="loading"
    >
      <div v-if="showSharePatient">
        <div class="tipTitle">
          选择共享权益人<span>（可选以下共享权益人一同就诊）</span>
        </div>
        <a-form-item
          field="originatorsInfo"
          :label-col-props="{ span: 0 }"
          :wrapper-col-props="{ span: 24 }"
        >
          <rightsholder
            v-model="form.originatorsInfo"
            :max="8"
            :data="sharedPatientList"
          />
        </a-form-item>
      </div>
    </a-spin>
    <a-form-item
      v-if="form.serviceIdRecord?.serviceCode === ServiceIdCategory.SZYY"
      field="firstVisitType"
      label="首诊类型"
      :rules="[
        {
          required: true,
          message: '请选择首诊类型',
        },
      ]"
      @change="handleChange"
    >
      <a-radio-group v-model="form.firstVisitType">
        <a-radio :value="FirstVisitEnum.XSSZ">线上首诊</a-radio>
        <a-radio :value="FirstVisitEnum.XXSZ">线下首诊</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item
      v-if="form.firstVisitType === FirstVisitEnum.XXSZ"
      field="firstVisitOrgId"
      label="首诊机构"
      :rules="[
        {
          required: form.firstVisitType === FirstVisitEnum.XXSZ,
          message: '请选择首诊机构',
        },
      ]"
    >
      <a-select
        v-model="form.firstVisitOrgId"
        :loading="dictionaryState.organLoading"
        placeholder="请选择"
        allow-search
      >
        <!-- <a-option
          v-for="item in dictionaryState?.organList"
          :key="item.organId"
          :value="item.organId"
        >
          {{ item.name }}
        </a-option> -->
        <a-option value="怡健殿方圆中心店"> 怡健殿方圆中心店 </a-option>
      </a-select>
    </a-form-item>
    <a-form-item
      field="doctorItem"
      label="服务医生"
      :rules="[
        {
          required: true,
          message: '请选择服务医生',
        },
      ]"
    >
      <a-select
        v-model="form.doctorItem"
        placeholder="请选择"
        class="doctorSelect"
        :loading="dictionaryState.signDoctorLoading"
        show-header-on-empty
        :show-extra-options="true"
        value-key="doctorId"
        allow-clear
        @popup-visible-change="searchVal = undefined"
      >
        <template #header>
          <div class="headerSearch">
            <a-input-search
              v-model="searchVal"
              :loading="searchLoading"
              class="searchInput"
              placeholder="搜索其他医生"
              @search="getOtherDoctorList"
              @press-enter="getOtherDoctorList"
              @click.stop
              @focus.stop
              @mousedown.stop
            />
          </div>
        </template>
        <a-optgroup v-if="dictionaryState.signDoctorList?.length">
          <template #label
            ><span class="optgroupStyle">签约医生</span></template
          >
          <a-option
            v-for="signItem in dictionaryState.signDoctorList"
            :key="signItem?.doctorId"
            :value="signItem"
          >
            <span class="optionItem">
              <span
                >{{ signItem?.doctorName
                }}{{ signItem?.titleName ? `/${signItem?.titleName}` : "" }}
                {{ signItem?.orgName ? `/${signItem?.orgName}` : "" }}</span
              >
              <span v-if="signItem?.majorName" class="tag">{{
                signItem?.majorName
              }}</span>
            </span>
          </a-option>
        </a-optgroup>
        <a-optgroup>
          <template #label>
            <div class="optgroupStyle">其他医生</div>
          </template>
          <a-option
            v-for="signItem in searchDoctorList"
            :key="signItem?.doctorId"
            :value="signItem"
          >
            <span class="optionItem">
              <span
                >{{ signItem?.doctorName
                }}{{ signItem?.titleName ? `/${signItem?.titleName}` : "" }}
                {{ signItem?.orgName ? `/${signItem?.orgName}` : "" }}</span
              >
              <span v-if="signItem?.majorName" class="tag">{{
                signItem?.majorName
              }}</span>
            </span>
          </a-option>
        </a-optgroup>
      </a-select>
    </a-form-item>
    <a-form-item
      v-if="form.userInfo && form.serviceIdRecord && form.doctorItem"
      field="reservationTime"
      label="预约时间"
      :rules="[
        {
          required: true,
          message: '请选择预约时间',
        },
      ]"
    >
      <datePicker
        v-model="form.reservationTime"
        :form="form"
        :service-id-list="dictionaryState.serviceIdList"
      />
    </a-form-item>
    <a-form-item
      v-if="form.firstVisitType === FirstVisitEnum.XXSZ"
      field="isReservedParking"
      label="预留车位"
      :rules="[
        {
          required: form.firstVisitType === FirstVisitEnum.XXSZ,
          message: '请选择首诊类型',
        },
      ]"
    >
      <a-radio-group v-model="form.isReservedParking">
        <a-radio :value="true">是</a-radio>
        <a-radio :value="false">否</a-radio>
      </a-radio-group>
    </a-form-item>
  </CardView>
</template>

<style lang="scss" scoped>
.headerSearch {
  padding: 0 12px;

  .searchInput {
    margin: 12px 0;
  }
}

:deep(.arco-select-option-content) {
  width: 100%;
}

.doctorSelect {
  :deep(.arco-select-option-content) {
    width: 100%;
  }
}

.optgroupStyle {
  color: #86909c;
  font-size: 14px;
  font-weight: 400;
}

.optionItem {
  width: 100%;
  font-size: 14px;
  color: #000000;
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f2f3f5;

  .tag {
    display: flex;
    align-items: center;
    height: 20px;
    padding: 4px;
    border-radius: 4px;
    background: #fff7e8;
    color: #ff7d00;
    font-size: 12px;
  }
}
.tipTitle {
  font-size: 14px;
  font-weight: bold;
  color: #000000;
  margin-bottom: 20px;

  > span {
    font-size: 12px;
  }
}

.optionSty {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .tag {
    display: flex;
    align-items: center;
    height: 20px;
    padding: 4px;
    border-radius: 4px;
    background: #fff7e8;
    color: #ff7d00;
    font-size: 12px;
  }
}
</style>
