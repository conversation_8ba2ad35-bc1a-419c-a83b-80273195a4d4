<!--
* @description 选择主权益人
* @fileName dominant.vue
* <AUTHOR>
* @date 2025/06/20 17:00:39
!-->
<script setup lang="ts">
import { ref } from "vue";
import CardView from "./cardView.vue";
import { TForm } from "../service-appointment.vue";
import userSelect from "./userSelect.vue";

const form = defineModel<TForm>("form", { required: true });
</script>

<template>
  <CardView title="选择权益人">
    <a-form-item
      field="userInfo"
      :label-col-props="{ span: 0 }"
      :wrapper-col-props="{ span: 24 }"
      :rules="[{ required: true, message: '请选择选择权益人' }]"
    >
      <userSelect
        v-model="form.userInfo"
        @clear-originators="form.originatorsInfo = []"
      />
    </a-form-item>
    <div v-if="!form.userInfo" class="emptyUser">
      暂无信息，请先进行搜索并选择权益人
    </div>
  </CardView>
</template>

<style lang="scss" scoped></style>
