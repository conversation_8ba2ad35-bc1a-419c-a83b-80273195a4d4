<!--
 * @Description: 服务预约的全局组件
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-12-17 17:38:07
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-25 17:17:22
-->
<template>
  <!-- 服务预约的组件 -->
  <a-drawer
    unmount-on-close
    :width="650"
    :visible="visible"
    :footer="!isView"
    :mask-closable="false"
    :on-before-ok="handleOnOk"
    @cancel="handleCancel"
  >
    <template #title>
      {{ isView ? "查看" : "保险服务" }}
    </template>
    <!-- 服务预约的内容 -->
    <a-spin
      :style="{
        width: '100%',
      }"
      :loading="loadingContent.loadingData"
    >
      <a-form
        ref="modalFormRef"
        :model="form"
        :label-col-props="{ span: 10 }"
        :wrapper-col-props="{ span: 14 }"
        :disabled="isView"
        label-align="left"
        class="serviceAppointmentForm"
      >
        <a-form-item
          field="userInfo"
          hide-label
          :rules="[{ required: true, message: '请选择需要预约服务的患者' }]"
        >
          <bd-user-select
            v-model="form.userInfo"
            :disabled="isView"
            :default-patient-id="curPatientId"
          />
        </a-form-item>
        <div class="serviceAppointmentFormItems">
          <a-form-item
            field="reservationChannel"
            label="预约渠道"
            show-colon
            :rules="[
              {
                required: true,
                message: '请选择预约渠道',
              },
            ]"
          >
            <a-select
              v-model="form.reservationChannel"
              placeholder="请选择"
              :loading="loadingContent.loadingReservationChannel"
            >
              <a-option
                v-for="item in reservationChannel"
                :key="item.dictValue"
                :value="item.dictValue"
              >
                {{ item.dictLabel }}
              </a-option>
            </a-select>
          </a-form-item>
          <!-- <a-form-item
            field="serviceCategory"
            label="服务分类"
            show-colon
            :rules="[
              {
                required: true,
                message: '请选择服务分类',
              },
            ]"
          >
            <a-select
              v-model="form.serviceCategory"
              value-key="label"
              placeholder="请选择"
              :loading="
                loadingContent.loadingProjectList || loadingContent.rightsItem
              "
              @change="
                () => {
                  form.project = undefined;
                }
              "
            >
              <a-option
                v-for="item in projectList"
                :key="item.dictValue"
                :value="item.dictValue"
              >
                {{ item.dictLabel }}
              </a-option>
            </a-select>
          </a-form-item> -->
          <div :style="{ gridColumn: '1 / span 2' }">
            <a-form-item
              field="project"
              label="服务项目"
              show-colon
              :label-col-props="{ span: 5 }"
              :wrapper-col-props="{ span: 19 }"
              :rules="[
                {
                  required: true,
                  message: '请选择服务项目',
                },
              ]"
            >
              <a-select
                v-model="form.project"
                placeholder="请选择"
                value-key="uuid"
                :loading="loadingContent.rightsItem"
              >
                <a-option
                  v-for="item in rightsItemList"
                  :key="item.uuid"
                  :value="item"
                >
                  {{ item.serviceItemName }}
                </a-option>
                <!-- <a-option :value="EProjectType.PZ">陪诊</a-option>
                <a-option :value="EProjectType.PH">陪护</a-option>
                <a-option :value="EProjectType.ZY">住院绿通</a-option>
                <a-option :value="EProjectType.MZ">门诊绿通</a-option>
                <a-option :value="EProjectType.ZJ">专家二诊</a-option>
                <a-option :value="EProjectType.KF">康复护理</a-option> -->
              </a-select>
              <a-button
                v-if="!isView"
                style="margin-left: 10px"
                type="primary"
                :disabled="!form.userInfo?.name"
                @click="() => (servicePurchaseVisible = true)"
                >去购买</a-button
              >
            </a-form-item>
          </div>
          <a-form-item
            field="treatmentItemCode"
            label="治疗类型"
            show-colon
            :rules="[
              {
                required: true,
                message: '请选择治疗类型',
              },
            ]"
          >
            <a-select
              v-model="form.treatmentItemCode"
              placeholder="请选择"
              :loading="loadingContent.loadingTreatmentItem"
            >
              <a-option
                v-for="item in treatmentItem"
                :key="item.dictValue"
                :value="item.dictValue"
              >
                {{ item.dictLabel }}
              </a-option>
            </a-select>
          </a-form-item>
        </div>
        <use-user ref="useUserFormRef" :is-view="isView" />
        <!-- 几个表单的公共字段 -->
        <ez-form
          v-if="form.serviceCategory === EProjectType.ZJEZ"
          ref="contentFormRef"
          :is-view="isView"
        />
        <kfhl-form
          v-if="form.serviceCategory === EProjectType.KFHL"
          ref="contentFormRef"
          :rights-item="form.project"
          :rights-item-list="rightsItemList"
          :is-view="isView"
        />
        <!-- 住院绿通服务的表单 -->
        <zylt-Form
          v-if="
            [
              EProjectType.ZYXZ,
              EProjectType.MZXZ,
              EProjectType.PH,
              EProjectType.PZ,
            ].includes(form.serviceCategory as EProjectType)
          "
          ref="contentFormRef"
          :service-category="form.serviceCategory"
          :rights-item="form.project"
          :is-view="isView"
        />
        <a-form-item
          v-if="isView"
          field="createAt"
          label="预约提交时间"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
          show-colon
        >
          <a-date-picker v-model="form.createAt" style="width: 100%" />
        </a-form-item>
        <!-- TODO: 当前没有状态的枚举 等到 有枚举的时候 根据枚举值来展示 取消时间 -->
        <!-- <a-form-item
          v-if="isView"
          field="cancelTime"
          label="取消时间"
          show-colon
        >
          <a-date-picker
            v-model="form.cancelTime"
            style="width: 100%"
          />
        </a-form-item> -->
      </a-form>
    </a-spin>
    <ServicePurchaseModal
      v-model:visible="servicePurchaseVisible"
      :user-info="form.userInfo"
    />
  </a-drawer>
</template>

<script setup lang="ts">
import useQuickAccessStore from "@/store/modules/quick-access";
import { computed, onMounted, onUpdated, reactive, ref, watch } from "vue";
import bdUserSelect from "@/components/bd-user-select/index.vue";
import { FormInstance } from "@arco-design/web-vue";
import dayjs from "dayjs";
import {
  getBdManageApiSystemDictDataTypeByDictType,
  getBdManageApiSystemDictDataTypeByDictTypeResponse,
  getBdManageApiSystemDictTypeList,
  getBdManageApiSystemDictTypeListResponse,
  postBdManageApiHealthInsuranceApplyOrder,
  postBdManageApiHealthInsuranceQueryById,
  postBdManageApiHealthInsuranceQueryRights,
} from "@/batchApi";
import to from "await-to-js";
import zyltForm, { TZyltForm } from "./components/zylt-form.vue";
import pzForm from "./components/pz-form.vue";
import phForm from "./components/ph-form.vue";
import ezForm, { TEzForm } from "./components/ez-form.vue";
import kfhlForm, { TKfhlForm } from "./components/kfhl-form.vue";
import mzldForm from "./components/mzld-form.vue";
import useUser, { TUseUserForm } from "./components/use-user.vue";
import ServicePurchaseModal from "./components/service-purchase.vue";

const modalFormRef = ref<FormInstance>();
const useUserFormRef = ref<{
  formInstance: FormInstance;
  setFormData: (data: any) => void;
}>();
const contentFormRef = ref<{
  formInstance: FormInstance;
  setFormData: (data: any) => void;
}>();

const visible = ref(false);
const servicePurchaseVisible = ref<boolean>(false);
const form = reactive<TForm>({
  // 使用人的相关信息
  userInfo: {
    patientId: "",
  },
  // 服务大类
  serviceCategory: "",
  project: undefined,
  treatmentItemCode: "",
  createAt: dayjs().format("YYYY-MM-DD"),
  // 预约渠道
  reservationChannel: "",
  cancelTime: "",
});

const loadingContent = reactive({
  rightsItem: false,
  hospital: false,
  // 全局的加载状态
  loadingData: false,
  loadingReservationChannel: false,
  loadingTreatmentItem: false,
  loadingProjectList: false,
});

// const rightsList = ref<TRightList>([]);
const rightsItemList = ref<TRightList>([]);
const projectList = ref<
  getBdManageApiSystemDictDataTypeByDictTypeResponse["data"]
>([]);

const reservationChannel = ref<
  getBdManageApiSystemDictDataTypeByDictTypeResponse["data"]
>([]);
const treatmentItem = ref<
  getBdManageApiSystemDictDataTypeByDictTypeResponse["data"]
>([]);

const quickAccessStore = useQuickAccessStore();

const isView = computed(() => {
  return quickAccessStore.quickAccessState.serviceReservationId !== "";
});

const curPatientId = computed(() => {
  return quickAccessStore.quickAccessState.patientId;
});

watch(
  () => quickAccessStore.quickAccessState.showServiceReservation,
  () => {
    visible.value = quickAccessStore.quickAccessState.showServiceReservation;
  },
);

watch(
  () => form.userInfo,
  () => {
    handleRightsInfo();
    useUserFormRef.value?.setFormData({
      userName: form.userInfo.name,
      userMobile: form.userInfo.contactMobile,
      userIdType: form.userInfo.certType || form.userInfo.idType,
      userIdNo: form.userInfo.idCard,
      userSex: String(form.userInfo.gender) === "1" ? "M" : "F",
      userBirthday: form.userInfo.birthdate,
    });
  },
);

// watch(
//   () => form.serviceCategory,
//   () => {
//     if (form.serviceCategory) {
//       // 根据服务包的分类 来展示服务项目的列表
//       rightsItemList.value = rightsList.value.filter(
//         _fi => _fi.parentType === form.serviceCategory,
//       );
//       console.log("form.project", form.project, rightsItemList.value);
//     }
//   },
// );

watch(
  () => form.project,
  () => {
    if (
      typeof form.project?.parentType === 'string'
    ) {
      form.serviceCategory = form.project?.parentType
    }
  }
)

const handleOnOk = async (): Promise<boolean> => {
  const useUserError = await useUserFormRef?.value?.formInstance?.validate();
  // 内容的数据
  const contentError = await contentFormRef?.value?.formInstance?.validate();
  const modalError = await modalFormRef.value?.validate();

  const { treatmentItemCode, project, reservationChannel, userInfo } =
    (await modalFormRef.value?.model) as TForm;

  return new Promise(resolve => {
    // 使用人的数据
    if (useUserError || contentError || modalError) {
      resolve(false);
      return;
    }
    console.log("获取数据", modalFormRef);
    const useUserData = useUserFormRef?.value?.formInstance
      .model as TUseUserForm;
    const contentData = contentFormRef?.value?.formInstance
      .model as TContentForm;

    console.log("useUserData, contentData", useUserData, contentData);
    postBdManageApiHealthInsuranceApplyOrder({
      patientId: userInfo?.patientId,
      serviceParentItem: form.serviceCategory,
      // applyBirthday: '',
      // applyClientNo: '',
      // applyIdNo: '',
      // applyIdType: '',
      applyMark: contentData?.applyMark,
      // applyName: '',
      applyOrderUserInfoComponentDTO: {
        /** 客户号(健康险MCS客户号) */
        clientNo: "",
        // 用户详细地址
        userAddress: contentData?.userAddress,
        /** 用户家庭住址省市区编码,逗号分割 */
        userAreaCode: [
          contentData?.userArea?.province?.code,
          contentData?.userArea?.city?.code,
          contentData?.userArea?.county?.code,
        ]
          .filter(_fi => _fi)
          .join(","),
        /** 用户家庭住址省市区 */
        userAreaName: `${contentData?.userArea?.province?.codeName}${contentData?.userArea?.city?.codeName}${contentData?.userArea?.county?.codeName}`,
        /** 用户出生日期 */
        userBirthday: useUserData?.userBirthday,
        /** 用户证件号码 */
        userIdNo: useUserData?.userIdNo,
        /** 用户证件类型 */
        userIdType: useUserData?.userIdType,
        /** 用户手机号 */
        userMobile: useUserData?.userMobile,
        /** 用户姓名 */
        userName: useUserData?.userName,
        /** 用户性别（M-男、F-女） */
        userSex: useUserData?.userSex,
      },
      applyOrderFileComponentDTO: {
        applyMaterialDTO:
          Array.isArray(contentData.applyOrderFileComponentDTO) &&
          contentData.applyOrderFileComponentDTO?.length > 0
            ? [
                {
                  fileType: contentData?.fileType ?? "",
                  imgIdList:
                    contentData.applyOrderFileComponentDTO.map(
                      _item => _item?.key,
                    ) ?? [],
                  iobsDownLoadUrl:
                    contentData.applyOrderFileComponentDTO.map(
                      _item => _item?.threeFilePath,
                    ) ?? [],
                  fileIds:
                    contentData.applyOrderFileComponentDTO.map(
                      _item => _item?.id,
                    ) ?? [],
                },
              ]
            : [],
      },
      applyOrderAppointInstitutionDTO: {
        appointDate: contentData?.appointDate
          ? dayjs(contentData?.appointDate).format("YYYY-MM-DD HH:mm:ss")
          : undefined,
        appointDateEnd: contentData?.appointDateEnd
          ? dayjs(contentData?.appointDateEnd).format("YYYY-MM-DD HH:mm:ss")
          : undefined,
        appointDepartment: contentData?.appointDepartment,
        appointDestination: contentData?.appointHospital?.address,
        // appointDoctor: '',
        appointHospital: contentData?.appointHospital?.hospitalName,
        cityCode: contentData?.city?.code,
        cityName: contentData?.city?.codeName,
        countryCode: "156",
        countryName: "中国",
        hospitalCode: contentData?.appointHospital?.hospitalId,
        provinceCode: contentData?.province?.code,
        provinceName: contentData?.province?.codeName,
      },
      diseaseInfoDTO: contentData?.diseaseInfoDTO,
      // applyPhone: '',
      // applySex: '',
      benefitNo: project?.benefitNo ?? "",
      certNo: project?.certNo ?? "",
      // channel: '',
      contractNo: project?.contractNo ?? "",
      orderNo: project?.orderNo ?? "",
      policyNo: project?.policyNo ?? "",
      reservationChannel,
      // serviceItemList: contentData?.serviceItemList?.map(_item => {
      //   return {
      //     certNo: _item.certNo,
      //     contractNo: _item.contractNo,
      //     policyNo: _item.policyNo,
      //     serviceItemName: _item.serviceItemName,
      //     serviceItemCode: _item.serviceItemCode
      //   };
      // }),
      serviceItemList: [
        {
          certNo: project?.certNo ?? "",
          contractNo: project?.contractNo ?? "",
          policyNo: project?.policyNo ?? "",
          serviceItemName: project?.serviceItemName ?? "",
          serviceItemCode: project?.serviceItemCode ?? "",
        },
      ],
      serviceCode: project?.serviceCode ?? "",
      serviceItemName: project?.serviceItemName ?? "",
      serviceItemCode: project?.serviceItemCode ?? "",
      serviceOrderType: project?.serviceOrderType ?? "",
      treatmentItemCode,
      visitDate: contentData?.appointDate,
      visitHospital: contentData?.appointHospital?.hospitalName ?? "",
      relationName: contentData?.relationName,
      relationPhone: contentData?.relationPhone,
      diseaseDescription: contentData?.diseaseDescription,

      // 这部分数据是非必填的 需要在提交的时候 不要提交给后端服务
      // applyClientNo: '',
      // applyIdNo: '',
      // applyIdType: '',
      // applyName: '',
      // applyPhone: '',
      // applySex: '',
      // channel: ''
    } as any).then(
      res => {
        console.log("_form", form, res);
        resolve(true);
        quickAccessStore.toggleServiceReservation(false);
      },
      err => {
        resolve(false);
        console.error("err", err);
      },
    );
  });
};

// const handleOk = async () => {

// }

const handleCancel = () => {
  // 重置所有的表单数据
  modalFormRef?.value?.resetFields();
  quickAccessStore.toggleServiceReservation(false, "");
};

const handleDictData = async (_dictType: string) => {
  const [err, res] = await to(
    getBdManageApiSystemDictDataTypeByDictType({
      dictType: _dictType,
    }),
  );
  if (err) {
    throw err;
  }
  if (res.data) {
    return res.data;
  }
  return [];
};

// 获取一个对应患者的权益信息
const handleRightsInfo = async () => {
  if (
    typeof form.userInfo?.patientId !== "string" ||
    form.userInfo?.patientId === ""
  ) {
    return;
  }
  console.log("_userInfo", form.userInfo);
  loadingContent.rightsItem = true;
  const [err, res] = await to(
    postBdManageApiHealthInsuranceQueryRights({
      patientId: form.userInfo?.patientId as unknown as number,
    }),
  );
  loadingContent.rightsItem = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    const { policyRights, serviceRights } = res.data;
    /**
     * 保单权益
     * contractNo， orderNo 没有
     */
    const newPolicyRights = policyRights?.map((_item: any) => {
      return {
        uuid: _item.benefitNo,
        benefitNo: _item.benefitNo,
        certNo: _item.certNo,
        // 没有匹配这个字段
        contractNo: "",
        policyNo: _item.polNo,
        // reservationChannel: '',
        serviceCode: _item.serviceCode,
        serviceItemName: _item.serviceItemName,
        serviceItemCode: _item.serviceItemCode,
        serviceOrderType: _item.serviceOrderType,
        // 没有这个字段 _item.orderNo
        orderNo: "",
        parentType: _item.parentType,
      };
    });
    /**
     * 服务权益
     * certNo， policyNo 没有
     */
    const newServiceRights = serviceRights.map((_item: any) => {
      return {
        uuid: `${_item.orderNo}${_item.serviceCode}${_item.serviceItemCode}`,
        benefitNo: _item.benefitNo,
        // 没有匹配这个字段
        certNo: "",
        contractNo: _item.contractNo,
        // 没有匹配这个字段
        policyNo: "",
        // reservationChannel: '',
        serviceCode: _item.serviceCode,
        serviceItemName: _item.serviceItemName,
        serviceItemCode: _item.serviceItemCode,
        serviceOrderType: _item.serviceOrderType,
        orderNo: _item.orderNo,
        parentType: _item.parentType,
      };
    });
    // rightsList.value = [...newPolicyRights, ...newServiceRights];
    rightsItemList.value = [...newPolicyRights, ...newServiceRights];
  }
  console.log("rightInfo", res);
};

onUpdated(async () => {
  if (visible.value) {
    loadingContent.loadingReservationChannel = true;
    reservationChannel.value = await handleDictData("reservation-channel");
    loadingContent.loadingReservationChannel = false;
    loadingContent.loadingTreatmentItem = true;
    treatmentItem.value = await handleDictData("treatment-item");
    loadingContent.loadingTreatmentItem = false;
    loadingContent.loadingProjectList = true;
    projectList.value = await handleDictData("service_parent_item");
    loadingContent.loadingProjectList = false;
    if (isView.value) {
      loadingContent.loadingData = true;
      postBdManageApiHealthInsuranceQueryById({
        id: quickAccessStore.quickAccessState.serviceReservationId,
      }).then(
        _res => {
          console.log("detail_res", _res);
          const resData = _res.data;
          if (resData) {
            console.log("res_data", resData);
            const {
              threeDetails: {
                // 申请医院的信息
                applyHopeAppointDTO,
                // 申请订单的扩展信息
                applyOrderInfoComponentDTO,
                // 申请图片的信息
                applyOrderFileComponentDTO,
                // 疾病的详细信息
                detailDiseaseInfoDTO,
                // 联系人的信息
                detailRelationDTO,
                // 详细的申请信息
                detailApplyMarkDTO,
                // 申请订单的用户信息 -- 使用人的信息
                applyOrderUserInfoComponentDTO,
                // 扩展的用户信息
                applyOrderExtraInfoComponentDTO,
                // 文件信息
                materialInfoDTO,
              },
              ...baseInfo
            } = resData;
            // 使用人的信息
            form.userInfo = {
              patientId: baseInfo.patientId,
              name: applyOrderUserInfoComponentDTO.userName,
              gender:
                applyOrderUserInfoComponentDTO.userSex === "M" ? "1" : "2",
              age: applyOrderUserInfoComponentDTO.age,
              contactMobile: applyOrderUserInfoComponentDTO.userMobile,
              idCard: applyOrderUserInfoComponentDTO.userIdNo,
              idType: applyOrderUserInfoComponentDTO.userIdType,
              birthdate: applyOrderUserInfoComponentDTO.userBirthday,
            };
            form.reservationChannel = baseInfo.reservationChannel ?? "";
            form.serviceCategory = baseInfo.serviceParentItem ?? "";
            form.project = {
              uuid:
                applyOrderInfoComponentDTO.serviceOrderType === "P"
                  ? applyOrderInfoComponentDTO.benefitNo
                  : `${applyOrderInfoComponentDTO.orderNo}${applyOrderInfoComponentDTO.serviceCode}${applyOrderInfoComponentDTO.serviceItemCode}`,
              benefitNo: applyOrderInfoComponentDTO.benefitNo,
              certNo: applyOrderInfoComponentDTO.certNo,
              contractNo: applyOrderInfoComponentDTO.contractNo,
              orderNo: applyOrderInfoComponentDTO.orderNo,
              policyNo: applyOrderInfoComponentDTO.policyNo,
              serviceCode: applyOrderInfoComponentDTO.serviceCode,
              serviceItemName: applyOrderInfoComponentDTO.serviceItemName,
              serviceItemCode: applyOrderInfoComponentDTO.serviceItemCode,
              serviceOrderType: applyOrderInfoComponentDTO.serviceOrderType,
              parentType: applyOrderExtraInfoComponentDTO.serviceParentType,
            };
            console.log("form.project", form.project);
            form.treatmentItemCode = baseInfo.treatmentItemCode ?? "";
            form.createAt = baseInfo.createTime ?? "";
            form.cancelTime = baseInfo.cancelDate ?? "";
            // 内容数据的回显
            const contentInfo = {
              relationName: detailRelationDTO.relationName,
              relationPhone: detailRelationDTO.relationPhone,
              applyMark: detailApplyMarkDTO.applyMark,
              diseaseDescription: baseInfo.diseaseDescription ?? "",
              userAddress: applyOrderUserInfoComponentDTO.userAddress,
              userArea: {
                province: {
                  code: applyOrderUserInfoComponentDTO?.userAreaCode?.split(
                    ",",
                  )?.[0],
                },
                city: {
                  code: applyOrderUserInfoComponentDTO?.userAreaCode?.split(
                    ",",
                  )?.[1],
                },
                county: {
                  code: applyOrderUserInfoComponentDTO?.userAreaCode?.split(
                    ",",
                  )?.[2],
                },
              },
              fileType: materialInfoDTO?.applyMaterialDTO?.[0]?.fileType,
              applyOrderFileComponentDTO:
                materialInfoDTO?.applyMaterialDTO?.[0]?.iobsDownLoadUrl?.map(
                  (_item: string) => {
                    return {
                      threeFilePath: _item,
                      uri: _item,
                    };
                  },
                ),
              province: {
                code: applyHopeAppointDTO.provinceCode,
                codeName: applyHopeAppointDTO.provinceName,
              },
              city: {
                code: applyHopeAppointDTO.cityCode,
                codeName: applyHopeAppointDTO.cityName,
              },
              county: {
                code: applyHopeAppointDTO.countyCode,
                codeName: applyHopeAppointDTO.countyName,
              },
              appointDate: applyHopeAppointDTO.appointDate,
              appointDateEnd: applyHopeAppointDTO.appointDateEnd,
              appointDepartment: applyHopeAppointDTO.appointDepartment,
              appointHospital: {
                hospitalName: applyHopeAppointDTO.appointHospital,
                address: applyHopeAppointDTO.appointDestination,
                hospitalId: applyHopeAppointDTO.hospitalCode,
              },
              diseaseInfoDTO: {
                diseaseName: detailDiseaseInfoDTO.diseaseName,
                diseaseInfo: detailDiseaseInfoDTO.diseaseInfo,
              },
              serviceItemList: [],
            };
            console.log("contentInfo", contentInfo);
            setTimeout(() => {
              loadingContent.loadingData = false;
              contentFormRef?.value?.setFormData(contentInfo);
            }, 2000);
            // TODO: 回显的时候 进行参数赋值
            // form.desc = _res.data.diseaseDescription ?? '';
            // form.hospital = _res.data.visitHospital ?? '';
            // form.date = _res.data.visitDate ?? '';
            // form.type = _res.data.treatmentItemCode ?? '';
            // form.project = _res.data.serviceItemCode ?? '';
            // form.channel = _res.data.reservationChannel ?? '';
            // form.cancelTime = _res.data.cancelTime ?? '';
          }
        },
        err => {
          loadingContent.loadingData = false;
        },
      );
    }
  }
});
</script>

<script lang="ts">
// 使用 parentType 来对应类型
export const enum EProjectType {
  // 门诊协助=门诊绿通，类型：HC、类型名称：号床
  MZXZ = "H",
  // 陪诊，类型：PB、类型名称：陪伴
  PZ = "P",
  // 陪护=住院陪护，类型：PB、类型名称：陪伴
  PH = "P",
  // 住院协助=住院绿通，类型：HC、类型名称：号床
  ZYXZ = "C",
  // 专家二诊，类型：ZX、类型名称：咨询
  ZJEZ = "EZ",
  // 康复护理，类型：KF、类型名称：康复
  KFHL = "HU",
}

export type TRightsItem = {
  // 每一个不同的权益项的唯一id
  uuid: string;
  // 权益号(MCS权益唯一标识)，MCS可提供
  benefitNo: string;
  // 分单号，对接时,MCS可提供，保单类产品P
  certNo: string;
  contractNo: string;
  policyNo: string;
  // reservationChannel: string
  serviceCode: string;
  // serviceItem: string
  serviceItemName: string;
  orderNo: string;
  serviceItemCode: string;
  serviceOrderType: string;
  parentType: EProjectType;
};

type TRightList = Array<TRightsItem>;

export type TForm = {
  userInfo: any;
  serviceCategory: string;
  reservationChannel: string;
  treatmentItemCode: string;
  createAt: string;
  cancelTime: string;
  project: TRightsItem | undefined;
};

type TContentForm = TZyltForm & TEzForm & TKfhlForm;

// export const projectList = [
//   {
//     label: '门诊协助',
//     value: EProjectType.MZXZ
//   },
//   {
//     label: '陪诊/陪护',
//     value: EProjectType.PZ
//   },
//   // {
//   //   label: '陪护',
//   //   value: EProjectType.PH
//   // },
//   {
//     label: '住院协助',
//     value: EProjectType.ZYXZ
//   },
//   {
//     label: '二诊',
//     value: EProjectType.ZJEZ
//   },
//   {
//     label: '康复护理',
//     value: EProjectType.KFHL
//   },
// ]
</script>

<style lang="scss" scoped>
// 禁用样式之下的输入框处理
:deep(.arco-input-disabled) {
  color: #000 !important;
  background-color: white !important;
  padding-left: 0 !important;
  input:disabled {
    -webkit-text-fill-color: #000 !important;
  }
}

// 禁用状态下的只读样式处理
:deep(.arco-select-view-disabled) {
  color: #000 !important;
  background-color: white !important;
  padding-left: 0 !important;
  input:disabled {
    -webkit-text-fill-color: #000 !important;
  }
  .arco-select-view-suffix {
    display: none;
  }
}

:deep(.arco-picker-disabled) {
  color: #000 !important;
  background-color: white !important;
  padding-left: 0 !important;
  input {
    &:disabled {
      padding-left: 0 !important;
    }
  }
  .arco-picker-suffix {
    display: none;
  }
}

:deep(.arco-picker-disabled) {
  input:disabled {
    -webkit-text-fill-color: #000 !important;
  }
}

:deep(.arco-textarea-disabled) {
  color: #000 !important;
  background-color: white !important;
  padding-left: 0 !important;
  textarea {
    -webkit-text-fill-color: #000 !important;
    padding-left: 0;
    padding-right: 0;
  }
  div.arco-textarea-word-limit {
    display: none;
  }
}

.serviceAppointmentForm {
  width: 100%;
  > .serviceAppointmentFormItems {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
