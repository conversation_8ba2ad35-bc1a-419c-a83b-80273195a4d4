<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-10-21 16:40:33
 * @LastEditors: yangrongxin
 * @LastEditTime: 2024-10-24 16:06:58
-->
<template>
  <!-- TODO 360的页面应该放在更高的层级 -->
  <a-modal
    v-model:visible="visible"
    :footer="false"
    :hide-title="true"
    fullscreen
    :body-style="{
      padding: '0',
      backgroundColor: '#f3f3f3',
      overflowY: 'hidden',
    }"
  >
    <patient-center v-if="visible" @close="handleClose" />
  </a-modal>
</template>

<script setup lang="ts">
import patientCenter from "@/views/patient-center/index.vue";
import { usePatientCenterStore } from "@/store";
import { ref, watch } from "vue";
import useAppStore from "@/store/modules/app";

const appStore = useAppStore();
const patientCenterStore = usePatientCenterStore();
const visible = ref(false);

watch(
  () => appStore.isShowPatientCenter,
  _value => {
    visible.value = _value;
  },
);

const handleClose = () => {
  appStore.hidePatientCenter();
  patientCenterStore.resetSecondaryMenu();
};
</script>

<style lang="scss" scoped></style>
