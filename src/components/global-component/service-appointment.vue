<!--
 * @Description: 服务预约的全局组件
 * @Author: ya<PERSON><PERSON><PERSON>
 * @Date: 2024-12-17 17:38:07
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-05-19 17:07:32
-->
<template>
  <!-- 服务预约的组件 -->
  <a-drawer
    unmount-on-close
    :width="680"
    :visible="visible"
    :mask-closable="true"
    :on-before-ok="handleOnOk"
    body-class="appointmentView"
    title="服务预约"
    @cancel="handleCancel"
  >
    <a-spin
      :style="{
        width: '100%',
      }"
    >
      <a-form
        ref="modalFormRef"
        :model="form"
        :label-col-props="{ span: 4 }"
        :wrapper-col-props="{ span: 20 }"
        label-align="left"
        scroll-to-first-error
        class="serviceAppointmentForm"
      >
        <!-- 选择主权益人 -->
        <dominantForm v-model:form="form" />
        <!-- 服务信息 -->
        <serviceInformationForm
          v-model:form="form"
          :dictionary-state="dictionaryState"
          @clear-dictionary-state="clearDictionaryState"
        />
        <!-- 既往就诊信息 -->
        <pastVisitForm v-if="showStatus.pastVisitForm" v-model:form="form" />
        <!-- 基本健康信息 -->
        <baseHealthInfoForm
          v-if="showStatus.baseHealthInfoForm"
          v-model:form="form"
        />
        <!-- 首诊信息 -->
        <template v-if="form.userInfo">
          <firstVisitForm
            v-for="(post, index) in form.firstVisitList"
            :key="post.patientId || index"
            v-model:form="form"
            :index="index"
          />
        </template>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<script setup lang="ts">
import useQuickAccessStore from "@/store/modules/quick-access";
import { computed, onUpdated, reactive, ref, watch } from "vue";
import { FormInstance, Message } from "@arco-design/web-vue";
import to from "await-to-js";
import dayjs from "dayjs";
import * as apis from "@/batchApi";
import { map, omitBy, isUndefined } from "lodash";
import { firstVisitComparisonCodeMap } from "@/constant";
import dominantForm from "./formContent/dominantForm.vue";
import pastVisitForm from "./formContent/pastVisitForm.vue";
import baseHealthInfoForm from "./formContent/baseHealthInfoForm.vue";
import firstVisitForm from "./formContent/firstVisitForm.vue";
import serviceInformationForm from "./formContent/serviceInformationForm.vue";

const modalFormRef = ref<FormInstance>();
const visible = ref(false);

const dictionaryState = reactive<IDictionaryState>({
  // 预约渠道
  loadingReservationChannel: false,
  reservationChannel: [],
  // 服务项目
  loadingServiceItems: false,
  serviceItems: [],
  serviceItems1: [],
  // 就诊机构
  organLoading: false,
  organList: [],
  // 服务包
  servicePackageLoading: false,
  servicePackage: [],
  serviceIdList: [],
  signDoctorLoading: false,
  signDoctorList: [],
});

const form = reactive<TForm>({
  userInfo: undefined,
  channelId: undefined,
  payType: undefined,
  serviceIdRecord: undefined,
  originatorsInfo: [], // 权益人
  firstVisitList: [],
  firstVisitType: undefined,
  firstVisitOrgId: "怡健殿方圆中心店",
  reservationTime: undefined,
  isReservedParking: undefined,
  isInHospital: undefined, // 近半年是否本院有就诊
  pastDiagnosis: undefined,
  hasCurrentHistory: undefined, // 现病史
  currentHistory: undefined,
  hasPastDisease: undefined, // 过往病史
  pastDisease: undefined,
  hasAllergyHistory: undefined, // 过敏史
  allergyHistory: undefined,
  packageCodeItem: undefined,
  rightItemCode: undefined,
  doctorItem: undefined,
});

const quickAccessStore = useQuickAccessStore();

const showStatus = computed(() => {
  const defaultStatus = {
    pastVisitForm: false,
    baseHealthInfoForm: false,
  };
  if (form.payType === PayTypeEnum.QYHX) {
    return defaultStatus;
  }
  if (form.payType === PayTypeEnum.HZZF) {
    return {
      pastVisitForm: true,
      baseHealthInfoForm: true,
    };
  }
  return defaultStatus;
});

watch(
  () => [form.userInfo, form.originatorsInfo],
  ([userInfo, originatorsInfo]) => {
    if (userInfo) {
      form.firstVisitList = [userInfo, ...originatorsInfo].map(item => ({
        ...item,
        description: undefined,
        reports: [],
        consentFiles: [],
      }));
    } else {
      form.firstVisitList = [];
    }
    modalFormRef.value?.resetFields([
      "firstVisitList",
      "packageCodeItem",
      "serviceIdRecord",
    ]);
  },
  { immediate: true, deep: true },
);

watch(
  () => form.serviceIdRecord,
  () => {
    const exclude: string[] = [
      "channelId",
      "payType",
      "serviceIdRecord",
      "userInfo",
      "packageCodeItem",
    ];
    const fieldNames = Object.keys(form)?.filter(key => !exclude.includes(key));
    modalFormRef.value?.resetFields(fieldNames);
  },
);

watch(
  () => quickAccessStore.quickAccessState.showServiceAppointment,
  () => {
    visible.value = quickAccessStore.quickAccessState.showServiceAppointment;
  },
);

/* 处理上传文件 */
const clearFileList = (list: Array<TForm["originatorsInfo"]>, type: string) => {
  if (!list?.length) return [];
  return (
    list?.map((item: any) => ({
      threeFilePath: item?.uri,
      fileType: item?.ext,
      type,
    })) ?? []
  );
};

/* 根据人数选择服务ID */
const filterService = (list: any[]) => {
  const { firstVisitList } = form;
  if (!firstVisitList?.length || !list?.length) return;
  const count = firstVisitList?.length;
  const result = list?.find(
    item =>
      item?.serviceCode === firstVisitComparisonCodeMap.get(count)?.serviceCode,
  );
  return result;
};

const handleOnOk = async (): Promise<boolean> => {
  const modalError = await modalFormRef.value?.validate();
  return new Promise(resolve => {
    if (modalError) {
      resolve(false);
      return;
    }
    const {
      userInfo,
      channelId,
      payType,
      serviceIdRecord,
      firstVisitType,
      firstVisitOrgId,
      isReservedParking,
      firstVisitList,
      reservationTime,
      packageCodeItem,
      doctorItem,
    } = form;
    const {
      serviceId,
      serviceName,
      serviceCode,
      child = [],
    } = serviceIdRecord || {};
    // 如果选择首诊预约，根据人数选择不同的ID
    const mainPatientId = userInfo?.mainPatientId ?? undefined;
    // const doctorId = userInfo?.doctorId ?? undefined;
    // const doctorUserId = userInfo?.doctorUserId ?? undefined;

    const detailList = firstVisitList?.map(item => {
      const reports = clearFileList(item?.reports as any, "1");
      const consentFiles = clearFileList(item?.consentFiles as any, "2");
      return {
        allergyHistory: form.allergyHistory,
        currentHistory: form.currentHistory,
        description: item.description,
        hasAllergyHistory: form.hasAllergyHistory,
        hasCurrentHistory: form.hasCurrentHistory,
        hasPastDisease: form.hasPastDisease,
        isInHospital: form.isInHospital,
        pastDiagnosis: form.pastDiagnosis,
        pastDisease: form.pastDisease,
        patientId: item?.patientId,
        reservationTime: reservationTime?.date
          ? dayjs(reservationTime?.date).format("YYYY-MM-DD HH:mm:ss")
          : undefined,
        fileList: [...reports, ...consentFiles],
      };
    });
    const result = map(detailList, obj => omitBy(obj, isUndefined));
    const reservedParkingTime = reservationTime?.date
      ? dayjs(reservationTime?.date).format("YYYY-MM-DD HH:mm:ss")
      : undefined;
    const appointTime = reservationTime?.date
      ? dayjs(reservationTime?.date).format("YYYY-MM-DD")
      : undefined;
    // 核销权益rightItemCode，需要把服务包的serviceId给传上,患者支付 ，serviceId，
    const serviceIdObj =
      payType === PayTypeEnum.QYHX
        ? { rightItemCode: serviceCode, serviceId: packageCodeItem?.serviceId }
        : { serviceId };
    const params = {
      channelId,
      firstVisitOrgId,
      firstVisitType,
      isReservedParking,
      reservedParkingTime: isReservedParking
        ? `${appointTime} ${reservationTime?.startTime}-${reservationTime?.endTime}`
        : undefined,
      scheduleId: reservationTime?.scheduleId,
      payType,
      serviceName,
      detailList: result,
      patientId: mainPatientId,
      doctorId: doctorItem?.doctorId,
      doctorUserId: doctorItem?.doctorUserId,
      appointTime: `${appointTime} ${reservationTime?.startTime}-${reservationTime?.endTime}`,
      firstTime: reservedParkingTime,
      packageCode: packageCodeItem?.serviceCode,
      intervalId: reservationTime?.intervalId, // 额外参数，防止后端理解出错
      price: reservationTime?.price ?? undefined,
      ...serviceIdObj,
    } as any as apis.postBdManageApiServiceReservationAddServiceReservationRequest;

    apis
      .postBdManageApiServiceReservationAddServiceReservation(params)
      .then(res => {
        Message.success("成功");
        resolve(true);
        handleCancel();
        quickAccessStore.toggleServiceAppointment(false);
      })
      .catch(err => {
        resolve(false);
      });
  });
};

const clearDictionaryState = () => {
  dictionaryState.serviceItems1 = [];
  dictionaryState.organList = [];
  dictionaryState.servicePackage = [];
  dictionaryState.signDoctorList = [];
};

const handleCancel = () => {
  // 重置所有的表单数据
  modalFormRef?.value?.resetFields();
  clearDictionaryState();
  dictionaryState.reservationChannel = [];
  dictionaryState.serviceItems = [];
  dictionaryState.serviceIdList = [];
  quickAccessStore.toggleServiceAppointment(false, "");
};

// const handleDictData = async (_dictType: string) => {
//   const [err, res] = await to(
//     getBdManageApiSystemDictDataTypeByDictType({
//       dictType: _dictType,
//     }),
//   );
//   if (err) {
//     throw err;
//   }
//   if (res.data) {
//     return res.data;
//   }
//   return [];
// };

/** 获取号源serviceId */
const getServiceIdList = async () => {
  dictionaryState.loadingServiceItems = true;
  const [err, res] = await to(
    apis.postBdManageApiServiceReservationQueryServiceItems(),
  );
  dictionaryState.loadingServiceItems = false;
  if (err) {
    throw err;
  }
  if (res?.data) {
    const result = res?.data?.find(item => item?.serviceCode === "9999")?.child;
    dictionaryState.serviceIdList =
      result as apis.QueryServiceItemsUsingPOSTServiceItemsDto[];
    dictionaryState.serviceItems = res?.data?.filter(
      item => item?.serviceCode !== "9999",
    );
  }
};

/* 获取服务项目 */
const getServiceItems = async () => {
  dictionaryState.loadingServiceItems = true;
  const [err, res] = await to(
    apis.getBdManageApiServiceReservationGetServiceItem({
      patientId: form.userInfo?.patientId,
      serviceId: form.packageCodeItem?.serviceId as string,
    }),
  );
  dictionaryState.loadingServiceItems = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    dictionaryState.serviceItems1 = res?.data;
  }
};

/* 获取首诊机构 */
// const getOrganList = async () => {
//   dictionaryState.organLoading = true;
//   const [err, res] = await to(postBdManageApiOrganQueryOrganList());
//   dictionaryState.organLoading = false;
//   if (err) {
//     throw err;
//   }
//   if (res.code === "1") {
//     dictionaryState.organList = res?.data;
//   }
// };

/* 获取渠道 */
// const getChannel = async () => {
//   dictionaryState.loadingReservationChannel = true;
//   const [err, res] = await to(apis.postBdManageApiChannelList());
//   dictionaryState.loadingReservationChannel = false;
//   if (err) {
//     throw err;
//   }
//   if (res?.data) {
//     dictionaryState.reservationChannel = res?.data;
//   }
// };

/* 获取患者服务包 */
const getServicePackage = async () => {
  dictionaryState.servicePackageLoading = true;
  const [err, res] = await to(
    apis.getBdManageApiServiceReservationGetServicePackage({
      patientId: form.userInfo?.patientId,
    }),
  );
  dictionaryState.servicePackageLoading = false;
  if (err) {
    throw err;
  }
  if (res?.data) {
    dictionaryState.servicePackage = res?.data;
  }
};

watch(
  () => [form.userInfo, form.payType],
  ([newUserInfo, newPaytype]) => {
    if (newUserInfo?.patientId && newPaytype === PayTypeEnum.QYHX) {
      getServicePackage();
    }
  },
);

watch(
  () => [form.userInfo, form.payType, form.packageCodeItem],
  ([newUserInfo, newPaytype, newPackageCodeItem]) => {
    if (
      newUserInfo?.patientId &&
      newPackageCodeItem?.serviceId &&
      newPaytype === PayTypeEnum.QYHX
    ) {
      form.serviceIdRecord = undefined;
      getServiceItems();
      getSignDoctorList();
    }
    if (newPaytype === PayTypeEnum.HZZF) {
      dictionaryState.signDoctorList = [];
    }
  },
);
// 签约医生
const getSignDoctorList = async () => {
  dictionaryState.signDoctorLoading = true;
  const [err, res] = await to(
    apis.postBdManageApiServiceReservationGetTeamDoctor({
      patientId: form.userInfo?.patientId,
      serviceCode: form.packageCodeItem?.serviceCode as string,
    }),
  );
  dictionaryState.signDoctorLoading = false;
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    dictionaryState.signDoctorList =
      res?.data as apis.GetTeamDoctorUsingPOSTDoctorInfoDto[];
  }
};

onUpdated(async () => {
  if (visible.value) {
    // await getOrganList();
    // await getChannel();
    await getServiceIdList();
  }
});
</script>

<script lang="ts">
export const enum ServiceIdCategory {
  // 首诊预约
  SZYY = "PAH_SV_0003",
  // 互联网医院服务项目
  HLWYY = "040110",
}

export const enum PayTypeEnum {
  QYHX = "1",
  HZZF = "2",
}

export const enum FirstVisitEnum {
  XSSZ = "0",
  XXSZ = "1",
}
// 字典数据
export type IDictionaryState = {
  loadingReservationChannel: boolean;
  reservationChannel: apis.ListUsingPOSTChannel[];
  loadingServiceItems: boolean;
  serviceItems: apis.QueryServiceItemsUsingPOSTServiceItemsDto[];
  serviceItems1: apis.QueryServiceItemsUsingPOSTServiceItemsDto[];
  organLoading: boolean;
  organList: apis.postBdManageApiOrganQueryOrganListResponse["data"];
  servicePackageLoading: boolean;
  servicePackage: apis.GetServicePackageUsingGETDocBusinessDto[];
  serviceIdList: apis.QueryServiceItemsUsingPOSTServiceItemsDto[]; // 号源ID
  signDoctorLoading: boolean;
  signDoctorList: apis.GetTeamDoctorUsingPOSTDoctorInfoDto[];
};

export type TForm = {
  userInfo: any;
  channelId: string | undefined;
  payType: string | undefined;
  serviceIdRecord: apis.QueryServiceItemsUsingPOSTServiceItemsDto | undefined;
  originatorsInfo: Array<{
    personName?: string;
    description?: string;
    reports?: any[];
    consentFiles?: any[];
    fileType?: string;
    [key: string]: any;
  }>;
  firstVisitList: Array<{
    personName?: string;
    description?: string;
    reports?: any[];
    consentFiles?: any[];
    fileType?: string;
    [key: string]: any;
  }>;
  firstVisitType: string | undefined;
  firstVisitOrgId: string | undefined;
  reservationTime: any;
  isReservedParking: boolean | undefined;
  isInHospital: boolean | undefined; // 近半年是否本院有就诊
  pastDiagnosis: string | undefined;
  hasCurrentHistory: boolean | undefined; // 现病史
  currentHistory: string | undefined;
  hasPastDisease: boolean | undefined; // 过往病史
  pastDisease: string | undefined;
  hasAllergyHistory: boolean | undefined; // 过敏史
  allergyHistory: string | undefined;
  packageCodeItem: apis.GetServicePackageUsingGETDocBusinessDto | undefined;
  rightItemCode: string | undefined;
  doctorItem: apis.GetTeamDoctorUsingPOSTDoctorInfoDto | undefined;
};
</script>

<style lang="scss">
// 禁用样式之下的输入框处理
:deep(.arco-input-disabled) {
  color: #000 !important;
  background-color: white !important;
  padding-left: 0 !important;
  input:disabled {
    -webkit-text-fill-color: #000 !important;
  }
}

// 禁用状态下的只读样式处理
:deep(.arco-select-view-disabled) {
  color: #000 !important;
  background-color: white !important;
  padding-left: 0 !important;
  input:disabled {
    -webkit-text-fill-color: #000 !important;
  }
  .arco-select-view-suffix {
    display: none;
  }
}

:deep(.arco-picker-disabled) {
  color: #000 !important;
  background-color: white !important;
  padding-left: 0 !important;
  input {
    &:disabled {
      padding-left: 0 !important;
    }
  }
  .arco-picker-suffix {
    display: none;
  }
}

:deep(.arco-picker-disabled) {
  input:disabled {
    -webkit-text-fill-color: #000 !important;
  }
}

:deep(.arco-textarea-disabled) {
  color: #000 !important;
  background-color: white !important;
  padding-left: 0 !important;
  textarea {
    -webkit-text-fill-color: #000 !important;
    padding-left: 0;
    padding-right: 0;
  }
  div.arco-textarea-word-limit {
    display: none;
  }
}

.serviceAppointmentForm {
  width: 100%;
  > .serviceAppointmentFormItems {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }
}

.titleView {
  font-size: 16px;
  font-weight: bold;
  color: #000000;
  margin-bottom: 8px;
}

.emptyUser {
  height: 103px;
  border-radius: 8px;
  line-height: 103px;
  text-align: center;
  background: #f9fbff;
  color: rgba(0, 82, 217, 0.5);
  font-size: 12px;
  margin-bottom: 20px;
}
</style>
