<!--
* @description 表格筛选
* @fileName index.vue
* <AUTHOR>
* @date 2025/07/07 16:28:49
!-->
<script setup lang="ts">
type IEmits = {
  (event: "handleFilterConfirm"): void;
  (event: "handleFilterReset"): void;
  (event: "setFilterValue", value: Array<string>): void;
};

const value = defineModel<string>("value", { required: true });

const emits = defineEmits<IEmits>();
</script>

<template>
  <div class="custom-filter">
    <div class="content">
      <a-input
        placeholder="请输入"
        :model-value="value"
        @press-enter="emits('handleFilterConfirm')"
        @input="value => emits('setFilterValue', [value])"
      />
    </div>

    <a-space class="custom-filter-footer">
      <a-button size="mini" @click="emits('handleFilterReset')">重置</a-button>
      <a-button size="mini" type="primary" @click="emits('handleFilterConfirm')"
        >确定</a-button
      >
    </a-space>
  </div>
</template>

<style lang="scss" scoped>
.custom-filter {
  background: var(--color-bg-5);
  border: 1px solid var(--color-neutral-3);
  border-radius: var(--border-radius-medium);
  box-shadow: 0 2px 5px rgb(0 0 0 / 10%);

  .content {
    padding: 12px;
  }

  .custom-filter-footer {
    padding: 8px 12px;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #f2f3f5;
  }
}
</style>
