<!--
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2024-11-01 11:05:03
 * @LastEditors: yangrong<PERSON>
 * @LastEditTime: 2024-11-19 16:53:44
-->
<template>
  <a-range-picker
    :style="{ width: props.width ?? '254px' }"
    :disabled-date="filterDisabledDate"
    @change="handleDateChange"
    @select="handleDateSelect"
  />
</template>

<script setup lang="ts">
import { CalendarValue } from '@arco-design/web-vue/es/date-picker/interface';
import dayjs from 'dayjs'
import { ref } from 'vue';

// 获取当前的开始的时间
const startDate = ref('');

interface IBdDateRangeProp {
  width?: string
}

const props = withDefaults(
  defineProps<IBdDateRangeProp>(),
  {
    width: '254px'
  }
)

const emits = defineEmits([
  'update:modelValue'
])

// 限制时间结束时间 只能选择开始时间一年以内的时间
const filterDisabledDate = (
  _current: Date,
  type: "start" | "end"): boolean =>
{
  // console.log('_disabled_date', _current, type, dayjs(_current))
  // 当前是选择结束的时间
  if (
    type === 'end'
  ) {
    // 获取当前时间数据的dayjs数据
    const inlineStartDate = dayjs(startDate.value);
    // 在当前的时间数据上增加一年的时间
    const oneYearLater = inlineStartDate.add(1, 'year');
    return dayjs(_current).isBefore(inlineStartDate) || dayjs(_current).isAfter(oneYearLater);
  }
  // 开始的时间不禁止选择
  if (type === "start") {
    return false;
  }
  return true; // 默认禁用
}

const handleDateSelect = (
  value: (CalendarValue | undefined)[],
  date: (Date | undefined)[],
  dateString: (string | undefined)[]
): void => {
  if (
    Array.isArray(value) &&
    value.length === 1 &&
    typeof value[0] === 'string'
  ) {
    startDate.value = value[0] as string;
    console.log('date_select', date, dateString)
  }
}

const handleDateChange: any = (_value: string[]) => {
  emits('update:modelValue', _value);
}

</script>

<style scoped>

</style>