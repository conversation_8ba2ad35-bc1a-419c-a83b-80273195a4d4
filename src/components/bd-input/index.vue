<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-10-21 11:02:29
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-06-26 14:52:36
-->
<template>
  <a-input-search
    id="mainButton"
    v-model="inlineValue"
    :style="{ width: props.width ?? '253px', borderRadius: '4px' }"
    :placeholder="props.placeholder"
    search-button
    v-bind="$attrs"
    @press-enter="handlePressEnter"
    @search="handleSearch"
  />
</template>

<script setup lang="ts">
import { ref } from "vue";

interface IBdInputProps {
  value?: string | number;
  width?: string;
  field?: string;
  placeholder?: string;
}

const props = withDefaults(defineProps<IBdInputProps>(), {
  value: "",
  width: "253px",
  field: "",
  placeholder: "搜索患者姓名/证件号/电话",
});

const inlineValue = ref();
const emits = defineEmits(["search"]);

const handlePressEnter = (e: KeyboardEvent) => {
  console.log("pressEnter", (e.target as HTMLInputElement).value);
  emits("search", {
    [props.field]: (e.target as HTMLInputElement).value,
  });
};

const handleSearch = (value: string) => {
  emits("search", {
    [props.field]: value,
  });
};

const handleClear = () => {
  inlineValue.value = "";
};

defineExpose({
  handleClear,
});
</script>

<style lang="scss" scoped>
:deep(.arco-input-wrapper) {
  padding-left: 10px !important;
  padding-right: 10px !important;
}
</style>
