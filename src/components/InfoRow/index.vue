<!--
* @description 信息行展示
* @fileName index.vue
* <AUTHOR>
* @date 2025/07/25 14:23:10
!-->
<script setup lang="ts">
import { computed, h, VNode } from "vue";
import type { RenderInfo } from "@/types/global";

interface Props<T = any> {
  data: T;
  items: RenderInfo<T>[];
  gutter?: [number, number];
  labelSpan?: number;
  valueSpan?: number;
  labelClass?: string;
  labelStyle?: Record<string, any>;
  valueClass?: string;
  valueStyle?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  gutter: () => [0, 10],
  labelSpan: 4,
  valueSpan: 20,
  labelClass: "label",
});

const renderCell = (content: string | VNode | undefined) => {
  if (typeof content === "string") {
    return h("span", content);
  }
  return content;
};

// 过滤可见项
const visibleItems = computed(() => {
  return props.items?.filter(item => {
    return !item?.show || item?.show(props.data);
  });
});
</script>

<template>
  <a-row :gutter="gutter">
    <template v-for="item in visibleItems" :key="item.attrName">
      <a-col
        :span="item.span?.[0] || labelSpan"
        :class="labelClass"
        :style="labelStyle"
      >
        {{ item.label }}
      </a-col>
      <a-col
        :span="item.span?.[1] || valueSpan"
        :class="valueClass"
        :style="valueClass"
      >
        <template v-if="item?.render">
          <component :is="renderCell(item.render(data))" />
        </template>
        <template v-else>
          {{ data?.[item.attrName] ?? "-" }}
        </template>
      </a-col>
    </template>
  </a-row>
</template>

<style lang="less" scoped></style>
