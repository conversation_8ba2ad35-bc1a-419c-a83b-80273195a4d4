<!--
 * @Description: 表格标签的展示
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-11-11 11:17:44
 * @LastEditors: yangrongxin
 * @LastEditTime: 2024-11-21 10:25:30
-->
<template>
  <div>
    <!-- 默认只展示前两个元素 -->
    <span
      v-for="_pkg, _index in props.items.slice(0, props.hiddenNum || 2)"
      :key="_index"
      class="code-item"
    >
      {{ _pkg }}
    </span>
    <a-trigger 
      position="bottom"
      auto-fit-position
      :unmount-on-close="false"
      :popup-translate="[20, 20]"
    >
      <span
        v-if="props.items.length >= (props.hiddenNum + 1 || 3)"
        class="code-item"
      >
        ...
      </span>
      <template #content>
        <!-- 超出的部分展示一个三个点 -->
        <div class="code-content">
          <span
            v-for="_pkg, _index in props.items"
            :key="_index"
            class="code-item"
          >
            {{ _pkg }}
          </span>
        </div>
      </template>
    </a-trigger>
  </div>
</template>

<script setup lang="ts">

interface ITableTagsProps {
  items?: any
  hiddenNum?: number
}

const props = withDefaults(
  defineProps<ITableTagsProps>(),
  {
    items: [],
    hiddenNum: 0
  }
)

</script>

<style lang="scss" scoped>
// 新的根据权益编码展示的样式
.code-item {
  font-size: 12px;
  color: var(--primary-color);
  padding: 3px 4px;
  border: 1px solid var(--primary-color);
  border-radius: 2px;
  background-color: rgba(0, 82, 217, 0.1);
  margin-right: 4px;
  &:last-child {
    margin-right: 0px;
  }
}

// 展示内部内容的颜色
.code-content {
  padding: 8px;
  max-width: 208px;
  border-radius: 4px;
  background-color: white;
  box-shadow: 2px 2px 5px #ddd;
}
</style>