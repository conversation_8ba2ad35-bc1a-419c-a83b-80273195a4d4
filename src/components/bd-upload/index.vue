<!--
 * @Description: 用于图片和pdf进行上传的组件
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-06-19 10:28:11
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-12 01:03:21
-->
<template>
  <!-- 是用于进行图片自定义上传的组件 -->
  <a-upload
    v-model:file-list="fileList"
    class="upload-wrapper"
    list-type="picture-card"
    :accept="acceptContent"
    :action="uploadPath"
    :headers="headers"
    :limit="limit"
    image-preview
    @before-upload="beforeUpload"
    @error="handleError"
  >
    <template #upload-item="{ fileItem }">
      <div class="upload-item">
        <upload-picture-item
          v-if="fileType === EFileType.Img"
          :file="fileItem"
        />
        <upload-file-item
          v-if="fileType === EFileType.File"
          :file="fileItem"
          @preview="handlePreview(fileItem)"
        />
        <a-tooltip :content="fileItem.name">
          <span class="file-name">{{ fileItem.name }}</span>
        </a-tooltip>
      </div>
    </template>
    <template #upload-button>
      <div class="arco-upload-picture-card">
        <div class="arco-upload-picture-card-text">
          <IconPlus />
          <div style="margin-top: 10px; font-weight: 600">上传</div>
        </div>
      </div>
    </template>
  </a-upload>
  <!-- 单独pdf预览的组件 -->
  <a-modal 
    v-model:visible="pdfVisible" 
    title="pdf预览" 
    width="800px" 
    :mask-closable="false" 
    :footer="false" 
    unmount-on-close
    @cancel="handlePdfCancel"
  >
    <div style="height: 80vh; overflow: auto;">
      <vue-pdf-embed ref="pdfRef" :source="pdfSrc" />
    </div>
  </a-modal>
</template>


<script setup lang="ts">
import { getToken } from '@/utils/auth';
import {
  ref,
  computed,
  watch
} from 'vue';
import { FileItem, Message } from '@arco-design/web-vue';
import UploadPictureItem from '@arco-design/web-vue/es/upload/upload-picture-item'
import VuePdfEmbed from 'vue-pdf-embed';
import uploadFileItem from './upload-file-item.vue';

const fileList = ref([])
const headers = {
  accessToken: getToken() ?? ''
}
const uploadPath = `${import.meta.env.VITE_API_BASE_URL}/base/upload/uploadImgMin`;
const pdfVisible = ref(false);
const pdfSrc = ref();
const limitSize = 10;

// 定义当前组件支持的参数
const {
  maxSize,
  limit = 0,
  fileType = EFileType.Img
} = defineProps<{
  maxSize?: number
  limit?: number
  fileType: EFileType
}>();

// const emits = defineEmits(['update:modelValue']);

const handleError = (
  fileItem: FileItem
) => {
  console.log('fileItem', fileItem)
  Message.error('上传文件失败');
}

const handlePdfCancel = () => {
  pdfVisible.value = false;
}

// 预览指定的pdf文件
const handlePreview = (file: FileItem) => {
  pdfSrc.value = file.response?.data?.uri;
  pdfVisible.value = true;
}

// 根据不同的上传类型 返回不同的文件信息
const acceptContent = computed(() => {
  if (
    fileType === EFileType.File
  ) {
    return "application/pdf"
  }
  return "image/jpeg,image/png,image/jpg"
})

const beforeUpload = (
  file: File
) => {
  const isLt20M = file.size / 1024 / 1024 < (maxSize ?? limitSize);
  if (
    !isLt20M
  ) {
    Message.error(`文件大小不能超过${limitSize}M`);
    return false
  }
  return true
}

defineExpose({
  fileList
})

</script>

<script lang="ts">
export const enum EFileType {
  File = 'file',
  Img = 'img'
}
</script>


<style lang="scss" scoped>
.upload-wrapper {
  margin-bottom: var(--spacing-7);
}
.upload-item {
  display: inline-block;
  width: 80px;
  // height: 100px;
  overflow: hidden;
  margin-right: var(--spacing-7);
  .file-name {
    display: block;
    width: 80px;
    overflow: hidden;
    white-space: nowrap; 
    text-overflow: ellipsis; 
  }
}
</style>