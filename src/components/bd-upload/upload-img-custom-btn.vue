<!--
 * @Description: 自定义上传图片按钮的文件上传
 * @Author: yangrong<PERSON>
 * @Date: 2025-06-24 16:35:25
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-11 10:16:09
-->
<template>
  <a-upload
    v-model:file-list="fileList"
    class="upload-wrapper"
    list-type="picture"
    :accept="acceptContent"
    :action="uploadPath"
    :headers="headers"
    :multiple="false"
    :limit="1"
    @before-upload="beforeUpload"
    @error="handleError"
  >
    <template #upload-item="{ fileItem }">
      <slot name="upload-item" :file-item="fileItem" :on-delete-img="handleDeleteImg"></slot>
    </template>
    <template #upload-button>
      <slot name="upload-btn" />
    </template>
  </a-upload>
</template>

<script setup lang="ts">
import { getToken } from '@/utils/auth';
import { FileItem, Message } from '@arco-design/web-vue';
import { ref, watch } from 'vue';

const emit = defineEmits(['update:modelValue'])

const props = defineProps<{
  modelValue: any[]
}>()
const limitSize = 10;

// 限制的上传文件的类型
const acceptContent = "image/jpeg,image/png,image/jpg";
const uploadPath = `${import.meta.env.VITE_API_BASE_URL}/base/upload/uploadImgMin`;

// 当前已经上传的文件列表
const fileList = ref<any[]>([])
const headers = {
  accessToken: getToken() ?? ''
}

watch(() => fileList.value, () => {
  emit('update:modelValue', fileList.value)
});

watch(() => props.modelValue, (val) => {
  fileList.value = val ?? [];
});


const handleDeleteImg = () => {
  fileList.value = [];
  console.log('invoke_delete_img')
}

const beforeUpload = (
  file: File
) => {
  const isLt20M = file.size / 1024 / 1024 < limitSize;
  if (
    !isLt20M
  ) {
    Message.error(`文件大小不能超过${limitSize}M`);
    return false
  }
  return true
}

const handleError = (
  fileItem: FileItem
) => {
  console.log('fileItem', fileItem)
  Message.error('上传文件失败');
}

</script>

<style scoped lang="scss">
.upload-wrapper {
  :deep(.arco-upload) {
    width: 100%;
  }
}
</style>