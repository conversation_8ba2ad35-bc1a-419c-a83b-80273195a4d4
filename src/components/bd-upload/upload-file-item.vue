<!--
 * @Description: 用于展示和预览pdf内容的组件
 * @Author: yangrongxin
 * @Date: 2025-05-14 14:46:37
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-05-14 17:41:12
-->
<template>
  <div :class="cls">
    <!-- 展示pdf的图标 -->
    <upload-progress
      v-if="file.status === 'uploading'"
      :list-type="'picture-card'"
      :file="file"
    />
    <img
      v-else
      :src="pdfPng"
      alt=""
    />
    <!-- 展示操作按钮的内容 -->
    <div :class="`${itemCls}-mask`">
      <div
        v-if="file.status === 'error' && uploadCtx?.showCancelButton"
        :class="`${itemCls}-error-tip`"
      >
        <span
          :class="[uploadCtx?.iconCls, `${uploadCtx?.iconCls}-error`]"
        >
          <IconImageClose />
        </span>
      </div>
      <div :class="`${itemCls}-operation`">
        <!-- 预览的按钮 -->
        <span
          v-if="file.status !== 'error' && uploadCtx?.showPreviewButton"
          :class="[uploadCtx?.iconCls, `${uploadCtx?.iconCls}-preview`]"
          @click="handlePreview"
        >
          <IconEye />
        </span>
        <span
          v-if="['init', 'error'].includes(file.status as string) && uploadCtx?.showRetryButton"
          :class="[uploadCtx?.iconCls, `${uploadCtx?.iconCls}-upload`]"
          @click="() => uploadCtx?.onUpload(file)"
        >
          <IconUpload />
        </span>
        <span
          v-if="!uploadCtx?.disabled && uploadCtx?.showRemoveButton"
          :class="[uploadCtx?.iconCls, `${uploadCtx?.iconCls}-remove`]"
          @click="() => uploadCtx?.onRemove(file)"
        >
          <IconDelete />
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  inject,
  computed
} from 'vue';
import pdfPng from '@/assets/images/<EMAIL>';
import { FileItem } from '@arco-design/web-vue';
import UploadProgress from '@arco-design/web-vue/es/upload/upload-progress'
import { getPrefixCls } from '@arco-design/web-vue/es/_utils/global-config';
import {
  uploadInjectionKey
} from '@arco-design/web-vue/es/upload/context'

// 获取当前上传组件的上下文
const uploadCtx = inject(uploadInjectionKey, undefined);

const {
  file
} = defineProps<{
  file: FileItem
}>();

const emits = defineEmits<{
  preview: []
}>();

const handlePreview = () => {
  emits("preview");
}

const prefixCls = getPrefixCls('upload-list');
const itemCls = `${prefixCls}-picture`;
const cls = computed(() => [
  itemCls,
  {
    [`${itemCls}-status-error`]: file.status === 'error',
  },
]);

</script>

<style scoped lang="scss">
.file-item-container {

}
</style>