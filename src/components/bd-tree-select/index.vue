<!--
 * @Description: 树形数据选择组件
 * @Author: yangrong<PERSON>
 * @Date: 2024-10-25 09:29:25
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-05-19 17:05:19
-->
<template>
  <a-tree-select
    class="select-container"
    :data="treeData"
    :placeholder="props.placeholder"
    :style="{ width: props.width ?? '144px' }"
    :field-names="{
      key: props.valueField ?? 'value',
      title: props.labelField ?? 'label',
      children: props.childrenField ?? 'children'
    }"
    :tree-checkable="true"
    :tree-checked-strategy="'child'"
    :max-tag-count="1"
    @change="handleChange"
  >
  </a-tree-select>
</template>

<script setup lang="ts">
import { ChangeHandler } from '@arco-design/web-vue/es/tree-select/interface';
import to from 'await-to-js';
import { onMounted, ref } from 'vue';

interface IBdTreeSelect {
  width?: string
  placeholder?: string
  requestFn?: any
  requestParams?: any
  dataField?: string
  labelField?: string
  valueField?: string
  childrenField?: string
}

const props = withDefaults(
  defineProps<IBdTreeSelect>(),
  {
    width: '144px',
    labelField: 'label',
    valueField: 'value',
    placeholder: '',
    requestFn: undefined,
    requestParams: undefined,
    dataField: undefined,
    childrenField: 'children'
  }
)

const emits = defineEmits([
  'update:modelValue'
])

const treeData = ref<any[]>([])

const handleChange: ChangeHandler = (_value) => {
  emits('update:modelValue', _value);
  console.log('_value', _value)
}

const init = async () => {
  if (
    typeof props.requestFn === 'function'
  ) {
    console.log('props.requestFn', props.requestFn)
    const [err, res] = await to<any>(props.requestFn(props.requestParams));
    if (
      err
    ) {
      throw err;
    }
    const resArray = res.data[props.dataField ?? 'content']
    if (
      Array.isArray(resArray)
    ) {
      treeData.value = resArray;
    }
    console.log('res', res, resArray);
  }
}

onMounted(async () => {
  await init()
})

</script>

<style lang="scss" scoped>
</style>