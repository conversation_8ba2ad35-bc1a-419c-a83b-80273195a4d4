import { createPinia } from 'pinia';
import { PiniaLoading } from "./plugins/loadingStatus";
import useAppStore from './modules/app';
import useUserStore from './modules/user';
import useTabBarStore from './modules/tab-bar';
import usePatientCenterStore from './modules/patient-center';
import useTargetPopulation from './modules/target-population';
import useDictData from './modules/dict';
import useQuickAccessStore from './modules/quick-access'
import useWorkOrder from './modules/work-order'


const pinia = createPinia();
pinia.use(PiniaLoading)

export { useAppStore, useUserStore, useTabBarStore, usePatientCenterStore, useTargetPopulation, useDictData,useQuickAccessStore,useWorkOrder };
export default pinia;
