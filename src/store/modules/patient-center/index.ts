/*
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-10-22 13:59:04
 * @LastEditors: yangrongxin
 * @LastEditTime: 2024-10-24 16:08:12
 */
import { update } from "lodash";
import { defineStore } from "pinia";

export interface IMenuItem {
  name: string
  component: any
  componentParams?: any
}

const usePatientCenterStore = defineStore('patientCenter', {
  state: (): {
    showSecondaryMenu: boolean,
    secondaryMenuName: string
    secondaryMenus: IMenuItem[],
    secondaryDefaultKey?: number
  } => {
      return {
        showSecondaryMenu: false,
        secondaryMenuName: '',
        secondaryMenus: [],
        secondaryDefaultKey: 0
      }
  },
  getters: {
    isShowSecondaryMenu(state) {
      return state.showSecondaryMenu
    },
    defaultMenuKey(state) {
      return state.secondaryDefaultKey
    },
    secMenuName(state) {
      return state.secondaryMenuName
    }
  },
  actions: {
    resetSecondaryMenu() {
      this.showSecondaryMenu = false;
      this.secondaryMenuName = '';
      this.secondaryMenus = [];
      this.secondaryDefaultKey = 0;
    },
    // 展示当前二级菜单的方法
    changeSecondaryMenu(
      show: boolean,
      name: string,
      compArray: IMenuItem[] = [],
      defaultKey = 0
    ) {
      this.showSecondaryMenu = show;
      this.secondaryMenuName = name;
      if (
        Array.isArray(compArray)
      ) {
        this.secondaryMenus = compArray
      }
      this.secondaryDefaultKey = defaultKey;
    },
    // 更新二级菜单的name
    updateSecondaryMenuName(
      namelist: string[] = [],
    ) {
      const secondaryMenusList = this.secondaryMenus
      secondaryMenusList.map((item:IMenuItem, i: number ) => {
        item.name = namelist[i]
        return item
      })
      this.secondaryMenus = secondaryMenusList
    }
  }
})

export default usePatientCenterStore;