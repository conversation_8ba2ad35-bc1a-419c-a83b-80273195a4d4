/*
 * @Description: 存储目标人群的筛选条件 联动的数据根据这里的数据进行处理
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-10-24 17:21:15
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 14:42:15
 */
import { getCategoryList, ICategoryItem } from "@/api/patients";
import { postBdManageApiPatientQueryPatientGroup } from "@/batchApi";
import { ECrowdType, EIsOrNotOne } from "@/types/enums";
import to from "await-to-js";
import { defineStore } from "pinia";

const useTargetPopulation = defineStore('targetPopulation', {
  state: (): {
    customerSource: ICategoryItem[]
    serviceGroup: ICategoryItem['subs']
    customerSources: string[]
    serviceGroupIds: string[]
    // 当前是不是需要重新加载服务人群的数据
    reloadServiceGroup: boolean
  } => {
    return {
      // 所有渠道的编码
      customerSource: [],
      // 渠道下面所有的包的编码
      serviceGroup: [],
      // 选中的客户来源数据
      customerSources: [],
      // 选中的服务包数据
      serviceGroupIds: [],
      reloadServiceGroup: false
    }
  },
  getters: {
    getState(state) {
      return state
    },
    // 定义单独展示的客户来源的标签 -- 在目标人群筛选条件内定义为 code + labelName
    customerLabelSource: (state) => {
      return state.customerSource.map(_item => {
        return {
          ..._item,
          // name: `${_item.code}${_item.name}`,
          name: _item.name
        }
      })
    },
    // 根据选择的客户来源过滤展示对应的服务包的数据
    filteredServiceGroup: (state) => {
      if (state.customerSources.length === 0) {
        return state.serviceGroup;
      }
      return state.serviceGroup.filter(group => 
        state.customerSources.includes(group.pid)
      );
    }
  },
  actions: {
    // 重新加载服务人群的数据
    changeReloadServiceGroup(_reload: boolean) {
      this.reloadServiceGroup = _reload;
    },
    // 全局的服务包id变化 进行修改
    changeServiceGroupIds(_serviceGroupIds: string[]) {
      this.serviceGroupIds = _serviceGroupIds;
    },
    // 全局的权益包的id变化 进行修改
    changeCustomerSources(_customerSources: string[]) {
      this.customerSources = _customerSources;
    },
    setServiceGroupIds(_serviceGroupIds: string[], _customerSources: string[] = []) {
      // 设置当前选中的渠道的数据
      this.customerSources = _serviceGroupIds.map(_item => {
        const ele = this.serviceGroup.find(_fi => _fi.categoryId === _item)
        return ele?.pid
      }).filter(_fi => _fi) as string[];
      if (
        Array.isArray(_customerSources) && _customerSources.length > 0
      ) {
        this.customerSources = this.customerSources.concat(_customerSources)
      }
      // 设置当前选中的包的数据
      this.serviceGroupIds = _serviceGroupIds
    },
    async queryCategoryList(_crowdType: ECrowdType) {
      let treeData: ICategoryItem[] = [];
      const showNew = true;
      if (
        !showNew
      ) {
        const [err, res] = await to(getCategoryList({
          crowdType: _crowdType,
          // isSignedCrowd: EIsOrNotOne.Is,
        }))
        if (
          err
        ) {
          throw err;
        }
        if (
          Array.isArray(res?.data.content)
        ) {
          // 第一层的元素 如果是全部的话 单独取出来 做一层元素
          res.data.content.forEach(_item => {
            if (
              _item.name === '全部'
            ) {
              const { subs, ...nodeProps }  = _item
              treeData.unshift({
                ...nodeProps,
                subs: []
              })
              treeData = treeData.concat(subs as ICategoryItem[])
              // 所有渠道的编码
              this.customerSource = subs as ICategoryItem[];
              // 渠道下面所有的包的编码
              this.serviceGroup = subs.map(_item => _item.subs).reduce((b, a) => (b || []).concat(a)) as ICategoryItem[]
              console.log('this.customerSource, this.serviceGroup', this.customerSource, this.serviceGroup)
            }
          })
        }
      } else {
        const [err, res] = await to(postBdManageApiPatientQueryPatientGroup({}))
        if (
          err
        ) {
          throw err
        }
        if (
          Array.isArray(res.data)
        ) {
          // 根据当前的渠道的层级进行处理
          res.data.forEach(_item => {
            if (
              _item.name === '全部'
            ) {
              const { subs, ...nodeProps }  = _item
              treeData.unshift({
                ...nodeProps,
                subs: []
              } as unknown as ICategoryItem)
              treeData = treeData.concat(subs as unknown as ICategoryItem[])
              // 所有渠道的编码
              this.customerSource = subs as unknown as ICategoryItem[];
              // @ts-expect-error 渠道下面所有的包的编码
              this.serviceGroup = subs?.map(_item => _item.subs).reduce((b, a) => (b || []).concat(a)) as unknown as ICategoryItem[] ?? []
              console.log('this.customerSource, this.serviceGroup', this.customerSource, this.serviceGroup)
            }
          })
        }
        console.log('_res', res)
      }
      return treeData;
    }
  }
})

export default useTargetPopulation;