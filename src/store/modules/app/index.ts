/*
 * @Description: 全局app使用的store
 * @Author: yangrong<PERSON>
 * @Date: 2024-09-20 15:57:10
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 20:06:43
 */
import { defineStore } from 'pinia';
import { Notification } from '@arco-design/web-vue';
import type { NotificationReturn } from '@arco-design/web-vue/es/notification/interface';
import type { RouteRecordNormalized } from 'vue-router';
import defaultSettings from '@/config/settings.json';
import { getMenuList } from '@/api/user';
import { AppState } from './types';

const useAppStore = defineStore('app', {
  state: (): AppState => ({ ...defaultSettings }),

  getters: {
    appCurrentSetting(state: AppState): AppState {
      return { ...state };
    },
    appDevice(state: AppState) {
      return state.device;
    },
    appAsyncMenus(state: AppState): RouteRecordNormalized[] {
      return state.serverMenu as unknown as RouteRecordNormalized[];
    },
    // 控制当前是不是展示 用户中心
    isShowPatientCenter(state: AppState) {
      return state.showPatientCenter
    },
    // 所有的患者360组件 可以通过这里获取对应的参数
    patientInfo(state: AppState) {
      return state.patientCenterInfo
    }
  },

  actions: {
    // Update app settings
    updateSettings(partial: Partial<AppState>) {
      // @ts-expect-error 已知错误
      this.$patch(partial);
    },

    // Change theme color
    toggleTheme(dark: boolean) {
      if (dark) {
        this.theme = 'dark';
        document.body.setAttribute('arco-theme', 'dark');
      } else {
        this.theme = 'light';
        document.body.removeAttribute('arco-theme');
      }
    },
    toggleDevice(device: string) {
      this.device = device;
    },
    toggleMenu(value: boolean) {
      this.hideMenu = value;
    },
    // 打开患者中心的方法
    openPatientCenter(patientInfo: AppState['patientCenterInfo']) {
      this.patientCenterInfo = patientInfo
      this.showPatientCenter = true
    },
    // 关闭患者中心的方法
    hidePatientCenter() {
      this.patientCenterInfo = {
        name: '',
        patientId: '',
        contactMobile: ''
      }
      this.showPatientCenter = false
    },
    async fetchServerMenuConfig() {
      let notifyInstance: NotificationReturn | null = null;
      try {
        notifyInstance = Notification.info({
          id: 'menuNotice', // Keep the instance id the same
          content: 'loading',
          closable: true,
        });
        const { data } = await getMenuList();
        this.serverMenu = data;
        notifyInstance = Notification.success({
          id: 'menuNotice',
          content: 'success',
          closable: true,
        });
      } catch (error) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        notifyInstance = Notification.error({
          id: 'menuNotice',
          content: 'error',
          closable: true,
        });
      }
    },
    clearServerMenu() {
      this.serverMenu = [];
    },
  },
});

export default useAppStore;
