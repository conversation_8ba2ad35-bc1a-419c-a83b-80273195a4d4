/*
 * @Description: 用于管理与用户相关的逻辑信息
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-09-20 15:57:10
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 15:30:14
 */
import { defineStore } from 'pinia';
import {
  login as userLogin,
  logout as userLogout,
  loginByP<PERSON> as userLoginByPhone,
  getAuthCode as userGetAuthCode,
  getUserInfo,
  LoginData,
  ILoginByPhone,
  IGetAuthCode,
} from '@/api/user';
import {
  setToken,
  clearToken,
  setUserInfo,
  getToken,
  getUserInfo as getUserInfoLocal,
  // setMenus,
} from '@/utils/auth';
import { removeRouteListener } from '@/utils/route-listener';
import to from 'await-to-js';
import { postBdManageApiUserLoginByPhone, postBdManageApiUserLoginByTeamIdRoleIdUserId } from '@/batchApi';
import { UserState } from './types';
import useAppStore from '../app';

const useUserStore = defineStore('user', {
  state: (): UserState => ({
    accountInfo: getUserInfoLocal().accountInfo ?? {
      name: undefined,
      avatar: undefined,
      organId: undefined,
      organType: undefined,
      teamId: undefined,
      teamRoleName: undefined,
      phone: undefined,
      userId: undefined,
      organName: undefined,
      sex: undefined
    },
    token: getToken() ?? undefined,
    roleInfo: getUserInfoLocal().roleInfo ?? {
      roleId: '',
      roleName: '',
      // roleCode: '',
      roleType: '',
    },
    authCodeInfo: getUserInfoLocal().authCodeInfo ?? {
      needSlider: false,
      authKey: '',
      exprieTime: 0,
      canNext: false,
    },
  }),

  // 定义在 getter 中的数据可以快速的被 store对象访问
  getters: {
    userInfo(state: UserState): UserState {
      return { ...state };
    },
  },

  actions: {
    switchRoles() {
      return new Promise((resolve) => {
        this.roleInfo.roleName =
          this.roleInfo.roleName === 'user' ? 'admin' : 'user';
        resolve(this.roleInfo.roleName);
      });
    },
    // Set user's information
    setInfo(partial: Partial<UserState>) {
      this.$patch(partial);
    },

    // Reset user's information
    resetInfo() {
      this.$reset();
    },

    // Get user's information
    async info() {
      const res = await getUserInfo();
      this.setInfo(res.data);
    },

    // Login -- 使用账号密码进行登陆
    async login(loginForm: LoginData) {
      try {
        const res = await userLogin(loginForm);
        setToken(res.data.token);
      } catch (err) {
        clearToken();
        throw err;
      }
    },
    // 新版本 使用手机号 和 验证码 进行登陆
    async loginByRoleAndTeam(
      userId: string,
      roleId: string,
      teamId: string
    ) {
      const [err, res] = await to(postBdManageApiUserLoginByTeamIdRoleIdUserId({
        // @ts-ignore
        userId,
        // @ts-ignore
        roleId,
        // @ts-ignore
        teamId
      }))
      if (
        err
      ) {
        throw err;
      }
      // @ts-expect-error 缺少参数需要后端补充
      this.roleInfo = res.data?.userRoleInfo;
      this.accountInfo = {
        ...res.data?.info,
        // @ts-expect-error 账户信息一致 不知道为啥报错
        teamId: res.data?.userTeamInfoVO?.teamId,
        teamName: res.data?.userTeamInfoVO?.name
      };
      if (
        typeof res.data?.token === 'string' &&
        res.data?.token !== ""
      ) {
        setToken(res.data.token)
      }
      setUserInfo({
        accountInfo: this.accountInfo,
        token: this.token,
        roleInfo: this.roleInfo,
        authCodeInfo: this.authCodeInfo
      });
      // 获取对应的内容
    },
    // LoginByPhone -- 使用手机号获取验证码进行登陆
    async loginByPhone(loginPhoneForm: ILoginByPhone) {
      const [err, res] = await to(userLoginByPhone(loginPhoneForm));
      if (err) {
        throw err;
      }
      // 将接口返回的数据 存入当前的store中
      this.roleInfo = res.data.roleInfo;
      this.accountInfo = res.data.info;
      setToken(res.data.token);
      setUserInfo({
        accountInfo: this.accountInfo,
        token: this.token,
        roleInfo: this.roleInfo,
        authCodeInfo: this.authCodeInfo
      });
      
      // debugger;
      if (
        Array.isArray(res.data.mainMenuItems) &&
        res.data.mainMenuItems.length !== 0
      ) {
        const newMenu = res.data.mainMenuItems.map(_menu => {
          console.log('menu', _menu);
          // debugger;
          return {
            ..._menu,
          };
        });
        console.log('newMenu', newMenu);
      }
      // setMenus(res.data.mainMenuItems);
      // 计算当前的路由信息
      // const appStore = useAppStore();
      // appStore.updateSettings({
      //   serverMenu: [],
      // });
      console.log('data', res.data);
    },
    // 根据手机号码获取短信验证码
    async getAuthCode(authData: IGetAuthCode) {
      const [err, res] = await to(userGetAuthCode(authData));
      if (err) {
        throw err;
      }
      this.authCodeInfo = res.data;
      console.log('data', res);
    },
    logoutCallBack() {
      const appStore = useAppStore();
      this.resetInfo();
      clearToken();
      removeRouteListener();
      appStore.clearServerMenu();
    },
    // Logout
    async logout() {
      this.logoutCallBack();

      // try {
      //   await userLogout();
      // } finally {
      // }
    },
  },
});

export default useUserStore;
