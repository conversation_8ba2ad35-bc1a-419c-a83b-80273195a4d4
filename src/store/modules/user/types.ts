/*
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2024-09-20 15:57:10
 * @LastEditors: yangrongxin
 * @LastEditTime: 2024-10-21 15:13:13
 */
import type {
  IGetAuthCodeRes,
  ILoginByPhoneRes,
  TAccountInfo,
  TRoleInfo,
} from '@/api/user';

export type RoleType = '' | '*' | 'admin' | 'user';
export interface UserState extends Partial<Pick<ILoginByPhoneRes, 'token'>> {
  accountInfo: Partial<TAccountInfo>;
  /** 手机号获取验证码的 验证码信息 */
  authCodeInfo: IGetAuthCodeRes;
  /** 角色信息 用于存储当前登陆用户的角色 */
  roleInfo: TRoleInfo;
}
