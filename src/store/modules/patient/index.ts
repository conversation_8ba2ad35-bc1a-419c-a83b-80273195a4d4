import store from "@/store";
import { defineStore } from "pinia";
import { 
	patientDiagnoseAdd, 
	patientDiagnoseList, 
	patientDiagnoseData,
	patientL<PERSON>l<PERSON>ist,
	patientLabelGroupList,
	patientUserDefinedLabelAdd,
	patientLabelAdd,
	patientUserDefinedLabelDelete,
	patientDiagnoseMarkAsRead,
} from '@/api/patient';

// 定义状态管理
const usePatientStore = defineStore({
	id: "patient",
	state: () => ({
		updateReport: 1,
	}),
	getters: {
	},
	actions: {
		setUpdateReport(){
			this.updateReport = Date.now()
		},
		/** 诊疗数据添加 */
		async patientDiagnoseAdd(params: any) {
			try {
				const res: any = await patientDiagnoseAdd(params);
				const { code = "", data } = res || {};
				if (code === '1') {
					return Promise.resolve(data);
				}
				return Promise.resolve('');
			} catch (e) {
				return Promise.reject(e);
			}
		},
		/** 诊疗数据列表 */
		async patientDiagnoseList(params: any) {
			try {
				const res: any = await patientDiagnoseList(params);
				const { code = "", data } = res || {};
				if (code === '1') {
					return Promise.resolve(data);
				}
				return Promise.resolve('');
			} catch (e) {
				return Promise.reject(e);
			}
		},
		/** 检验检查聚合统计 */
		async patientDiagnoseData(params: any) {
			try {
				const res: any = await patientDiagnoseData(params);
				const { code = "", data } = res || {};
				if (code === '1') {
					return Promise.resolve(data);
				}
				return Promise.resolve('');
			} catch (e) {
				return Promise.reject(e);
			}
		},
		/** 查询患者标签 */
		async patientLabelList(params: any) {
			try {
				const res: any = await patientLabelList(params);
				const { code = "", data } = res || {};
				if (code === '1') {
					return Promise.resolve(data);
				}
				return Promise.resolve('');
			} catch (e) {
				return Promise.reject(e);
			}
		},
		/** 标签分组列表 */
		async patientLabelGroupList(params: any) {
			try {
				const res: any = await patientLabelGroupList(params);
				const { code = "", data } = res || {};
				if (code === '1') {
					return Promise.resolve(data);
				}
				return Promise.resolve('');
			} catch (e) {
				return Promise.reject(e);
			}
		},

		/** 添加自定义标签 */
		async patientUserDefinedLabelAdd(params: any) {
			try {
				const res: any = await patientUserDefinedLabelAdd(params);
				const { code = "", data } = res || {};
				if (code === '1') {
					return Promise.resolve(data);
				}
				return Promise.resolve('');
			} catch (e) {
				return Promise.reject(e);
			}
		},

		/** 患者添加标签 */
		async patientLabelAdd(params: any) {
			try {
				const res: any = await patientLabelAdd(params);
				const { code = "", data } = res || {};
				if (code === '1') {
					return Promise.resolve(data);
				}
				return Promise.resolve('');
			} catch (e) {
				return Promise.reject(e);
			}
		},

		/** 用户自定义标签 - 删除 */
		async patientUserDefinedLabelDelete(params: any) {
			try {
				const res: any = await patientUserDefinedLabelDelete(params);
				const { code = "", data } = res || {};
				if (code === '1') {
					return Promise.resolve(data);
				}
				return Promise.resolve('');
			} catch (e) {
				return Promise.reject(e);
			}
		},

		/** 诊疗结果标记已读 */
		async patientDiagnoseMarkAsRead(params: any) {
			try {
				const res: any = await patientDiagnoseMarkAsRead(params);
				const { code = "", data } = res || {};
				if (code === '1') {
					return Promise.resolve(data);
				}
				return Promise.resolve('');
			} catch (e) {
				return Promise.reject(e);
			}
		},
	},
	// persist: {
	//   enabled: true, // 是否开启此模块的数据持久化
	//   strategies: [
	//     {
	//       storage: sessionStorage, // 数据默认存在 sessionStorage 里
	//       paths: ['dataList'], // 默认所有 state 都会进行缓存，你能够通过 paths 指定要长久化的字段，其余的则不会进行持久化
	//     },
	//   ],
	// },
});

export default usePatientStore;

// Need to be used outside the setup
export function usePatientStoreWidthOut() {
	return usePatientStore(store);
}
