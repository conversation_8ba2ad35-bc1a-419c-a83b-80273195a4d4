import { defineStore } from "pinia";
import * as apis from "@/batchApi";
import { to } from "await-to-js";
import { PaginationProps } from "@arco-design/web-vue";

type Record = apis.MyTicketsUsingPOST_1TicketQueryResVo;

interface IWorkOrderState {
  list: Record[];
  pagination: PaginationProps;
}

const useWorkOrder = defineStore("workOrder", {
  state: (): IWorkOrderState => ({
    list: [],
    pagination: {
      size: "small",
      showTotal: true,
      showJumper: true,
      showPageSize: true,
      total: 0,
      current: 1,
      pageSize: 10,
    },
  }),
  actions: {
    // 工单列表
    async getTicketMyList(params?: apis.postBdManageApiTicketMyRequest) {
      const [err, res] = await to(
        apis.postBdManageApiTicketMy({
          pageNum: this.pagination.current,
          pageSize: this.pagination.pageSize,
          ...params
        }),
      );
      if (err) {
        throw err;
      }
      if (res?.code === "1") {
        this.list = res?.data as IWorkOrderState["list"];
        this.pagination.total = res?.total as number;
      }
    },
    setPagination({ current,pageSize}:PaginationProps){
      this.pagination.current = current
      this.pagination.pageSize = pageSize
    }
  },
});

export default useWorkOrder;
