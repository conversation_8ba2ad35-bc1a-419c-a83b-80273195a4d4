/*
 * @Description: 全局用于统一管理/获取字典数据的方法
 * @Author: yangrongxin
 * @Date: 2025-06-24 13:42:12
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-06-24 13:46:28
 */
import { DictTypeUsingGETSysDictData, getBdManageApiSystemDictDataTypeByDictType } from "@/batchApi";
import { defineStore } from "pinia";
import { reactive } from "vue";
import {
  to
} from 'await-to-js';


interface IDictData {
  // 字典项的名称: 字典项目的内容
  [props: string]: DictTypeUsingGETSysDictData[]
}

const useDictData = defineStore("dict-data", () => {
  const dictData = reactive<IDictData>({});  

  const getDictData = async (dictName: string) => {
    const localDictData = dictData[dictName];
    if (
      Array.isArray(localDictData) &&
      localDictData.length !== 0
    ) {
      return localDictData;      
    }
    const returnDictData = await getDictDataFromNet(dictName);
    return returnDictData
  }

  // 通过请求接口 更新当前的字典数据
  const getDictDataFromNet = async (dictName: string) => {
    const [err, res] = await to(getBdManageApiSystemDictDataTypeByDictType({
      dictType: dictName
    }))
    if (err) {
      throw err;
    }
    if (
      Array.isArray(res) 
    ) {
      dictData[dictName] = res
    }
    return res
  }

  return {
    getDictData
  }
})

export default useDictData;