/*
 * @Description: 存储快捷入口使用的state类型
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-12-17 17:43:04
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 20:04:56
 */

export interface IQuickAccessState {
  // 是否展示服务预约的弹窗
  showServiceReservation: boolean
  // 当前服务预约的ID -- 存在的时候 代表当前是编辑
  serviceReservationId: string
  patientId: string
  patientIds: string[]
  patientIdSMSList: Array<{patientId: number,phone: string}>[] 

  // 是否展示服务预约的弹窗
  showServiceAppointment: boolean
  // 当前服务预约的ID -- 存在的时候 代表当前是编辑
  ServiceAppointmentId: string
  mainPatientId: string | undefined

  // 签约信息的参数
  showTheSigningInterface: boolean
  showTheSigningIdCard: string

  // 短信编辑参数
  showSendSms: boolean,
  // 创建工单
  showWorkOrder: boolean

  // 推送评估的参数
  showSendEvaluation: boolean
  ticketId: number | undefined
}

export const defaultQuickAccessState = {
  // 保险服务预约的参数
  showServiceReservation: false,
  serviceReservationId: '',
  patientId: '',
  patientIds: [],
  patientIdSMSList: [],

  // 服务预约的参数
  showServiceAppointment: false,
  ServiceAppointmentId: '',
  mainPatientId: undefined,

  showTheSigningInterface: false,
  showTheSigningIdCard: '',
  
  // 短信编辑参数
  showSendSms: false,
  // 创建工单
  showWorkOrder: false,
  // 推送评估
  showSendEvaluation: false,
  ticketId: undefined
}