/*
 * @Description: 定义快捷入口需要使用到的全局调用工具的打开方式
 * @Author: yangrong<PERSON>
 * @Date: 2024-12-17 17:42:56
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-18 11:36:38
 */
import {
  defineStore,
} from 'pinia'
import {
  defaultQuickAccessState
} from './types'
import type {
  IQuickAccessState
} from './types'

const useQuickAccessStore = defineStore('quickAccess', {
  state: (): IQuickAccessState => ({ ...defaultQuickAccessState }),
  getters: {
    quickAccessState(state: IQuickAccessState): IQuickAccessState {
      return state
    }
  },
  actions: {
    // 控制保险服务预约的展示
    toggleServiceReservation(
      show: boolean,
      serviceReservationId?: string,
      patientId?: string
    ) {
      this.showServiceReservation = show;
      this.serviceReservationId = serviceReservationId ?? '';
      this.patientId = patientId ?? '';
    },

    // 控制服务预约的展示
    toggleServiceAppointment(
      show: boolean,
      serviceAppointmentId?: string,
      patientId?: string
    ) {
      this.showServiceAppointment = show;
      this.ServiceAppointmentId = serviceAppointmentId ?? '';
      this.mainPatientId = patientId ?? undefined;
    },
    // 控制签约的弹窗展示
    toggleSigningInterface(
      show: boolean,
      idCard = ''
    ) {
      this.showTheSigningInterface = show;
      this.showTheSigningIdCard = idCard;
    },
    // 控制短信的弹窗
    toggleSendSMS(show: boolean,patientIdSMSList: IQuickAccessState['patientIdSMSList']) {
      this.showSendSms = show;
      this.patientIdSMSList = patientIdSMSList;
    },
    // 控制创建工单
    toggleWorkOrderVisibleStatus (visible: boolean,ticketId?: number) {
      this.showWorkOrder = visible
      this.ticketId = ticketId ?? undefined
    },
    setPatientIds(_patientIds: string[]) {
      this.patientIds = _patientIds;
    },
    // 设置当前进行推送评估的人群
    toggleSendEvaluation(show: boolean, patientIds: string[] = []) {
      this.showSendEvaluation = show;
      this.setPatientIds(patientIds);
    },
  },
})

export default useQuickAccessStore;