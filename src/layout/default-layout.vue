<!--
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2024-09-20 15:57:10
 * @LastEditors: yangrong<PERSON>
 * @LastEditTime: 2024-11-22 17:52:26
-->
<template>
  <a-layout class="layout-demo">
    <a-layout-sider hide-trigger collapsible :collapsed="collapsed">
      <!-- 顶部的logo元素 -->
      <div class="logo">
        <img :src="!collapsed ? LogoPng : LogoMiniPng" alt="" />
      </div>
      <!-- 展示当前页面的菜单路由元素 -->
      <MenuEle />
    </a-layout-sider>
    <a-layout>
      <a-layout-header>
        <div class="left">
          <!-- 折叠菜单的按钮 -->
          <icon-menu-unfold
            v-if="collapsed"
            style="margin-right: 32px; font-size: var(--font-size-title-3)"
            class="cursor-pointer"
            @click="onCollapse"
          />
          <icon-menu-fold
            v-else
            style="margin-right: 32px; font-size: var(--font-size-title-3)"
            class="cursor-pointer"
            @click="onCollapse"
          />
          <!-- TODO: 面包屑导航的位置 需要单独抽离一个组件 记录所有历史访问的信息 -->
          <a-breadcrumb>
            <a-breadcrumb-item
              v-for="(route, index) in breadcrumbRoutes"
              :key="index"
              @click="navigateTo(route.path)"
            >
              {{ route.name }}
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        <RightNavBar />
      </a-layout-header>
      <a-layout>
        <a-layout-content>
          <PageLayout />
        </a-layout-content>
      </a-layout>
    </a-layout>
  </a-layout>
</template>

<script>
import { defineComponent, ref, watch } from "vue";
import { Message } from "@arco-design/web-vue";
// 菜单元素 用于定义右侧的菜单数据展示
import MenuEle from "@/components/menu/index.vue";
import LogoPng from "@/assets/images/logo.png";
import { useRoute, useRouter } from "vue-router";
import RightNavBar from "@/components/rightNavbar/index.vue";
import LogoMiniPng from "@/assets/images/logo-mini.png";
// 展示页面的主要内容组件
import PageLayout from "./page-layout.vue";

export default defineComponent({
  components: {
    RightNavBar,
    MenuEle,
    PageLayout,
  },
  setup() {
    const collapsed = ref(true);
    const onCollapse = () => {
      collapsed.value = !collapsed.value;
    };
    // 记录并展示面包屑的路由信息
    const breadcrumbRoutes = ref([]);
    const router = useRouter();
    const route = useRoute();
    const updateBreadcrumb = () => {
      breadcrumbRoutes.value = router
        .getRoutes()
        .filter((route) => route.meta.breadcrumb);
    };

    const navigateTo = (path) => {
      router.push(path);
    };

    // 监听路由变化以更新面包屑
    watch(() => route.currentRoute, updateBreadcrumb, { immediate: true });

    return {
      LogoPng,
      LogoMiniPng,
      collapsed,
      onCollapse,
      onClickMenuItem(key) {
        Message.info({ content: `You select ${key}`, showIcon: true });
      },
      // 路由的信息以及跳转的方法
      navigateTo,
      breadcrumbRoutes,
    };
  },
});
</script>

<style scoped>
.layout-demo {
  height: 100%;
  background: var(--color-fill-2);
  border: 1px solid var(--color-border);
}
.layout-demo :deep(.arco-layout-sider-light) {
  background-color: #192a53 !important;
}

.layout-demo :deep(.arco-layout-sider) .logo {
  text-align: center;
  height: 58px;
  line-height: 58px;
  background-color: var(--slider-bar-bg-color);
}
.layout-demo :deep(.arco-layout-sider) .logo > img {
  height: 40px;
  margin-top: 9px;
}
/* .layout-demo :deep(.arco-layout-sider-light) .logo {
    background: var(--color-fill-2);
  } */
.layout-demo :deep(.arco-layout-header) {
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* height: 64px; */
  height: 58px;
  /* line-height: 64px; */
  background: var(--color-bg-3);
  padding-left: var(--spacing-7);
}
.layout-demo :deep(.arco-layout-header) > .left {
  display: flex;
  align-items: center;
}
.layout-demo :deep(.arco-layout-footer) {
  height: 48px;
  color: var(--color-text-2);
  font-weight: 400;
  font-size: 14px;
  line-height: 48px;
}
.layout-demo :deep(.arco-layout-content) {
  color: var(--color-text-2);
  font-weight: 400;
  font-size: 14px;
  background: var(--color-bg-3);
}
.layout-demo :deep(.arco-layout-footer),
.layout-demo :deep(.arco-layout-content) {
  display: flex;
  flex-direction: column;
  /* justify-content: center;
  color: var(--color-white); */
  font-size: 16px;
  font-stretch: condensed;
  /* text-align: center; */
  height: 100%;
  background-color: #f2f3f5;
  padding: var(--spacing-7);
}
.arco-layout {
  overflow-y: hidden;
}
</style>
