import type { Router } from 'vue-router';
import { setRouteEmitter } from '@/utils/route-listener';
import setupUserLoginInfoGuard from './userLoginInfo';
import setupPermissionGuard from './permission';

function setupPageGuard(router: Router) {
  router.beforeEach(async (to) => {
    // emit route change
    setRouteEmitter(to);
  });
}

// 设置路由守卫
export default function createRouteGuard(router: Router) {
  // 设置页面守卫
  setupPageGuard(router);
  // 设置用户登陆信息守卫
  setupUserLoginInfoGuard(router);
  // 设置权限的钩子
  setupPermissionGuard(router);
}
