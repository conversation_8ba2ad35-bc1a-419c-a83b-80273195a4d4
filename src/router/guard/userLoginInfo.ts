/*
 * @Description: 对路由增加一个userInfo数据监控的钩子
 * @Author: yangrong<PERSON>
 * @Date: 2024-09-20 15:57:10
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-18 10:16:59
 */
import type { Router, LocationQueryRaw } from 'vue-router';
import NProgress from 'nprogress'; // progress bar

import { useUserStore } from '@/store';
import { isLogin } from '@/utils/auth';

export default function setupUserLoginInfoGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    NProgress.start();
    // 获取userInfo的数据
    const userStore = useUserStore();
    // 如果用户已经登陆了
    if (isLogin()) {
      // 当前用户存在角色 进入页面
      // if (userStore.roleInfo.roleCode) {
      if (userStore.roleInfo.roleType) {
        next();
      } else {
        try {
          // 尝试获取用户的基本信息
          await userStore.info();
          next();
        } catch (error) {
          // 如果没有获取到基本的信息 退出当前用户的登陆
          await userStore.logout();
          // 退出登陆之后 跳转到登陆的界面
          next({
            name: 'login',
            query: {
              redirect: to.name,
              ...to.query,
            } as LocationQueryRaw,
          });
        }
      }
    } else {
      // 如果当前用户还没有登陆 -- 但是其访问的是 不需要鉴权的域名 直接跳转到对应的域名内
      if (
        to.name === 'login' ||
        to.name === 'external-todo-items'
      ) {
        next();
        return;
      }
      next({
        name: 'login',
        query: {
          redirect: to.name,
          ...to.query,
        } as LocationQueryRaw,
      });
    }
  });
}
