/*
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2024-09-20 15:57:10
 * @LastEditors: yangrongxin
 * @LastEditTime: 2024-10-17 15:29:50
 */
import type { Router /** RouteRecordNormalized */ } from 'vue-router';
import NProgress from 'nprogress'; // progress bar

import usePermission from '@/hooks/permission';
// import { useUserStore } from '@/store';
// import { appRoutes } from '../routes';
import { /** WHITE_LIST */ NOT_FOUND } from '../constants';

export default function setupPermissionGuard(router: Router) {
  router.beforeEach(async (to, from, next) => {
    // 获取app的store信息
    // const appStore = useAppStore();
    // 获取用户的store信息
    // const userStore = useUserStore();
    // 获取权限的store信息
    const Permission = usePermission();
    // 判断当前页面的路由是不是有对应的权限
    const permissionsAllow = Permission.accessRouter(to);
    // eslint-disable-next-line no-lonely-if
    if (permissionsAllow) next();
    else {
      // 当前路由不能访问 访问第一条有权限的路由
      // const destination =
      //   Permission.findFirstPermissionRoute(appRoutes, userStore.roleInfo.roleCode) ||
      //   NOT_FOUND;
      const destination = NOT_FOUND;
      next(destination);
    }

    // // 当前的路由是不是来自服务器
    // if (appStore.menuFromServer) {
    //   // 针对来自服务端的菜单配置进行处理
    //   // Handle routing configuration from the server

    //   // 根据需要自行完善来源于服务端的菜单配置的permission逻辑
    //   // Refine the permission logic from the server's menu configuration as needed
    //   if (
    //     !appStore.appAsyncMenus.length &&
    //     !WHITE_LIST.find((el) => el.name === to.name)
    //   ) {
    //     await appStore.fetchServerMenuConfig();
    //   }
    //   const serverMenuConfig = [...appStore.appAsyncMenus, ...WHITE_LIST];

    //   let exist = false;
    //   // 根据当前的请求路由 是不是已经在本地路由进行处理
    //   while (serverMenuConfig.length && !exist) {
    //     const element = serverMenuConfig.shift();
    //     if (element?.name === to.name) exist = true;

    //     if (element?.children) {
    //       serverMenuConfig.push(
    //         ...(element.children as unknown as RouteRecordNormalized[])
    //       );
    //     }
    //   }
    //   if (exist && permissionsAllow) {
    //     next();
    //   } else next(NOT_FOUND);
    // } else {
    //   // 当前路由可以访问 直接跳转
    //   // eslint-disable-next-line no-lonely-if
    //   if (permissionsAllow) next();
    //   else {
    //     // 当前路由不能访问 访问第一条有权限的路由
    //     const destination =
    //       Permission.findFirstPermissionRoute(appRoutes, userStore.roleInfo.roleCode) ||
    //       NOT_FOUND;
    //     next(destination);
    //   }
    // }
    NProgress.done();
  });
}
