/*
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2025-04-28 20:34:37
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-06-09 15:50:11
 */
/*
 * @Description: 文书管理涉及的相关页面
 * @Author: yangrong<PERSON>
 * @Date: 2024-09-20 15:57:10
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-06-09 15:41:04
 */
import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const PAPERWORKEDITOR: AppRouteRecordRaw = {
  path: '/paperwork-editor',
  name: 'paperwork-editor',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.paperworkManagementEdit',
    requiresAuth: true,
    hideChildrenInMenu: true,
    icon: 'icon-pen-fill',
    order: 2,
  },
  children: [
    // 文书填写列表
    {
      path: '',
      name: 'paperwork-editor',
      component: () => import('@/views/paperwork-editor/index.vue'),
      meta: {
        locale: 'menu.paperworkManagementEdit',
        requiresAuth: true,
        roles: ['*'],
      },
    },
    // 文书填写详情
    {
      path: '/paperwork-editor/form-editor',
      name: 'paperwork-editor-content', 
      component: () => import("@/views/paperwork-editor/form-editor.vue"),
      meta: {
        locale: 'menu.formEditor',
        requiresAuth: true,
        roles: ['*']
      }
    },
  ],
};

export default PAPERWORKEDITOR;
