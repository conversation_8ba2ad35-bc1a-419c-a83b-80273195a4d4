/*
 * @Description: 文书管理涉及的相关页面
 * @Author: yangrong<PERSON>
 * @Date: 2024-09-20 15:57:10
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-19 21:51:04
 */
import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const PAPERWORKMANAGEMENT: AppRouteRecordRaw = {
  path: '/paperwork-management',
  name: 'paperwork-management',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.paperworkManagement',
    requiresAuth: true,
    hideChildrenInMenu: true,
    icon: 'icon-book',
    order: 5,
  },
  children: [
    // 文书模板管理
    {
      path: '',
      name: 'paperwork-management',
      component: () => import('@/views/paperworkManagement/index.vue'),
      meta: {
        locale: 'menu.paperworkManagement',
        requiresAuth: true,
        roles: ['*'],
      },
    },
    {
      path: '/paperwork-management/form-editor',
      name: 'paperwork-form-editor', 
      component: () => import("@/views/paperworkManagement/form-editor.vue"),
      meta: {
        locale: 'menu.formEditor',
        requiresAuth: true,
        roles: ['*']
      }
    },
  ],
};

export default PAPERWORKMANAGEMENT;
