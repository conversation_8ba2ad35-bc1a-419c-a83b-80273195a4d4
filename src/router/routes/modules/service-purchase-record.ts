/*
 * @Description: 服务预约记录涉及的页面
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-12-18 14:30:40
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-19 21:54:17
 */
import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const ServiceReservationRecord: AppRouteRecordRaw = {
  path: '/service-purchase-record',
  name: 'service-purchase-record',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.servicePurchaseRecord',
    requiresAuth: true,
    hideChildrenInMenu: true,
    icon: 'icon-find-replace',
    order: 9,
  },
  children: [
    {
      path: '',
      name: 'service-purchase-record',
      component: () => import('@/views/service-purchase-record/index.vue'),
      meta: {
        locale: 'menu.servicePurchaseRecord',
        requiresAuth: true,
        roles: ['*'],
      },
    }
  ]
}

export default ServiceReservationRecord