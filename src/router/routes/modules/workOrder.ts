/*
 * @Description: 工单管理
 * @Author: 李洪培
 * @Date: 2024-12-18 14:30:40
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-19 21:50:57
 */
import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const ServiceReservationRecord: AppRouteRecordRaw = {
  path: '/work-order',
  name: 'work-order',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.workOrder',
    requiresAuth: true,
    hideChildrenInMenu: true,
    icon: 'icon-tool',
    order: 4,
  },
  children: [
    {
      path: '',
      name: 'work-order',
      component: () => import('@/views/work-order-manage/index.vue'),
      meta: {
        locale: 'menu.workOrder',
        requiresAuth: true,
        roles: ['*'],
      },
    }
  ]
}

export default ServiceReservationRecord