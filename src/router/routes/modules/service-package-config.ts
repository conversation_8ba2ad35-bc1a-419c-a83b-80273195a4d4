/*
 * @Description: 服务包配置界面
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-06-24 14:30:40
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-19 21:53:22
 */
import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const ServicePackageConfig: AppRouteRecordRaw = {
  path: '/service-package-config',
  name: 'service-package-config',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.servicePackageConfig',
    requiresAuth: true,
    hideChildrenInMenu: true,
    icon: 'icon-settings',
    order: 6,
  },
  children: [
    {
      path: '',
      name: 'service-package-config',
      component: () => import('@/views/service-package-config/index.vue'),
      meta: {
        locale: 'menu.servicePackageConfig',
        requiresAuth: true,
        roles: ['*'],
      },
    }
  ]
}

export default ServicePackageConfig