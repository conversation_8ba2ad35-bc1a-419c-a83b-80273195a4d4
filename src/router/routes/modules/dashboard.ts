/*
 * @Description: 仪表盘/工作台的页面 现在暂时不需要使用
 * @Author: yangrong<PERSON>
 * @Date: 2024-09-20 15:57:10
 * @LastEditors: yangrongxin
 * @LastEditTime: 2024-10-21 15:05:17
 */
import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

// 工作台对应的路由
const DASHBOARD: AppRouteRecordRaw = {
  path: '/workbench',
  name: 'workbench',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.dashboard',
    requiresAuth: true,
    hideChildrenInMenu: true,
    icon: 'icon-dashboard',
    order: 0,
  },
  children: [
    {
      path: '',
      name: 'workbench',
      component: () => import('@/views/workbench/index.vue'),
      meta: {
        locale: 'menu.dashboard.workbench',
        requiresAuth: true,
        roles: ['*'],
      },
    },
  ],
};

export default DASHBOARD;
