// /*
//  * @Description: 诊疗记录
//  * @Author: lihongpei
//  * @Date: 2024-11-18 15:57:10
//  * @LastEditors: lihongpei
//  */
// import { DEFAULT_LAYOUT } from '../base';
// import { AppRouteRecordRaw } from '../types';

// const DIAGNOSIS: AppRouteRecordRaw = {
//   path: '/diagnosis',
//   name: 'diagnosis',
//   component: DEFAULT_LAYOUT,
//   meta: {
//     locale: 'menu.diagnosis',
//     requiresAuth: true,
//     hideInMenu: true,
//     icon: 'icon-dashboard',
//     order: 0,
//   },
//   children: [
//     {
//       path: 'diagnosis-record',
//       name: 'diagnosisRecord',
//       component: () => import('@/views/diagnosisRecord/index.vue'),
//       meta: {
//         locale: 'menu.diagnosisRecord',
//         requiresAuth: true,
//         roles: ['*'],
//       },
//     },
//   ],
// };

// export default DIAGNOSIS;