/*
 * @Description: 系统设置
 * @Author: lihongpei
 * @Date: 2024-11-18 15:57:10
 * @LastEditors: yangrongxin
 */
import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const Dicts: AppRouteRecordRaw = {
  path: '/dict',
  name: 'Dict',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.dict',
    requiresAuth: true,
    hideInMenu: false,
    icon: 'icon-storage',
    order: 13
  },
  children: [
    {
      path: 'tag-manage',
      name: 'tagManage',
      component: () => import('@/views/system/tag-manage/index.vue'),
      meta: {
        locale: 'menu.tagManage',
        requiresAuth: true,
        roles: ['*'],
      },
    },
    {
      path: 'organ-manage',
      name: 'organManage',
      component: () => import('@/views/system/organ-manage/index.vue'),
      meta: {
        locale: 'menu.organManage',
        requiresAuth: true,
        roles: ['*'],
      },
    },
    {
      path: 'department-manage',
      name: 'departmentManage',
      component: () => import('@/views/system/department-manage/index.vue'),
      meta: {
        locale: 'menu.departmentManage',
        requiresAuth: true,
        roles: ['*'],
      },
    }
  ],
};

export default Dicts;