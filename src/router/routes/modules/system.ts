/*
 * @Description: 系统设置
 * @Author: lihongpei
 * @Date: 2024-11-18 15:57:10
 * @LastEditors: yangrongxin
 */
import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const System: AppRouteRecordRaw = {
  path: '/system',
  name: 'System',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.system',
    requiresAuth: true,
    hideInMenu: false,
    icon: 'icon-settings',
    order: 12
  },
  children: [
    {
      path: 'waitstaff',
      name: 'waitstaff',
      component: () => import('@/views/system/waitstaff/index.vue'),
      meta: {
        locale: 'menu.waitstaff',
        requiresAuth: true,
        roles: ['*'],
      },
    },
    // 团队管理
    {
      path: 'team-manage',
      name: 'teamManage',
      component: () => import('@/views/system/team-manage/index.vue'),
      meta: {
        locale: 'menu.teamManage',
        requiresAuth: true,
        roles: ['*'],
      }
    },
    // 账号分配
    {
      path: 'account',
      name: 'account',
      component: () => import('@/views/system/account/index.vue'),
      meta: {
        locale: 'menu.accountSetting',
        requiresAuth: true,
        roles: ['*'],
      },
    },
  ],
};

export default System;