/*
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2024-10-21 14:55:23
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-19 21:51:27
 */
import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const TARGETPOPULATION: AppRouteRecordRaw = {
  // 获取目标人群的页面
  path: '/group',
  name: 'group',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.group',
    requiresAuth: true,
    hideChildrenInMenu: true,
    icon: 'icon-user-group',
    roles: ['*'],
    order: 1,
  },
  children: [
    {
      path: '/group',
      // path: '',
      name: 'group',
      // name: 'group',
      // component: DEFAULT_LAYOUT,
      component: () => import('@/views/home/<USER>'),
      meta: {
        // 展示国际化的文案
        // locale: 'menu.targetPopulation',
        locale: 'menu.group',
        requiresAuth: true,
        icon: 'icon-user-group',
        roles: ['*'],
      }
    }
  ]
}

export default TARGETPOPULATION;