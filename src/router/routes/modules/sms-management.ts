import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const SmsManagement: AppRouteRecordRaw = {
  path: '/sms-management',
  name: 'sms-management',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.sms',
    requiresAuth: true,
    hideChildrenInMenu: true,
    icon: 'icon-list',
    order: 11,
  },
  children: [
    {
      path: '',
      name: 'sms-management',
      component: () => import('@/views/sms-logs/index.vue'),
      meta: {
        locale: 'menu.sms',
        requiresAuth: true,
        roles: ['*'],
      },
    }
  ]
}

export default SmsManagement