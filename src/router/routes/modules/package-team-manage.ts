/*
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2025-07-19 20:57:33
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-19 22:12:10
 */
import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const PackageTeamManage: AppRouteRecordRaw = {
  path: '/package-team-manage',
  name: 'packageTeamManage',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.packageTeamInfo',
    requiresAuth: true,
    hideChildrenInMenu: true,
    icon: 'icon-settings',
    order: 7,
  },
  children: [
    {
      path: '',
      name: 'packageTeamManage',
      component: () => import('@/views/system/package-team-manage/index.vue'),
      meta: {
        locale: 'menu.packageTeamInfo',
        requiresAuth: true,
        roles: ['*'],
      },
    }
  ]
}

export default PackageTeamManage;