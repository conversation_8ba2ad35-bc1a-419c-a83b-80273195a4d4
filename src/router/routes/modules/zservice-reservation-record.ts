/*
 * @Description: 服务预约记录涉及的页面
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-12-18 14:30:40
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-19 21:54:56
 */
import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const ServiceReservationRecord: AppRouteRecordRaw = {
  path: '/service-reservation-record',
  name: 'service-reservation-record',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.serviceReservationRecord',
    requiresAuth: true,
    hideChildrenInMenu: true,
    icon: 'icon-list',
    order: 8,
  },
  children: [
    {
      path: '',
      name: 'service-reservation-record',
      component: () => import('@/views/service-reservation-record/index.vue'),
      meta: {
        locale: 'menu.serviceReservationRecord',
        requiresAuth: true,
        roles: ['*'],
      },
    }
  ]
}

export default ServiceReservationRecord