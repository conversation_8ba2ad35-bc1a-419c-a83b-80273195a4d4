/*
 * @Description: 服务预约页面
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-12-18 14:30:40
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-19 21:53:48
 */
import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const ServiceReservationRecord: AppRouteRecordRaw = {
  path: '/service-appointment',
  name: 'service-appointment',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.serviceAppointment',
    requiresAuth: true,
    hideChildrenInMenu: true,
    icon: 'icon-bookmark',
    order: 10,
  },
  children: [
    {
      path: '',
      name: 'service-appointment',
      component: () => import('@/views/service-appointment/index.vue'),
      meta: {
        locale: 'menu.serviceAppointment',
        requiresAuth: true,
        roles: ['*'],
      },
    }
  ]
}

export default ServiceReservationRecord