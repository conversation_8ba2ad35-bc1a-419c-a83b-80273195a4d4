<!--
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2024-12-12 17:58:41
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-25 15:58:21
-->
<template>
  <div class="table-wrapper">
    <!-- 新增模板处理 -->
    <table-filter
      :types-arr="typeArr"
      @table-filter="getTemplateList"
    />
    <!-- 表格的内容 -->
    <div class="table-container">
      <a-table
        :data="tableData"
        :pagination="pagination"
        @page-change="(current: number) => (pagination.current = current)"
        @page-size-change="
          (pageSize: number) => (pagination.pageSize = pageSize)
        "
      >
        <template #columns>
          <a-table-column
            title="序号"
            :width="65"
          >
            <template #cell="{ rowIndex }">
              <span>{{ rowIndex + 1 }}</span>
            </template>
          </a-table-column>
          <a-table-column
            title="模板名称"
            data-index="docName"
          >
            <template #cell="{ record }">
              <!-- 问卷类型的模板 直接展示模板的名称 如果是 文书类型的模板 直接展示模板的类型 -->
              {{ record.docType === EDocType.Question ? record?.docName : record.typeName }}
            </template>
          </a-table-column>
          <!-- <a-table-column
            title="模板类型"
            data-index="typeName"
          ></a-table-column> -->
          <a-table-column
            title="所属业务"
            data-index="businessName"
          >
            <template #cell="{ record }">
              <!-- 问卷类型的模板 直接展示模板的名称 如果是 文书类型的模板 直接展示模板的类型 -->
              {{ record.docType === EDocType.Question ? "-" : record.businessName }}
            </template>
          </a-table-column>
          <a-table-column
            title="状态"
          >
            <template #cell="{ record }">
              <span v-if="record.status === EPaperStatus.STOP" :style="{ color: '#F53F3F' }">停用</span>
              <span v-else-if="record.status === EPaperStatus.ENABELE" :style="{ color: '#00B42A' }">启用</span>
              <span v-else-if="record.status === EPaperStatus.DELETE">删除</span>
              <span v-else-if="record.status === EPaperStatus.TSSAVE" :style="{ color: '#666' }">暂存</span>
            </template>
          </a-table-column>
          <a-table-column
            title="启用时间"
            data-index="enabledTime"
          ></a-table-column>
          <a-table-column
            title="停用时间"
            data-index="stopTime"
          ></a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <div class="table-operate">
                <a-button
                  v-if="record.status === EPaperStatus.STOP || record.status === EPaperStatus.TSSAVE"
                  size="mini"
                  type="outline"
                  @click="handleChangeStatus(EPaperStatus.ENABELE, record)"
                >
                  启用
                </a-button>
                <a-button
                  v-if="record.status === EPaperStatus.ENABELE"
                  size="mini"
                  type="outline"
                  @click="handleChangeStatus(0, record)"
                >
                  停用
                </a-button>
                <a-button
                  v-if="record.status === EPaperStatus.TSSAVE || record.status === EPaperStatus.STOP"
                  size="mini"
                  type="outline"
                  @click="handleEdit(record)"
                >
                  编辑
                </a-button>
                <a-popconfirm
                  content="确认要删除此条记录?"
                  type="info"
                  :on-before-ok="(done) => handleChangeStatus(2, record, done)"
                >
                  <a-button
                    v-if="record.status === EPaperStatus.TSSAVE"
                    size="mini"
                    type="outline"
                  >
                    删除
                  </a-button>
                </a-popconfirm>
                <a-button
                  v-if="record.status === EPaperStatus.ENABELE || record.status === EPaperStatus.STOP"
                  size="mini"
                  type="outline"
                  @click="handleViewForm(record)"
                >
                  查看
                </a-button>
              </div>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { postBdManageApiDocCreateOrUpdateTemplate, postBdManageApiDocGetDocTypeBusiness, postBdManageApiDocGetDocTypeBusinessResponse, postBdManageApiDocQueryDocTemplateList, postBdManageApiDocQueryDocTemplateListResponse, postBdManageApiDocQueryDocTemplatePage } from '@/batchApi';
import { EDocType, EPaperStatus } from '@/types/enums'
import { PaginationProps } from '@arco-design/web-vue';
import tableFilter from './components/table-filter.vue';
import { EOprType } from './form-editor.vue';

const tableData = ref<postBdManageApiDocQueryDocTemplateListResponse['data']>([]);
const typeArr = ref<postBdManageApiDocGetDocTypeBusinessResponse['data']>([]);
const router = useRouter();
const pagination = reactive<PaginationProps>({
  size: "small",
  showTotal: true,
  showJumper: true,
  showPageSize: true,
  total: 0,
  current: 1,
  pageSize: 10,
})

const handleChangeStatus = (
  _status: EPaperStatus,
  _record: any,
  _done?: (closed: boolean) => void 
) => {
  postBdManageApiDocCreateOrUpdateTemplate({
    ..._record,
    docType: _record.docType || EDocType.Document,
    status: _status
  }).then(_res => {
    if (
      _res
    ) {
      // this.$message.succ÷ess('修改成功！');
      getTemplateList();
      if (
        typeof _done === 'function'
      ) {
        _done(true)
      }
    } else if (
      typeof _done === 'function'
    ) {
      _done(false)
    }
  }).catch(() => {
    if (
      typeof _done === 'function'
    ) {
      _done(false)
    }
  })
}

// 查看表单的结构
const handleViewForm = (_record) => {
  router.push({
    name: 'paperwork-form-editor',
    query: {
      formId: _record.formId,
      schemaId: _record.schemaId,
      templateId: _record.templateId,
      subcategory: _record.typeId,
      business: _record.businessId,
      subcategoryName: _record.typeName,
      formName: _record.formName,
      businessName: _record.businessName,
      docName: _record.docName,
      docType: _record.docTYpe,
      type: EOprType.View
    }
  })
}

const handleEdit = (
  _record
) => {
  router.push({
    name: 'paperwork-form-editor',
    query: {
      formId: _record.formId,
      schemaId: _record.schemaId,
      templateId: _record.templateId,
      subcategory: _record.typeId,
      business: _record.businessId,
      subcategoryName: _record.typeName,
      formName: _record.formName,
      businessName: _record.businessName,
      docName: _record.docName,
      docType: _record.docType ?? EDocType.Document,
      type: EOprType.Edit
    }
  })
  console.log('_record', _record)
}

const getInitEnums = () => {
  postBdManageApiDocGetDocTypeBusiness({})
    .then(_initRes => {
      if (
        Array.isArray(_initRes.data) &&
        _initRes.data.length !== 0
      ) {
        typeArr.value = _initRes.data
      }
      console.log('_initRes', _initRes)
    })
}

const getTemplateList = (_reqParams = {}) => {
  postBdManageApiDocQueryDocTemplateList(_reqParams).then(_res => {
    if (
      Array.isArray(_res.data)
    ) {
      tableData.value = _res.data
    }
    console.log('_res', _res)
  })
  postBdManageApiDocQueryDocTemplatePage({
    pageNum: pagination.current,
    pageSize: pagination.pageSize,
    query: _reqParams
  }).then(_res => {
    if (
      Array.isArray(_res.data?.content)
    ) {
      tableData.value = _res.data?.content;
    }
    pagination.total = _res.data?.total;
    console.log('_res', _res)
  })
}

onMounted(() => {
  getInitEnums();
  getTemplateList();
})

</script>

<style lang="scss" scoped>
.table-wrapper {
  background-color: white;
  padding: 16px;
  overflow-y: auto;
  .table-operate {
    display: flex;
    gap: 8px;
  }
}
</style>