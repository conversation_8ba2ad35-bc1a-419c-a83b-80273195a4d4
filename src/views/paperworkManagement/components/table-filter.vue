<!--
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2024-12-12 18:11:20
 * @LastEditors: yangrong<PERSON>
 * @LastEditTime: 2025-07-23 23:11:48
-->
<template>
  <div class="table-filter">
    <a-button
      type="primary"
      @click="visible = true"
    >
      <template #icon>
        <icon-plus />
      </template>
      <template #default>
        新增模板
      </template>
    </a-button>
    <a-form
      :model="form"
      layout="inline"
    >
      <a-form-item field="docType" label="模板类型">
        <a-select v-model="form.docType" :style="{width:'180px'}" placeholder="请选择" allow-clear>
          <a-option
            :value="EDocType.Document"
          >
            文书
          </a-option>
          <a-option
            :value="EDocType.Question"
          >
            问卷
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="status" label="状态">
        <a-select v-model="form.status" :style="{width:'180px'}" placeholder="请选择" allow-clear>
          <a-option :value="EPaperStatus.ENABELE">启用</a-option>
          <a-option :value="EPaperStatus.STOP">停用</a-option>
          <!-- <a-option :value="2">删除</a-option> -->
          <a-option :value="EPaperStatus.TSSAVE">暂存</a-option>
        </a-select>
      </a-form-item>
    </a-form>
  </div>
  <!-- 表格以外的其他元素 -->
  <a-modal
    v-model:visible="visible"
    :on-before-ok="handleBeforeOk"
    @ok="handleOk"
    @cancel="visible = false"
  >
    <template #title>
      新增模板
    </template>
    <a-form
      ref="modalFormRef"
      :model="modalForm"
    >
      <a-form-item
        field="docType"
        label="模板类型"
        :rules="[{ required: true, message: '请选择模板类型' }]"
      >
        <a-radio-group
          v-model="modalForm.docType"
        >
          <a-radio :value="EDocType.Document">文书</a-radio>
          <a-radio :value="EDocType.Question">问卷</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        v-if="modalForm.docType === EDocType.Question"
        field="docName"
        label="模板名称"
        :rules="[{ required: true, message: '请选择模板名称' }]"
      >
        <a-input
          v-model="modalForm.docName"
          placeholder="请填写模板名称"
        >
        </a-input>
      </a-form-item>
      <a-form-item
        v-if="modalForm.docType === EDocType.Document"
        field="typeId"
        label="模板名称"
        :rules="[{ required: true, message: '请选择模板名称' }]"
      >
        <a-select
          v-model="modalForm.typeId"
          :style="{width:'100%'}"
          placeholder="请选择模板名称"
        >
          <a-option
            v-for="_type, _key in typesArr"
            :key="_key"
            :value="_type.typeId"
          >
            {{ _type.typeName }}
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item
        v-if="modalForm.docType === EDocType.Document"
        field="businessId"
        label="所关联业务"
        :rules="[{ required: true, message: '请选择所关联业务' }]"
      >
        <a-select
          v-model="modalForm.businessId"
          :style="{width:'100%'}"
          placeholder="请选择所关联业务"
        >
          <a-option
            v-for="_type, _key in businessList"
            :key="_key"
            :value="_type.businessId"
          >
            {{ _type.businessName }}
          </a-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>

</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { EPaperStatus, EDocType } from '@/types/enums'
import { EOprType } from '../form-editor.vue';

const router = useRouter()

const modalFormRef = ref();
const visible = ref(false)
const form = reactive({
  docType: undefined,
  status: undefined
});

const modalForm = reactive({
  typeId: '',
  businessId: '',
  docType: EDocType.Document,
  docName: '',
})

const props = defineProps([
  'typesArr'
])

const emits = defineEmits([
  'tableFilter'
])

console.log('_props_', props)

watch([
  () => form.status,
  () => form.docType
], () => {
  console.log('new_vale')
  emits('tableFilter', {
    docType: form.docType,
    status: form.status
  })
})

watch(() => modalForm.typeId, (newVal, oldVal) => {
  if (
    oldVal !== newVal
  ) {
    modalForm.businessId = ""
  }
})

const businessList = computed(() => {
  return props.typesArr.find((_fi: any) => _fi.typeId === modalForm.typeId)?.docBusinessList
});

// 弹窗点击确定的前置事件
const handleBeforeOk = async () => {
  const errors = await modalFormRef.value.validate();
  console.log('errors', errors)
  if (!errors) {
    return true
  }
  return false;
}

// 前置事件执行成功后执行
const handleOk = () => {
  router.push({
    name: 'paperwork-form-editor',
    query: {
      docName: modalForm.docName,
      docType: modalForm.docType,
      subcategory: modalForm.typeId,
      subcategoryName: props.typesArr?.find((_fi: any) => _fi.typeId === modalForm.typeId)?.typeName,
      business: modalForm.businessId,
      businessName: props.typesArr?.find((_fi: any) => _fi.typeId === modalForm.typeId)?.docBusinessList?.find((_fi: any) => _fi.businessId === modalForm.businessId)?.businessName,
      type: EOprType.Add
    }
  })
}

</script>

<style lang="scss" scoped>
.table-filter {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  :deep(.arco-form-layout-inline .arco-form-item) {
    margin-bottom: 0px !important;
  }
}
</style>