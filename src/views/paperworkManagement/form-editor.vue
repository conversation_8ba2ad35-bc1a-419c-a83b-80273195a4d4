<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-12-13 09:23:24
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-26 17:55:30
-->
<template>
  <div class="form-editor-container">
    <div class="form-editor-toolBar">
      <div class="types">
        <div class="item">
          <span>问卷类型：</span>
          <span>{{ query.docType === EDocType.Document ? '文书' : '问卷' }}</span>
        </div>
        <div class="item">
          <span>问卷名称：</span>
          <span>{{ query.docType === EDocType.Document ? query.subcategoryName : query.docName }}</span>
        </div>
        <div
          v-if="query.docType === EDocType.Document"
          class="item"
        >
          <span>关联的业务：</span>
          <span>{{ query.businessName }}</span>
        </div>
      </div>
      <div class="tools">
        <a-button
          @click="handleReturn"
        >
          <template #icon>
            <icon-arrow-left />
          </template>
          <template #default>返回</template>
        </a-button>
        <a-button
          v-if="query.type !== EOprType.View"
          type="primary"
          :loading="tsLoading"
          @click="handleTemporaryStorage"
        >
          <template #icon>
            <icon-save />
          </template>
          <template #default>暂存</template>
        </a-button>
        <a-button
          v-if="query.type !== EOprType.View"
          type="primary"
          :loading="loading"
          @click="handleSave"
        >
          <template #icon>
            <icon-save />
          </template>
          <template #default>保存并启用</template>
        </a-button>
      </div>
    </div>
    <div class="iframe-container">
      <iframe
        ref="formIframeRef"
        :src="iframeUrl"
        frameborder="0"
      >
      </iframe>
    </div>
  </div>
</template>

<script setup lang="ts">
import { postBdManageApiDocCheckHasEnable, postBdManageApiDocCreateOrUpdateTemplate } from '@/batchApi';
import { onMounted, onUnmounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Message, Modal } from '@arco-design/web-vue'
import { EPaperStatus, EDocType } from '@/types/enums';
import {
  to
} from 'await-to-js'

const postMsgType = 'HxFormEngine'
const iframeUrl = ref();;
// const addUrl = ref('http://localhost:8808/#/design');
// 编辑入口
// const editUrl = ref("http://localhost:8808/#/design?formId=707887111331646550&schemaId=707887111331646551&formVersion=1&formName=%E6%A0%87%E5%87%86%E6%96%87%E4%B9%A6&contentId=");
const route = useRoute()
const router = useRouter()
const formIframeRef = ref();
const tsLoading = ref(false);
const loading = ref(false);
// 完成的事件
const doneEvent = ref();

const {
  query
} = route

// 根据表单编辑器返回的操作信息 进行对应的操作
const formEditorEvent = (event: MessageEvent) => {
  const {
    type,
    data
  } = event.data
  // 通过表单编辑器  进行通知 保存表单的数据
  if (
    type === `${postMsgType}_saveData`
  ) {
    postBdManageApiDocCreateOrUpdateTemplate({
      formId: data.formId,
      schemaId: data.schemaId,
      typeId: query?.subcategory,
      businessId: query?.business,
      templateId: query?.templateId,
      status: EPaperStatus.ENABELE,
      docType: query?.docType,
      docName: query?.docName
    }).then(_res => {
      if (
        typeof doneEvent.value === 'function'
      ) {
        doneEvent.value(true)
      }
      loading.value = false;
      if (
        _res.data?.templateId
      ) {
        Message.success("新建表单成功")
        handleReturn();
      }
    })
    // 保存模板
    console.log('_formEditorEvent_data_', data)
  } else if (
    // 通过表单编辑器  进行通知 暂存表单的数据
    type === `${postMsgType}_tsData`
  ) {
    postBdManageApiDocCreateOrUpdateTemplate({
      formId: data.formId,
      schemaId: data.schemaId,
      typeId: query?.subcategory,
      businessId: query?.business,
      templateId: query?.templateId,
      status: EPaperStatus.TSSAVE,
      docType: query?.docType,
      docName: query?.docName
    }).then(_res => {
      loading.value = false;
      Message.success("暂存表单成功")
      handleReturn();
    })
    // 保存并启用
    console.log('_formEditorEvent_data_', data)
  }
}

onMounted(() => {
  const urlSearch = new URLSearchParams()
  urlSearch.append('thirdPlatform', 'bd'); // 定义当前是第三方平台 北大使用编辑页面

  console.log('query', query, urlSearch.toString())
  if (
    query.type === EOprType.Add
  ) {
    urlSearch.append('formName', `${query?.subcategory}-${query.business}-${new Date().getTime()}`);
    iframeUrl.value = `${import.meta.env.VITE_FORM_DESIGN_BASE_URL}/#/design?${urlSearch.toString()}`
  } else if (
    query.type === EOprType.Edit ||
    query.type === EOprType.View
  ) {
    urlSearch.append('formId', query?.formId); // 定义当前是第三方平台 北大使用编辑页面
    urlSearch.append('schemaId', query?.schemaId);
    urlSearch.append('formVersion', query?.formVersion);
    urlSearch.append('formName', query?.formName);
    urlSearch.append('contentId', query?.contentId);
    iframeUrl.value = `${import.meta.env.VITE_FORM_DESIGN_BASE_URL}/#/design?${urlSearch.toString()}`
  }
  console.log('_iframeUrl_', iframeUrl.value)

  // 挂载的时候 注入当前交互的事件
  window.addEventListener('message', formEditorEvent, false);
})

onUnmounted(() => {
  window.removeEventListener('message', formEditorEvent)
})

// 返回到上一个页面
const handleReturn = () => {
  router.back()
}

// 暂存当前的模板数据
const handleTemporaryStorage = () => {
  formIframeRef.value.contentWindow.postMessage({
    type: `${postMsgType}_tsData`,
    data: {}
  }, '*')
  tsLoading.value = true;
}

// 保存当前模板的数据
const handleTemplateSave = () => {
  formIframeRef.value.contentWindow.postMessage({
    type: `${postMsgType}_saveData`,
    data: {}
  }, '*')
  loading.value = true
  // postBdManageApiDocCreateOrUpdateTemplate({
  //     formId: "679950718167665654",
  //     schemaId: "787230702079837008",
  //     typeId: query?.subcategory,
  //     businessId: query?.business,
  //     templateId: query?.templateId,
  //     status: EPaperStatus.ENABELE,
  //     docType: query?.docType,
  //     docName: query?.docName
  //   }).then(_res => {
  //     if (
  //       typeof doneEvent.value === 'function'
  //     ) {
  //       doneEvent.value(true)
  //     }
  //     loading.value = false;
  //     if (
  //       _res.data?.templateId
  //     ) {
  //       Message.success("新建表单成功")
  //       handleReturn();
  //     }
  //   })
}

// 保存当前的模板数据
const handleSave = async () => {
  if (
    query.docType === EDocType.Question
  ) {
    handleTemplateSave()
    return 
  }
  // 查询接口进行提示 如果用户同意 再进行保存
  const [checkError, checkRes] = await to(postBdManageApiDocCheckHasEnable({
    typeId: query.subcategory as unknown as number,
    businessId: query.business as unknown as number
  }))
  if (
    checkError
  ) {
    throw checkError;
  }
  // 如果当前存在其他启用状态的数据的时候 进行提示
  if (
    Array.isArray(checkRes.data) &&
    checkRes.data?.length > 0
  ) {
    Modal.confirm({
      title: '保存提示',
      content: '还存在相同模板，请确认是否停用？',
      onBeforeOk(done) {
        doneEvent.value = done;
        // 调用保存的接口 保存当前的表单的数据
        handleTemplateSave()
      },
      onCancel() {
        // 取消 结束保存的进程
        console.log('handleCancel')
        if (
          typeof doneEvent.value === 'function'
        ) {
          doneEvent.value(false)
        }
        loading.value = false;
      }
    })
  } else {
    // 不存在其他相同状态的数据的时候 不需要进行提示 直接进行保存
    handleTemplateSave();
  }
  console.log('checkRes', checkRes)
}

</script>

<script lang="ts">
export const enum EOprType {
  Add = 'add',
  Edit = 'edit',
  View = 'view'
}
</script>

<style lang="scss" scoped>
.form-editor-container {
  height: 100%;
  background-color: white;
  // 第三方平台中的操作栏
  .form-editor-toolBar {
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #E5E6EB;
    >.types {
      display: flex;
      gap: 32px;
      >.item {
        >span:nth-of-type(1) {
          font-weight: bold;
        }
      }
    }
    >.tools {
      display: flex;
      gap: 16px;
    }
  }
  .iframe-container {
    background-color: #FAFBFC;
    height: calc(100% - 80px);
    iframe {
      width: 100%;
      height: 100%;
    }
  }
}
</style>