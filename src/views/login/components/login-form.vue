<!--
 * @Description: login登录组件
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-07-14 18:34:21
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 16:42:29
-->
<template>
  <div class="login-form-wrapper">
    <div class="login-form-wrapper-left">

    </div>
    <div class="login-form-wrapper-right">
      <div class="login-form-title">
        <!-- @vue-ignore -->
        {{ $t('login.form.title') }}
      </div>
      <a-form
        ref="loginForm"
        :model="userInfo"
        class="login-form"
        layout="vertical"
        @submit="handleSubmit"
      >
        <!-- @vue-ignore -->
        <a-form-item
          label="手机号"
          field="phone"
          :rules="[{ required: true, message: $t('login.form.phone.errMsg') }]"
          :validate-trigger="['change', 'blur']"
        >
          <!-- @vue-ignore -->
          <a-input
            v-model="userInfo.phone"
            :placeholder="$t('login.form.phone.placeholder')"
          >
            <template #prefix>
              <icon-phone />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item
          label="验证码"
          field="authCode"
          :rules="[{ required: true, message: $t('login.form.authCode.errMsg') }]"
        >
          <ercode
            v-model="userInfo.authCode"
            @get-auth-code="handleAuthCode"
          />
        </a-form-item>
        <a-space :size="16" direction="vertical">
          <a-button size="large" type="primary" html-type="submit" long :loading="loading">
            <!-- @vue-ignore -->
            {{ $t('login.form.login') }}
          </a-button>
        </a-space>
      </a-form>
    </div>
  </div>
  <!-- 展示选择角色和团队的控件 -->
</template>

<script lang="ts" setup>
  import { h, reactive, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { Message, Modal } from '@arco-design/web-vue';
  import { ValidatedError } from '@arco-design/web-vue/es/form/interface';
  import { useI18n } from 'vue-i18n';
  import { useStorage } from '@vueuse/core';
  import { useUserStore } from '@/store';
  import useLoading from '@/hooks/loading';
  import to from 'await-to-js';
  import { postBdManageApiUserLoginByPhone, postBdManageApiUserLoginByTeamIdRoleIdUserId } from '@/batchApi';
  import { setToken } from '@/utils/auth';
  import infoConfirm from './info-confirm.vue';
  import ercode from './ercode.vue';

  const router = useRouter();
  const { t } = useI18n();
  const { loading, setLoading } = useLoading();
  const userStore = useUserStore();
  const infoConfirmRef = ref();

  const loginConfig = useStorage('login-config', {
    rememberPassword: false,
    phone: '', // 演示默认值
    authCode: '', // demo default value
  });
  const userInfo = reactive({
    // 验证码在测试环境默认是 888888 
    phone: loginConfig.value.phone ?? '',
    authCode: loginConfig.value.authCode ?? '',
  });

  const handleAuthCode = async (_successFn?: (_bool: boolean) => void) => {
    if (
      typeof userInfo.phone === 'string' &&
      userInfo.phone !== ''
    ) {
      await userStore.getAuthCode({
        phone: userInfo.phone,
      });
      if (
        typeof _successFn === 'function'
      ) {
        _successFn(true)
      }
    } else {
      Message.error({
        content: '请先输入手机号！',
      });
      if (
        typeof _successFn === 'function'
      ) {
        _successFn(false)
      }
    }
  };

  const handleSubmit = async ({
    errors,
    values,
  }: {
    errors: Record<string, ValidatedError> | undefined;
    values: Record<string, any>;
  }) => {
    if (loading.value) return;
    if (
      !userStore.userInfo.authCodeInfo.authKey
    ) {
      Message.info('请先获取验证码！')
      loading.value = false;
      return 
    }
    setLoading(true);

    // 是否使用新版本的校验方式
    const useNew = false;

    if (
      useNew
    ) {
      // 新版本登陆接口
      const [err, res] = await to(postBdManageApiUserLoginByPhone({
        phoneNum: userInfo.phone,
        authCode: userInfo.authCode,
        channelCode: 'health',
        appCode: 'HEALTHAPP'
      }))
      if (
        err
      ) {
        setLoading(false);
        throw err;
      }
      const resData = res.data;
      if (
        typeof resData?.info?.userId === 'string' &&
        resData?.info?.userId !== ''
      ) {
        if (
          typeof resData.token === 'string' &&
          resData.token !== ''
        ) {
          setToken(resData.token)
        }
        Modal.open({
          width: '465px',
          title: '请选择需要登陆信息',
          content: () => h(infoConfirm, {
            ref: infoConfirmRef,
            userId: resData?.info?.userId as string
          }),
          async onBeforeOk(done) {
            const formData = infoConfirmRef.value.form;
            const [err, res] = await to(userStore.loginByRoleAndTeam(
              // @ts-ignore
              resData?.info?.userId,
              formData.roleId,
              formData.teamId
            ))
            if (err) {
              done(false)
              throw err;
            }
            const { redirect, ...othersQuery } = router.currentRoute.value.query;
            router.push({
              name: (redirect as string) || 'workbench',
              query: {
                ...othersQuery,
              },
            });
            console.log('formData', formData, res)
            setLoading(false);
            done(true)
          },
        })
      }
    } else {
      // 调用登陆的接口 进行登陆 -- 历史版本
      const [err, res] = await to(
        userStore.loginByPhone({
          ...userStore.userInfo.authCodeInfo,
          phone: userInfo.phone,
          authCode: userInfo.authCode,
          deviceNum: '123321',
          deviceType: 'WEB',
        })
      );
      if (err) {
        loading.value = false;
        throw err;
      }
      // Message.success(t('login.form.login.success'));
      const { redirect, ...othersQuery } = router.currentRoute.value.query;
      router.push({
        name: (redirect as string) || 'workbench',
        query: {
          ...othersQuery,
        },
      });
      setLoading(false);
      console.log('_res_', err, res);  
    }

    // if (!errors) {
    //   setLoading(true);
    //   try {
    //     await userStore.login(values as LoginData);
    //     const { redirect, ...othersQuery } = router.currentRoute.value.query;
    //     router.push({
    //       name: (redirect as string) || 'Workplace',
    //       query: {
    //         ...othersQuery,
    //       },
    //     });
    //     Message.success(t('login.form.login.success'));
    //     const { rememberPassword } = loginConfig.value;
    //     const { username, password } = values;
    //     // 实际生产环境需要进行加密存储。
    //     // The actual production environment requires encrypted storage.
    //     loginConfig.value.username = rememberPassword ? username : '';
    //     loginConfig.value.password = rememberPassword ? password : '';
    //   } catch (err) {
    //     errorMessage.value = (err as Error).message;
    //   } finally {
    //     setLoading(false);
    //   }
    // }
  };
  const setRememberPassword = (value: boolean) => {
    loginConfig.value.rememberPassword = value;
  };
</script>

<style lang="less" scoped>
  .login-form {
    // 最外层的样式设置
    &-wrapper {
      width: 1000px;
      height: 600px;
      display: flex;
      align-items: center;
      border-radius: 24px;
      background-color: white;
      &-left {
        flex: 1;
        height: 100%;
        background-image: url('../../../assets/images/login-left-bg.png');
        background-size: 100% 100%;
      }
      &-right {
        display: flex;
        flex-direction: column; 
        justify-content: space-between;
        flex: 1;
        height: 100%;
        margin: 0 40px;
        padding: 40px 0;
        // 登陆的表单元素的控制
        >form.arco-form {
          >div {
            // 展示第一个标题元素的内容
            &:nth-child(1),
            &:nth-child(2){
              // 每一个label元素展示的样式
              :deep(div.arco-form-item-label-col) {
                >label.arco-form-item-label {
                  font-size: 16px;
                  font-weight: bold;
                  >strong {
                    display: none;
                  }
                }
              }
              // 每一个content元素展示的样式
              :deep(div.arco-form-item-wrapper-col) {
                // 设置input输入框的样式
                .arco-input-wrapper {
                  height: 50px;
                  svg, input.arco-input {
                    font-size: 16px;
                  }
                }
              }
            }
            // 展示的是最后一个分类的div元素
            &:last-child {
              margin-top: 70px;
              margin-bottom: 80px;
              // 展示登陆按钮的数据
              button {
                height: 54px;
                font-size: 16px;
                font-weight: bold;
                letter-spacing: 2px;
                border-radius: 10px;
              }
            }
          }
        }
      }
    }

    &-title {
      color: var(--color-text-1);
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;
    }

    &-sub-title {
      color: var(--color-text-3);
      font-size: 16px;
      line-height: 24px;
    }

    &-error-msg {
      height: 32px;
      color: rgb(var(--red-6));
      line-height: 32px;
    }

    &-password-actions {
      display: flex;
      justify-content: space-between;
    }

    &-register-btn {
      color: var(--color-text-3) !important;
    }
  }
</style>
