<template>
  <a-form
    :model="form"
  >
    <a-form-item
      field="roleId"
      label="角色"
    >
      <a-select
        v-model="form.roleId"
        :style="{width:'320px'}"
        placeholder="请选择角色"
      >
        <a-option
          v-for="item in roleList"
          :key="item.id"
          :value="item.roleType"
        >
          {{ item.roleName }}
        </a-option>
      </a-select>
    </a-form-item>
    <a-form-item
      
      field="teamId"
      label="团队"
    >
      <a-select
        v-model="form.teamId"  
        :style="{width:'320px'}"
        placeholder="请选择团队"
      >
        <a-option
          v-for="item in teamList"
          :key="item.teamId"
          :value="item.teamId"
        >
          {{ item.name }}
        </a-option>
      </a-select>
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import { getBdManageApiUserQueryRoleByUserId, getBdManageApiUserQueryTeamByRoleType, getBdManageApiUserQueryTeamByUserId, QueryRoleByUserIdUsingGETUserRoleInfo, QueryTeamByRoleTypeUsingGETUserTeamInfoVo } from '@/batchApi';
import { onMounted, reactive, ref, watch } from 'vue';

const roleList = ref<QueryRoleByUserIdUsingGETUserRoleInfo[]>([]);
const teamList = ref<QueryTeamByRoleTypeUsingGETUserTeamInfoVo[]>([]);

const {
  userId
} = defineProps<{
  userId: string | number
}>()

const form = ref({
  roleId: '',
  teamId: ''
});

const getTeamByRoleId = () => {
  getBdManageApiUserQueryTeamByUserId({
    userId: userId as unknown as number,
  }).then(_res => {
    if (
      Array.isArray(_res.data)
    ) {
      teamList.value = _res.data;
    }
  })
}

onMounted(() => {
  getTeamByRoleId();
  getBdManageApiUserQueryRoleByUserId({
    userId: userId as unknown as number
  }).then(_res => {
    if (
      Array.isArray(_res.data)
    ) {
      roleList.value = _res.data;
    }
    console.log('_res', _res)
  })
})

defineExpose({
  form
})

</script>

<style scoped>

</style>