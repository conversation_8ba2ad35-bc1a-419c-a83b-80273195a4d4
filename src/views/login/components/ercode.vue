<!--
 * @Description: 用于获取验证码使用的组件
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-10-24 16:50:42
 * @LastEditors: yangrongxin
 * @LastEditTime: 2024-11-15 09:19:57
-->
<template>
  <div class="ercode-container">
    <a-input
      v-model="authCode"
      placeholder="请输入验证码"
      allow-clear
      max-length="6"
      @change="onFinish"
    >
      <template #prefix>
        <icon-message />
      </template>
      <template #append>
        <a-button 
          type="primary" 
          :disabled="isCountingDown"
          @click="handleAuthCode" 
        >
          {{ buttonText }}
        </a-button>
      </template>
    </a-input>
    <!-- <a-verification-code v-model="authCode" @finish="onFinish" />
    <a-button 
      type="primary" 
      :disabled="isCountingDown"
      @click="handleAuthCode" 
    >
      {{ buttonText }}
    </a-button> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

// 验证码的数据
const authCode = ref('')
const countdown = ref(0)
const isCountingDown = ref(false)

const emits = defineEmits([
  'getAuthCode',
  'update:modelValue'
])

const buttonText = computed(() => {
  return isCountingDown.value ? `${countdown.value}秒后重试` : '获取验证码'
})

const onFinish = () => {
  emits('update:modelValue', authCode.value);
}

const startCountdown = () => {
  isCountingDown.value = true
  countdown.value = 30
  const timer = setInterval(() => {
    countdown.value -= 1;
    if (countdown.value <= 0) {
      clearInterval(timer)
      isCountingDown.value = false
    }
  }, 1000)
}

const handleAuthCode = () => {
  emits('getAuthCode', (
    _result: boolean
  ) => {
    if (
      _result
    ) {
      startCountdown()
    }
  })
}

</script>

<style lang="scss" scoped>
.ercode-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-7);
}
</style>
