<!--
 * @Description: 处理服务人群相关的信息
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-10-17 17:10:57
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 14:59:13
-->
<template>
  <div class="group-select-container">
    <!-- 默认当前是获取子节点的数据 如果当前的节点没有子节点 就返回的当前节点的数据 -->
    <a-tree
      v-model:checked-keys="checkedKeys"
      :block-node="true"
      :multiple="true"
      :checkable="true"
      :data="treeData"
      :field-names="fieldNames"
      checked-strategy="child"
      @check="handleCheck"
    >
      <template #extra="nodeData">
        <div
          v-if="nodeData.name !== '全部'"
          class="tree-node-extra"
        >
          {{ nodeData.count }}
        </div>
      </template>
      <template #title="nodeData">
        <div
          class="group-select-tooltip"
        >
          <a-tooltip
            :content="nodeData.describe || nodeData.name"
            mini
          >
            <!-- <a-button :class="{'package-name': nodeData.level === 3}">{{ nodeData.name }}</a-button> -->
            <a-button :class="{'package-name': nodeData.level === 3}">
              <span class="ellipsis">{{ nodeData.name }}</span>
            </a-button>
          </a-tooltip>
        </div>
      </template>
    </a-tree>
  </div>
</template>

<script setup lang="ts">
import { ICategoryItem } from '@/api/patients';
import { watch, onMounted, ref } from 'vue'
import { useTargetPopulation } from '@/store';
import type { TreeNodeData, TreeProps } from '@arco-design/web-vue/es/tree/interface';

const fieldNames = {
  key: 'categoryId',
  title: 'name',
  children: 'subs',
}
const targetPopulation = useTargetPopulation()
const prop = defineProps([
  'crowdType'
])
const treeData = ref<TreeNodeData[]>([])
// 来源的id和当前的服务包的id的结合
// 默认选中当前是 全部的数据
const checkedKeys = ref<string[]>(["13954334545654323"])

watch([
  () => targetPopulation.getState.serviceGroupIds,
  () => targetPopulation.getState.customerSources,
], (
  [
    newServiceGroupIds,
    newCustomerSources
  ]
) => {
  console.log('_newServiceGroupIds_', newServiceGroupIds)
  // let newCheckedValue = [...newServiceGroupIds, ...newCustomerSources];
  // TODO: 如果存在权益不存在对应的包的情况 需要修改对应的参数
  let newCheckedValue = newServiceGroupIds;
  const allOldHas = checkedKeys.value.every(_item => {
    return newCheckedValue.findIndex(_fi => _fi === _item) !== -1
  })
  const totalOtherCodes = [...(treeData.value as unknown as ICategoryItem[])
    .filter(_fi => _fi.name !== '全部')
    .map(_item => _item.categoryId)]
  const allOtherCodes = [...(treeData.value as unknown as ICategoryItem[])
    .filter(_fi => _fi.name === '全部')
    .map(_item => _item.categoryId)]
  const hasAll = totalOtherCodes.every(_item => {
    return newCustomerSources.findIndex(_fi => _fi === _item) !== -1
  })
  // 如果除了全部以外的其他节点 都已经被选中 需要增加一个全部的选中
  if (
    !allOldHas
  ) {
    if (
      hasAll
    ) {
      newCheckedValue = newCheckedValue.concat(allOtherCodes)
    }
    checkedKeys.value = newCheckedValue;
  } else {
    // TODO: 如果存在来源下面没有服务包的情况 需要扩展这里的数据
    checkedKeys.value = newServiceGroupIds;
    if (
      hasAll
    ) {
      checkedKeys.value = checkedKeys.value.concat(allOtherCodes)
    }
  } 
})

// 添加一个辅助函数来根据 ID 查找节点
const findNodeById = (nodes: TreeNodeData[], id: string): ICategoryItem | null => {
  // eslint-disable-next-line no-restricted-syntax
  for (const node of nodes) {
    const typedNode = node as unknown as ICategoryItem;
    if (typedNode.categoryId === id) {
      return typedNode;
    }
    if (typedNode.subs && typedNode.subs.length > 0) {
      const found = findNodeById(typedNode.subs as TreeNodeData[], id);
      if (found) return found;
    }
  }
  return null;
}


const handleCheck: TreeProps['onCheck'] = (_checkedKeys, { node }) => {
  let inlineServiceGroupIds: string[] = [];
  let inlineCustomerSources: string[] = [];
  // 检查是否点击的是“全部”节点
  if ((node as unknown as ICategoryItem)?.name === '全部') {
    if (_checkedKeys.includes((node as unknown as ICategoryItem)?.categoryId)) {
      // 如果选中"全部"节点，选中除"全部"节点外的所有子节点
      inlineServiceGroupIds = [...(treeData.value as unknown as ICategoryItem[])
        .filter(item => Array.isArray(item.subs) && item.subs.length !== 0 && item.name !== '全部')
        .map(_item => _item.subs)
        .reduce((b, a) => (b || []).concat(a))
        .map(item => item.categoryId)];
      inlineCustomerSources = [...(treeData.value as unknown as ICategoryItem[])
        .filter(_fi => _fi.name !== '全部')
        .map(_item => _item.categoryId)]
    } else {
      // 如果取消选中“全部”节点，取消全部的数据选中
      inlineServiceGroupIds = [];
      inlineCustomerSources = [];
    }
  } else {
    // 处理普通节点的选中逻辑
    const currentNode = node as unknown as ICategoryItem;
    // 当前节点是一个来源节点 并且它没有子元素
    if (currentNode.parentName === '全部' && (!currentNode.subs || currentNode.subs.length === 0)) {
      // 如果是父节点名为"全部"且没有子节点的情况，将其添加到 customerSources
      inlineCustomerSources = checkedKeys.value.filter(key => {
        const node = findNodeById(treeData.value, key);
        return node?.parentName === '全部' && (!node.subs || node.subs.length === 0);
      });
      // 其他节点添加到 serviceGroupIds
      inlineServiceGroupIds = checkedKeys.value.filter(key => {
        const node = findNodeById(treeData.value, key);
        return !(node?.parentName === '全部' && (!node.subs || node.subs.length === 0));
      });
    } else {
      checkedKeys.value.forEach(_item => {
        // 找到当前选中元素的每一个节点
        const node = findNodeById(treeData.value, _item) as ICategoryItem
        if (
          // 子节点的id
          node.level === 3
        ) {
          inlineServiceGroupIds.push(node.categoryId)
          // 要根据子节点获取对应的父节点id
          const {
            pid
          } = node;
          if (
            // 没有包括的节点 需要加入全局
            inlineCustomerSources.findIndex(_fi => _fi === pid) === -1
          ) {
            inlineCustomerSources.push(pid)
          }
        } else if (
          node.level === 2
        ) {
          inlineCustomerSources.push(node.categoryId)
        }
        console.log('_node_', node)
      })
    }
  }
  console.log('setCheckedValue', inlineServiceGroupIds, inlineCustomerSources);
  targetPopulation.changeServiceGroupIds(inlineServiceGroupIds);
  targetPopulation.changeCustomerSources(inlineCustomerSources);
}

/**
 * 获取服务人群的分类
 */
const init = async () => {
  const inlineTreeData = await targetPopulation.queryCategoryList(prop.crowdType);
  treeData.value = inlineTreeData as unknown as TreeNodeData[];
  targetPopulation.changeReloadServiceGroup(false);
}

watch(() => targetPopulation.getState.reloadServiceGroup, () => {
  if (
    targetPopulation.getState.reloadServiceGroup === true
  ) {
    init()
  }
})

onMounted(async () => {
  await init();
})
</script>

<style lang="scss" scoped>
.group-select-container {
  padding: var(--size-2);
  border: 1px solid var(--border-color);
  // 树组件控制节点的样式 设置
  .tree-node-extra {
    position: absolute;
    right: 16px;
  }
  :deep(.group-select-tooltip) {
    max-width: 240px;
    >button {
      height: auto !important;
      padding: 0 !important;
      background-color: transparent !important;
      display: block;
      text-align: left;
      // width: 120px;
      white-space: nowrap; /* 确保文本不换行 */
      overflow: hidden; /* 超出部分隐藏 */
      text-overflow: ellipsis;
      &.package-name {
        // width: 90px;
      }
    }
  }
}
.ellipsis {
  display: block;
  width: 8vw;
  white-space: nowrap; /* 确保文本不换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis;
}
</style>