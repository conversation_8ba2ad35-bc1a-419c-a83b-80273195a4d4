<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-10-17 15:32:33
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-19 10:35:40
-->
<template>
  <div class="target-population-wrapper">
    <a-split default-size="240px" min="300px">
      <template #first>
        <div class="target-population-tabs">
          <a-button type="primary" @click="handleAddCustomer">
            <template #icon>
              <icon-plus />
            </template>
            <template #default>
              新增客户信息
            </template>
          </a-button>
          <!-- 展示人群分组的列表 -->
          <a-tabs default-active-key="2" class="custom-tabs">
            <a-tab-pane key="1" disabled>
              <template #title>
                <div class="tab-disabled">
                  潜在人群
                  <span>功能开发中</span>
                </div>
                <!-- <group-select
                  :crowd-type="ECrowdType.PotentialPopulation"
                /> -->
              </template>
            </a-tab-pane>
            <a-tab-pane key="2" title="服务人群" class="tabPanel">
              <!-- 展示当前服务人群的筛选 -->
              <group-select :crowd-type="ECrowdType.ServicePopulation" />
            </a-tab-pane>
          </a-tabs>
        </div>
      </template>
      <template #second>
        <!-- 表格展示人群的数据 -->
        <div class="target-population-table">
          <bd-table />
        </div>
      </template>
    </a-split>
  </div>
</template>

<script setup lang="ts">
import useQuickAccessStore from "@/store/modules/quick-access";
// import { postBdManageApiPackageAdd } from '@/batchApi';
import { ref } from "vue";
import { ECrowdType } from "@/types/enums";
import bdTable from "@/components/bd-table/index.vue";
import groupSelect from "./components/group-select.vue";

const quickAccessStore = useQuickAccessStore();

const size = ref(0.5);

// 新增客户信息
const handleAddCustomer = () => {
  console.log("quickAccessStore", quickAccessStore);
  quickAccessStore.toggleSigningInterface(true);
  // 新增一个服务包的接口
  // postBdManageApiPackageAdd({
  //   name: '肥胖',
  //   code: 'FAT'
  // }).then(res => {
  //   console.log('_res', res)
  // })
};
</script>

<style lang="scss" scoped>
.custom-tabs {
  :deep(.arco-tabs-nav-tab) {
    display: none !important;
  }
}
div {
  color: var(--shadow-none);
  min-height: 100%;
}
.tab-disabled {
  > span {
    &:before {
      position: relative;
      top: -2px;
      left: -5px;
      content: "";
      display: inline-block;
      width: 0;
      height: 0;
      border-right: 5px solid var(--disabled-bg-color);
      border-bottom: 2.5px solid transparent;
      border-top: 2.5px solid transparent;
    }
    margin-left: var(--spacing-1);
    color: var(--disabled-font-color);
    border-radius: var(--border-radius-medium);
    padding: var(--spacing-1) var(--spacing-2) var(--spacing-1) 0;
    font-size: 10px;
    background-color: var(--disabled-bg-color);
  }
}
.target-population-wrapper {
  display: flex;
  gap: var(--spacing-4);
  width: 100%;
  background-color: #f2f3f5;
  // 展示选择目标人群的数据
  .target-population-tabs {
    border-radius: var(--border-radius-medium);
    background-color: #fff;
    .tabPanel {
      padding: 0 var(--spacing-7) var(--spacing-7) var(--spacing-7);
    }
    > button {
      width: 100%;
    }
  }
  // 展示目标人群对应的表格
  .target-population-table {
    flex: 1;
    overflow-x: auto;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    :deep(.bd-table-wrapper) {
      height: calc(100vh - 58px - 32px - 2px);
    }
  }
}
</style>
