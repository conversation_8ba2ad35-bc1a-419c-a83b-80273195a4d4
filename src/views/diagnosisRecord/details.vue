<!--
* @description 详情
* @fileName details.vue
* <AUTHOR>
* @date 2024/11/19 15:53:09
!-->
<script setup lang="ts">
import { ref } from "vue";

const data = [
  {
    label: "姓名",
    value: "张三",
    span: 4,
  },
  {
    label: "性别",
    value: "男",
    span: 4,
  },
  {
    label: "年龄",
    value: "54",
    span: 4,
  },
  {
    label: "联系方式",
    value: "18109068582",
    span: 4,
  },
  {
    label: "证件号",
    value: "513022199508148017",
    span: 8,
  },
  {
    label: "首诊描述",
    value:
      "首诊才有这个字段， 首诊才有这个字段， 首诊才有这个字段， 首诊才有这个字段， 首诊才有这个字段，首诊才有这个字段，首诊才有这个字段。",
    span: 24,
  },
];

const data1 = [
  {
    label: "医生",
    value: "张三",
    span: 4,
  },
  {
    label: "科室",
    value: "呼吸内科 ",
    span: 10,
  },
  {
    label: "就诊时间",
    value: "2024-09-23 11:30",
    span: 10,
  },
  {
    label: "主诉",
    value: "连续咳嗽三天，嗓子疼痛难忍，不能顺畅的吞咽食物，有痰，黄色。",
    span: 24,
  },
  {
    label: "药物过敏史",
    value: "否认药物过敏史",
    span: 24,
  },
  {
    label: "个人史",
    value: "否认个人史",
    span: 24,
  },
  {
    label: "诊断",
    value: "上呼吸道感染",
    span: 24,
  },
];
const data2 = [
  {
    label: "预约时间",
    value: "2024年10月14日 12:30:20",
    span: 8,
  },
  {
    label: "结束时间",
    value: "2024年10月14日 12:30:20 ",
    span: 8,
  },
  {
    label: "取消时间",
    value: "-",
    span: 8,
  },
];
const imgurl =
  "https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/a8c8cdb109cb051163646151a4a5083b.png~tplv-uwbnlip3yd-webp.webp";
const imgList = [imgurl, imgurl, imgurl];
</script>

<template>
  <div class="drawer-content-view">
    <!-- 患者信息 -->
    <a-descriptions
      layout="vertical"
      :data="data"
      title="患者信息"
      :column="24"
      bordered
    />
    <a-descriptions class="report-view" layout="inline-horizontal">
      <a-descriptions-item label="报告内容">
        <div class="custom-report-img">
          <a-image
            v-for="item in imgList"
            :key="item"
            width="80"
            height="80"
            :src="item"
          />
        </div>
      </a-descriptions-item>
    </a-descriptions>
    <!-- 处方信息 -->
    <a-descriptions
      title="处方信息"
      layout="vertical"
      table-layout="fixed"
      bordered
      :column="24"
    >
      <a-descriptions-item label="中药处方（2024-09-23 11:30）" :span="24">
        <div class="custom-descriptions-prescription">
          <span v-for="item in 15" :key="item">黄芩20g</span>
        </div>
      </a-descriptions-item>
      <a-descriptions-item label="西药处方（一）" :span="24">
        <div class="drug-view">
          <div v-for="item in 4" :key="item" class="custom-descriptions-drug">
            <div class="info">
              <span class="drug">阿美拉挫（奥美拉挫肠溶片）胶囊剂</span>
              <span>1mg*12片/盒</span>
            </div>
            <div class="num">
              <span>每次60mg,qid,口服</span>
              <span>*1盒</span>
            </div>
          </div>
        </div>
      </a-descriptions-item>
      <a-descriptions-item label="服用说明" :span="12">
        <div class="custom-descriptions-prescription">
          <span v-for="item in 4" :key="item">煎服</span>
        </div>
      </a-descriptions-item>
      <a-descriptions-item label="备注" :span="12">
        饭前服用，饮食清淡
      </a-descriptions-item>
    </a-descriptions>
    <!-- 病例信息 -->
    <a-descriptions
      layout="vertical"
      :data="data1"
      title="患者信息"
      :column="24"
      bordered
    />
    <!-- 沟通内容 -->
    <a-descriptions title="沟通内容" bordered>
      <a-descriptions-item label="点击查看">
        <a-link href="link">https://arco.design/vue/component</a-link>
      </a-descriptions-item>
    </a-descriptions>
    <!-- 权益核销记录 -->
    <a-descriptions
      layout="vertical"
      table-layout="fixed"
      title="权益核销记录"
      bordered
      :data="data2"
      :column="24"
    />
  </div>
</template>

<style lang="scss" scoped>
.drawer-content-view {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-7);
  .report-view {
    .custom-report-img {
      display: flex;
      margin-left: var(--spacing-7);
      gap: var(--spacing-7);
    }
  }

  .custom-descriptions-prescription {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-4) var(--spacing-7);
  }

  .drug-view {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-7);
    .custom-descriptions-drug {
      .info {
        display: flex;
        justify-content: space-between;
        margin-bottom: var(--spacing-4);
        color: var(--text-span-color);
        font-size: var(--font-size-caption);

        .drug {
          font-size: var(--font-size-body-3);
        }
      }
      .num {
        display: flex;
        justify-content: space-between;
        color: var(--text-gray-color);
        font-size: var(--font-size-caption);
      }
    }
  }
}
</style>
