<!--
* @description 诊疗记录
* @fileName index.vue
* <AUTHOR>
* @date 2024/11/18 10:54:14
!-->
<script setup lang="ts">
import { reactive, ref } from "vue";
import BdSlect from "@/components/bd-select/index.vue";
import { FormInstance } from "@arco-design/web-vue";
import PageContainer from "@/components/page-container/index.vue";
import { columns } from "./config";
import Details from "./details.vue";

type IState = {
  visible: boolean;
  treatyVisible: boolean;
  treatyForm: any;
};
const formRef = ref<FormInstance>();
const pageState = reactive<IState>({
  visible: false,
  treatyVisible: false,
  treatyForm: {},
});

const handleOk = () => {};

const handleSubmit = (done: (closed: boolean) => void) => {
  formRef.value?.validate().then((err) => {
    if (!err) {
      // todo
      done(true);
    } else {
      done(false);
    }
  });
};
</script>

<template>
  <PageContainer class="diagnosis-record-view">
    <a-table :columns="columns" :data="[{ name: 1 }]">
      <template #optional="{ record }">
        <a-space size="mini">
          <a-button
            size="small"
            type="text"
            @click="pageState.treatyVisible = true"
            >改约取消</a-button
          >
          <a-button size="small" type="text" @click="pageState.visible = true"
            >详情</a-button
          >
        </a-space>
      </template>
    </a-table>
    <a-drawer
      title="详情"
      :width="790"
      :visible="pageState.visible"
      :footer="false"
      unmount-on-close
      @ok="handleOk"
      @cancel="() => (pageState.visible = false)"
    >
      <Details />
    </a-drawer>
    <a-modal
      v-model:visible="pageState.treatyVisible"
      :width="600"
      title="改约取消"
      @before-ok="handleSubmit"
      @cancel="pageState.treatyVisible = false"
    >
      <a-form ref="formRef" :model="pageState.treatyForm" layout="inline">
        <a-form-item field="name" label="选择医生">
          <BdSlect
            v-model="pageState.treatyForm.teamId"
            placeholder="请选择医生"
          />
        </a-form-item>
        <a-form-item
          field="date"
          label="选择日期"
          :rules="[{ required: true, message: '请选择日期' }]"
        >
          <a-date-picker
            v-model="pageState.treatyForm.date"
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="选择时段" field="isRead">
          <a-radio-group v-model="pageState.treatyForm.shijianduan">
            <a-radio value="A">08:00-09:00</a-radio>
            <a-radio value="B">09:00-10:00</a-radio>
            <a-radio value="C">10:00-11:00</a-radio>
            <a-radio value="D">10:00-12:00</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </PageContainer>
</template>

<style lang="scss" scoped>
.diagnosis-record-view {
  overflow-x: auto;
}
</style>
