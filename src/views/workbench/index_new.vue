<!--
* @description 工作台
* @fileName index.vue
* <AUTHOR>
* @date 2024/11/19 17:49:46
!-->
<script setup lang="ts">
import { reactive, computed } from "vue";
import PageContainer from "@/components/page-container/index.vue";
import * as apis from "@/batchApi";
import { useUserStore } from '@/store';
import ManPng from '@/assets/images/man.png';
import WomanPng from '@/assets/images/woman.png';
import {
  backlogMap,
  BacklogType,
  BacklogTypeEnum,
  componentMap,
} from "./config";
import ManageAffairs from "./components/manage-affairs.vue";
import quickAccess from './components/quick-access.vue';

// import {
//   useUserStore
// } from '../'

type List = apis.GetPatCallListUsingPOSTCallListRespVo;
type IState = {
  visible: boolean;
  compkey: BacklogType;
  tabKey: number | string;
  data: List[];
  countList: apis.PatCallCountUsingPOSTCallCountRespVo[];
  teamFinishCount: number;
  signedRenewalCount: number;
};

const userStore = useUserStore();

const pageState = reactive<IState>({
  visible: false,
  compkey: BacklogTypeEnum.doctorCallBack,
  tabKey: 0,
  data: [],
  countList: [],
  teamFinishCount: 0,
  signedRenewalCount: 0
});

const title = computed(() => {
  if (pageState.compkey === 1) {
    const total = findCount("JYHD")?.count;
    return `待办-家医回电(${total})`;
  }
  if (
    pageState.compkey === BacklogTypeEnum.followUp
  ) {
    return `待办-随访`
  }
  if (
    pageState.compkey === BacklogTypeEnum.contractExpiration
  ) {
    return `待办-签约到期`
  }
  console.log('pageState', pageState)
  return "待办";
});

const doctorName = computed(() => {
  const userInfo = localStorage.getItem("user_info");
  const userInfoData = userInfo ? JSON.parse(userInfo) : {};
  return userInfoData?.accountInfo?.name;
});

const organName = computed(() => {
  const userInfo = localStorage.getItem("user_info");
  const userInfoData = userInfo ? JSON.parse(userInfo) : {};
  return userInfoData?.accountInfo?.organName;
})

const avatarPng = computed(() => {
  const {
    sex
  } = userStore.userInfo.accountInfo;
  if (
    sex === 2
  ) {
    return WomanPng;
  }
  return ManPng;
})

const getCallCount = async () => {
  try {
    // 获取家医回电的统计数据
    const { data } = await apis.postBdManageApiCallPatCallCount({ type: 1 });
    pageState.countList = data as apis.PatCallCountUsingPOSTCallCountRespVo[];
    // 获取随访的统计数据
    const {
      data: followTeamCount
    } = await apis.postBdManageApiFollowupGetBdFollowCount({
      teamId: userStore.accountInfo.teamId as unknown as number
    })
    pageState.teamFinishCount = followTeamCount?.followTotal ?? 0;
    console.log('followTeamCount', followTeamCount, pageState.countList)
    // 获取签约的统计数据
    const {
      data: signedRenewalCount
    } = await apis.postBdManageApiSignGetSignedRenewalCount({
      teamId: userStore.accountInfo.teamId as unknown as number
    })
    pageState.signedRenewalCount = signedRenewalCount?.count ?? 0;
    console.log('signedRenewalCount', signedRenewalCount)
  } catch (error) {
    console.log("===>", error);
  }
};

const findCount = (key: string) => {
  console.log('findCountKey', key)
  if (
    // 当前是随访类型的时候 返回当前的团队完成数量
    key === 'sf'
  ) {
    console.log('teamFinishCount', pageState.teamFinishCount);
    return {
      count: pageState.teamFinishCount
    };
  }
  if (
    key === 'qydq'
  ) {
    return {
      count: pageState.signedRenewalCount
    };
  }
  const record = pageState.countList?.find((item) => item.key === key);
  return record;
};

const handOpenDetails = (key: BacklogType) => {
  pageState.compkey = key;
  pageState.visible = true;
};

const handTabChange = (key: number | string) => {
  pageState.tabKey = key;
};

getCallCount();
</script>

<template>
  <div class="main-container">
    <!-- 展示医生的待办信息 -->
    <div class="doctor-container">
      <!-- 展示医生的基本信息 -->
      <div class="doctor-content-view">
        <a-avatar :size="80">
          <img
            alt="avatar"
            :src="avatarPng"
          />
        </a-avatar>
        <div class="text-info">
          <div class="welcome-text">上午好，{{ doctorName }}主治医师</div>
          <div class="organ-text">{{ organName }}</div>
        </div>
      </div>
      <!-- 展示医生的待办信息 -->
      
    </div>
    <!-- 展示快捷入口 以及其他功能模块 -->
    <div class="workbench-info">
      <!-- 快捷入口的信息 -->
      <div class="quick-access-container">
        <quick-access />
      </div>
      <!-- 左下第一个暂未开放 -->
      <div class="wait-one"></div>
      <!-- 右侧第一个暂未开放 -->
      <div class="wait-two"></div>
      <!-- 右下第二个暂未开放 -->
      <div class="wait-three"></div>
    </div>
  </div>
  <!-- <div class="workbench-view">
    <PageContainer class="doctor-info-view">
      <div class="doctor-content-view">
        <a-avatar :size="64">
          <img
            alt="avatar"
            src="https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/3ee5f13fb09879ecb5185e440cef6eb9.png~tplv-uwbnlip3yd-webp.webp"
          />
        </a-avatar>
        <div class="text-info">
          <div class="welcome-text">上午好，{{ doctorName }}主治医师</div>
          <div>{{ organName }}</div>
        </div>
      </div>
    </PageContainer>
    <PageContainer class="task-conainer">
      <div class="task-content">
        <a-tabs
          v-model:active-key="pageState.tabKey"
          destroy-on-hide
          @change="handTabChange"
        >
          <a-tab-pane :key="0" title="待办">
            <div class="backlog-view">
              <div
                v-for="key in backlogMap.keys()"
                :key="key"
                class="backlog-card"
                @click="() => handOpenDetails(key)"
              >
                <img
                  v-if="findCount(backlogMap.get(key)?.flieName as string)?.isUrgent"
                  class="tipImg"
                  :src="backlogMap.get(key)?.tipIcon"
                />
                <span class="text-info">
                  <img :src="backlogMap.get(key)?.icon" alt="" />
                  <span>{{ backlogMap.get(key)?.title }}</span>
                </span>
                <span>{{
                  findCount(backlogMap.get(key)?.flieName as string)?.count ?? 0
                }}</span>
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane :key="1" title="已处理">
            <ManageAffairs />
          </a-tab-pane>
        </a-tabs>
      </div>
    </PageContainer>
    <PageContainer class="quick-container">
      <quick-access></quick-access>
    </PageContainer>
    <PageContainer class="lock2-view">
      <div class="lock2-content">
      </div>
    </PageContainer>
    <div class="lock-view-container">
      <div class="lock1-content">
      </div>
      <div class="lock3-content">
      </div>
    </div>
    <a-drawer
      :title="title"
      :width="980"
      :visible="pageState.visible"
      :footer="false"
      unmount-on-close
      @cancel="
        () => {
          pageState.visible = false;
          getCallCount();
        }
      "
    >
      <component
        :is="componentMap.get(pageState.compkey)"
        :refresh-func="getCallCount"
      ></component>
    </a-drawer>
  </div> -->
</template>

<style lang="scss" scoped>
.main-container {
  // 展示医生的信息
  >.doctor-container {
    padding: var(--spacing-8) var(--spacing-9);
    border-radius: var(--border-radius-large);
    background-image: linear-gradient(180deg, #E5EFFF 0%, #FFFFFF 81%);;
    .doctor-content-view {
      display: flex;
      gap: var(--spacing-8);
      .text-info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: var(--spacing-4);
        font-size: var(--font-size-body-4);
        color: var(--text-sub-color);
        // 欢迎信息
        .welcome-text {
          color: var(--color-black);
          font-size: var(--font-size-title-3);
          font-weight: var(--font-weight-700);
        }
        // 机构信息
        .organ-text {
          margin-top: var(--spacing-8);
        }
      }
    }
  }
  // 展示工作台的信息
  >.workbench-info {
    display: grid;
    gap: var(--spacing-7);
    >.quick-access-container {
      padding: var(--spacing-7) var(--spacing-8);
      border-radius: var(--border-radius-large);
      background-color: white;
    }
    >.wait {
      // 敬请期待
      &-one, &-two, &-three {
        background-image: url('');
      }
    }
  }
}
// .workbench-view {
//   height: 100%;
//   overflow: auto;
//   display: grid;
//   gap: var(--spacing-7);
//   grid-template-columns: minmax(100px, 1fr) minmax(700px, auto);
//   grid-template-rows: 96px minmax(320px, 1fr) 172px minmax(360px, 1.1fr);
//   grid-template-areas:
//     "doctor lock1"
//     "task  lock1"
//     "quick lock1"
//     "lock2 lock1";

//   .doctor-info-view {
//     grid-area: doctor;
//     height: 96px;
//     .doctor-content-view {
//       display: flex;
//       gap: var(--spacing-7);

//       .text-info {
//         display: flex;
//         flex-direction: column;
//         justify-content: center;
//         gap: var(--spacing-4);
//         font-size: var(--font-size-body-3);
//         color: var(--text-sub-color);
//         .welcome-text {
//           color: var(--color-black);
//           font-size: var(--font-size-body-4);
//           font-weight: var(--font-weight-700);
//         }
//       }
//     }
//   }

//   .task-conainer {
//     grid-area: task;
//     height: 100%;
//     display: flex;
//     flex-direction: column;
//     padding: 0px;
//     padding-bottom: var(--spacing-7);
//     .task-content {
//       height: 100%;
//       :deep(.arco-tabs) {
//         height: 100%;
//         overflow: hidden;
//       }

//       :deep(.arco-tabs-content) {
//         height: calc(100% - 48px);
//         overflow: hidden;
//       }

//       :deep(.arco-tabs-content .arco-tabs-content-list) {
//         height: 100%;
//       }

//       :deep(.arco-tabs-tab-active) {
//         font-weight: var(--font-weight-700);
//       }
//       :deep(.arco-tabs-nav-type-line .arco-tabs-tab) {
//         padding-top: var(--spacing-7);
//       }
//       :deep(.arco-tabs-pane) {
//         padding: 0px var(--spacing-7);
//         height: 100%;
//       }

//       .backlog-view {
//         display: flex;
//         gap: var(--spacing-7);

//         .backlog-card {
//           position: relative;
//           width: 182px;
//           height: 58px;
//           padding: 0 var(--spacing-7);
//           display: flex;
//           align-items: center;
//           justify-content: space-between;
//           border-radius: var(--border-radius-medium);
//           background: #f3f5f7;
//           cursor: pointer;
//           font-size: var(--font-size-body-4);
//           font-weight: var(--font-weight-700);
//           color: var(--color-black);

//           .tipImg {
//             position: absolute;
//             top: 0;
//             left: 0;
//           }

//           .text-info {
//             display: flex;
//             align-items: center;
//             font-size: var(--font-size-body-3);
//             color: var(--text-sub-color);

//             img {
//               width: 16px;
//               height: 16px;
//               margin-right: var(--spacing-4);
//             }
//           }
//         }
//       }
//     }
//   }

//   .quick-container {
//     grid-area: quick;
//   }

//   .lock-view-container {
//     grid-area: lock1;
//     display: grid;
//     grid-template-rows: 52.1fr 45.3fr;
//     gap: 16px;
//     .lock1-content {
//       background-image: url("@/assets/images/lock1.png");
//       background-size: 100% 100%;
//       height: 100%;
//       display: flex;
//       align-items: center;
//       border-radius: 4px;
//     }
//     .lock3-content {
//       background-image: url("@/assets/images/lock3.png");
//       background-size: 100% 100%;
//       height: 100%;
//       display: flex;
//       align-items: center;
//       border-radius: 4px;
//     }
//   }

//   .lock1-view {
//     grid-area: lock1;
//     .lock1-content {
//       background-image: url("@/assets/images/lock1.png");
//       background-size: cover;
//       height: 100%;
//       display: flex;
//       align-items: center;
//     } 
//   }
//   .lock2-view {
//     grid-area: lock2;
//     .lock2-content {
//       background-image: url("@/assets/images/lock2.png");
//       background-size: cover;
//       height: 100%;
//       display: flex;
//       align-items: center;
//     }
//   }
//   .lock3-view {
//     grid-area: lock1;
//     .lock3-content {
//       background-image: url("@/assets/images/lock3.png");
//       background-size: cover;
//       height: 100%;
//       display: flex;
//       align-items: center;
//     }
//   }
// }
</style>
