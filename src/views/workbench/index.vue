<!--
* @description 工作台
* @fileName index.vue
* <AUTHOR>
* @date 2024/11/19 17:49:46
!-->
<script setup lang="ts">
import { reactive, computed, ref, onMounted, nextTick } from "vue";
import PageContainer from "@/components/page-container/index.vue";
import * as apis from "@/batchApi";
import { useUserStore } from "@/store";
import { getImageSrc } from "@/utils";
import {
  backlogMap,
  BacklogType,
  BacklogTypeEnum,
  componentMap,
} from "./config";
import ManageAffairs from "./components/manage-affairs.vue";
import quickAccess from "./components/quick-access.vue";

type List = apis.GetPatCallListUsingPOST_1CallListRespVo;
type IState = {
  visible: boolean;
  compkey: BacklogTypeEnum;
  tabKey: number | string;
  data: List[];
  countList: apis.PatCallCountUsingPOSTCallCountRespVo[];
  teamFinishCount: number;
  signedRenewalCount: number;
  syncErrorCount: number;
  allCount: apis.TodoStatisticsUsingPOSTTypeCountDto[];
  title: string;
  articleType: number | undefined; // 文书类型
};

const userStore = useUserStore();

const pageState = reactive<IState>({
  visible: false,
  compkey: BacklogTypeEnum.doctorCallBack,
  tabKey: 0,
  data: [],
  countList: [],
  teamFinishCount: 0,
  signedRenewalCount: 0,
  syncErrorCount: 0,
  allCount: [],
  title: "",
  articleType: undefined,
});

const isExpanded = ref(false);
const showExpandButton = ref(false);
const todoViewRef = ref<HTMLElement | null>(null);

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

// 检测内容是否超过两行高度
const checkContentHeight = () => {
  if (todoViewRef.value) {
    const cardHeight = 84; // 固定卡片高度
    const rowGap = 24;
    const twoRowsHeight = cardHeight * 2 + rowGap; // 默认显示两行

    const contentHeight = todoViewRef.value.scrollHeight;
    // 如果内容高度大于两行高度，则显示展开按钮
    showExpandButton.value = contentHeight > twoRowsHeight + 2; // 添加2px容错
  }
};

// 监听窗口大小变化
const setupResizeObserver = () => {
  if (typeof window !== "undefined") {
    const resizeObserver = new ResizeObserver(() => {
      checkContentHeight();
    });

    if (todoViewRef.value) {
      resizeObserver.observe(todoViewRef.value);
    }

    // 监听窗口大小变化
    window.addEventListener("resize", checkContentHeight);

    return () => {
      resizeObserver.disconnect();
      window.removeEventListener("resize", checkContentHeight);
    };
  }
};

onMounted(async () => {
  await nextTick();
  checkContentHeight();
  setupResizeObserver();
});

const doctorName = computed(() => {
  return userStore.accountInfo.name
  // const userInfo = localStorage.getItem("user_info");
  // const userInfoData = userInfo ? JSON.parse(userInfo) : {};
  // return userInfoData?.accountInfo?.name;
});

// const organName = computed(() => {
//   const userInfo = localStorage.getItem("user_info");
//   const userInfoData = userInfo ? JSON.parse(userInfo) : {};
//   return userInfoData?.accountInfo?.organName;
// });

const teamName = computed(() => {
  return userStore.accountInfo.teamName
});

// 提示信息
const tipHello = computed(() => {
  // 计算提示信息
  const now = new Date();
  if (now.getHours() >= 0 && now.getHours() < 6) {
    return "凌晨好";
  } 
  if (now.getHours() >= 6 && now.getHours() < 9) {
    return "早上好";
  } 
  if (now.getHours() >= 9 && now.getHours() < 12) {
    return "上午好";
  } 
  if (now.getHours() >= 12 && now.getHours() < 14) {
    return "中午好";
  } 
  if (now.getHours() >= 14 && now.getHours() < 18) {
    return "下午好";
  }
  return "晚上好";
})


const getCallCount = async () => {
  try {
    // 获取家医回电的统计数据
    const { data } = await apis.postBdManageApiCallPatCallCount({ type: 1 });
    pageState.countList = data as apis.PatCallCountUsingPOSTCallCountRespVo[];
    // 获取随访的统计数据
    const { data: followTeamCount } =
      await apis.postBdManageApiFollowupGetBdFollowCount({
        teamId: userStore.accountInfo.teamId as unknown as number,
      });
    pageState.teamFinishCount = followTeamCount?.followTotal ?? 0;
    // 获取签约的统计数据
    const { data: signedRenewalCount } =
      await apis.postBdManageApiSignGetSignedRenewalCount({
        teamId: userStore.accountInfo.teamId as unknown as number,
      });
    pageState.signedRenewalCount = signedRenewalCount?.count ?? 0;

    const { data: syncErrorCount } = await apis.postBdManageApiIhAbnormalCount({
      teamId: userStore.accountInfo.teamId as unknown as number,
    });
    pageState.syncErrorCount = syncErrorCount ?? 0;
    const { data: allCount } = await apis.postBdManageApiTodoTodoStatistics({});
    pageState.allCount = allCount as IState["allCount"];

    // 数据加载完成后再次检查是否需要显示展开按钮
    nextTick(() => {
      checkContentHeight();
    });
  } catch (error) {
    console.log("===>", error);
  }
};

const findCount = (key: string, articleType?: number) => {
  // eslint-disable-next-line no-prototype-builtins
  if (articleType) {
    const record = pageState.allCount?.find(
      item => item?.articleType === articleType,
    );
    return { count: record?.count };
  }
  if (
    // 当前是随访类型的时候 返回当前的团队完成数量
    key === "sf"
  ) {
    return {
      count: pageState.teamFinishCount,
    };
  }
  if (key === "qydq") {
    return {
      count: pageState.signedRenewalCount,
    };
  }
  if (key === "khjd") {
    return {
      count: pageState.syncErrorCount,
    };
  }
  const record = pageState.countList?.find(item => item.key === key);
  return record;
};

const handOpenDetails = (key: BacklogTypeEnum) => {
  pageState.title = backlogMap.get(key)?.title as string;
  pageState.articleType = backlogMap.get(key)?.articleType;
  pageState.compkey = key;
  pageState.visible = true;
};

const handTabChange = (key: number | string) => {
  pageState.tabKey = key;
};

getCallCount();
</script>

<template>
  <div class="workbench-view">
    <PageContainer class="doctor-info-view">
      <div class="doctor-content-view">
        <a-avatar :size="64">
          <img
            alt="avatar"
            src="https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/3ee5f13fb09879ecb5185e440cef6eb9.png~tplv-uwbnlip3yd-webp.webp"
          />
        </a-avatar>
        <div class="text-info">
          <div class="welcome-text">{{ tipHello }}，{{ doctorName }}主治医师</div>
          <div>{{ teamName }}</div>
        </div>
      </div>

      <div class="todo-section">
        <div class="todo-header">
          <div class="todo-title">待办事项</div>
        </div>

        <div class="todoView-container" :class="{ expanded: isExpanded }">
          <div
            ref="todoViewRef"
            class="todoView"
            :class="{ 'todo-expanded': isExpanded }"
          >
            <div
              v-for="key in backlogMap.keys()"
              :key="key"
              class="cardView"
              @click="handOpenDetails(key)"
            >
              <img :src="getImageSrc(backlogMap.get(key)?.icon as string)" />
              <div class="info">
                <div>{{ backlogMap.get(key)?.title }}</div>
                <div class="count">
                  <span>{{
                    findCount(
                      backlogMap.get(key)?.flieName as string,
                      backlogMap.get(key)?.articleType as number,
                    )?.count ?? 0
                  }}</span>
                  <icon-right style="color: rgba(0, 0, 0, 0.3)" :size="18" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="showExpandButton" class="expand-button-container">
          <a-button type="text" class="expand-button" @click="toggleExpand">
            <template #icon>
              <icon-up v-if="isExpanded" />
              <icon-down v-else />
            </template>
            {{ isExpanded ? "收起" : "展开更多" }}
          </a-button>
        </div>
      </div>
    </PageContainer>

    <PageContainer class="quick-container">
      <quick-access></quick-access>
    </PageContainer>
    <PageContainer class="lock2-view">
      <div class="lock2-content"></div>
    </PageContainer>
    <div class="lock-view-container">
      <div class="lock1-content"></div>
      <div class="lock3-content"></div>
    </div>
    <a-drawer
      :title="`待办-${pageState.title}`"
      :width="1200"
      :visible="pageState.visible"
      :footer="false"
      unmount-on-close
      @cancel="
        () => {
          pageState.visible = false;
          getCallCount();
        }
      "
    >
      <component
        :is="componentMap.get(pageState.compkey)"
        :refresh-func="getCallCount"
        :article-type="pageState.articleType"
      ></component>
    </a-drawer>
  </div>
</template>

<style lang="scss" scoped>
.workbench-view {
  height: 100%;
  overflow: auto;
  display: grid;
  gap: var(--spacing-7);
  grid-template-columns: minmax(100px, 1fr) minmax(700px, auto);
  grid-template-rows: auto 172px minmax(360px, 1.1fr);
  grid-template-areas:
    "doctor doctor"
    "quick lock1"
    "lock2 lock1";

  .doctor-info-view {
    grid-area: doctor;
    background: linear-gradient(180deg, #e5efff 0%, #ffffff 81%);
    padding-bottom: 16px;
    transition: all 0.3s ease;

    .doctor-content-view {
      display: flex;
      gap: var(--spacing-7);
      margin-bottom: 24px;

      .text-info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: var(--spacing-4);
        font-size: var(--font-size-body-3);
        color: var(--text-sub-color);
        .welcome-text {
          color: var(--color-black);
          font-size: var(--font-size-body-4);
          font-weight: var(--font-weight-700);
        }
      }
    }

    .todo-section {
      display: flex;
      flex-direction: column;

      .todo-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;

        .todo-title {
          font-size: 16px;
          font-weight: 600;
          color: var(--color-black);
        }
      }

      .todoView-container {
        overflow: hidden;
        height: auto;
        max-height: 200px; /* 精确设置为两行卡片高度 + 间距 */
        transition: max-height 0.5s cubic-bezier(0.4, 0, 0.2, 1);

        &.expanded {
          max-height: 1000px; /* 足够大的高度以容纳所有内容 */
        }
      }

      .todoView {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(235px, 235px));
        gap: 16px;
        grid-auto-rows: 84px; /* 固定每行高度 */
        padding-bottom: 16px;

        .cardView {
          width: 235px;
          height: 84px; /* 固定卡片高度 */
          display: flex;
          padding: 12px;
          border-radius: 8px;
          background: #fff;
          box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.05);
          cursor: pointer;
          overflow: hidden;

          .info {
            width: 100%;
            font-size: 16px;
            font-weight: 500;
            color: #767676;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            .count {
              display: flex;
              justify-content: space-between;
              align-items: center;
              font-size: 24px;
              font-weight: bold;
              line-height: 24px;
              color: #000;
              margin-top: 8px;
            }
          }
          > img {
            width: 48px;
            height: 48px;
            margin-right: 12px;
            flex-shrink: 0;
          }
        }
      }

      .expand-button-container {
        display: flex;
        justify-content: center;

        .expand-button {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #2253e6;
        }
      }
    }
  }

  .quick-container {
    grid-area: quick;
  }

  .lock-view-container {
    grid-area: lock1;
    display: grid;
    grid-template-rows: 52.1fr 45.3fr;
    gap: 16px;
    .lock1-content {
      background-image: url("@/assets/images/lock2.png");
      background-size: cover;
      height: 100%;
      display: flex;
      align-items: center;
      border-radius: 4px;
    }
    .lock3-content {
      background-image: url("@/assets/images/lock1.png");
      background-size: contain;
      height: 100%;
      display: flex;
      align-items: center;
      border-radius: 4px;
    }
  }

  .lock2-view {
    grid-area: lock2;
    .lock2-content {
      background-image: url("@/assets/images/lock3.png");
      background-size: cover;
      height: 100%;
      display: flex;
      align-items: center;
    }
  }
}
</style>
