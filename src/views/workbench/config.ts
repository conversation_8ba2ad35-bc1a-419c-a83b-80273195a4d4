import phoneImg from "@/assets/images/phone.png";
import parkingLotImg from "@/assets/images/parkingLot.png";
import urgencyImg from "@/assets/images/urgency.png";
import sfPng from "@/assets/images/sf.png";
import qydqPng from "@/assets/images/qydq.png";
import { defineAsyncComponent } from "vue";

export enum BacklogTypeEnum {
  /* 家医回电 */
  doctorCallBack = 1,
  /* 车位预留 */
  parkingSpace = 2,
  /** 随访列表 */
  followUp = 3,
  /** 签约到期 */
  contractExpiration = 4,
  /** 客户建档异常 */
  customerRecordError = 5,
  /* 线下首诊 */
  offlineFirstVisit = 6,
  /* 问诊小结 */
  consultation = 7,
  /* 体检方案建议书 */
  physicalExamination = 8,
  /* 体检报告解读 */
  physicalReport = 9,
  /* 营养建议书 */
  nutritionRecommendation = 10,
  /* 家医来信 */
  doctorLetter = 11,
  /* 随访小姐 */
  FollowUpSummary = 12,
   /* 健康建议书 */
  healthRecommendation = 13,
  /* 专家咨询 */
  ExpertConsultation = 14,
  /* 工单 */
  WorkOrder = 15,
}

/* 待办类型 */
export enum ToDoType {
  /* 文书待办 */
  document = 1,
  /* 车位待办 */
  parkingLot = 2,
  /* 首诊待办 */
  firstVisit = 3,
}
/** 类型 1 家医回电 2车位预留 */
export type BacklogType =
  | BacklogTypeEnum.doctorCallBack
  | BacklogTypeEnum.parkingSpace
  | BacklogTypeEnum.followUp
  | BacklogTypeEnum.contractExpiration
  | BacklogTypeEnum.customerRecordError
  | BacklogTypeEnum.offlineFirstVisit
  | BacklogTypeEnum.consultation
  | BacklogTypeEnum.physicalExamination
  | BacklogTypeEnum.physicalReport
  | BacklogTypeEnum.nutritionRecommendation
  | BacklogTypeEnum.doctorLetter
  | BacklogTypeEnum.FollowUpSummary
  | BacklogTypeEnum.ExpertConsultation
  | BacklogTypeEnum.WorkOrder

export const componentMap = new Map<any, any>([
  [
    BacklogTypeEnum.doctorCallBack,
    defineAsyncComponent(() => import("./components/doctor-callback.vue")),
  ],
  [
    BacklogTypeEnum.parkingSpace,
    defineAsyncComponent(() => import("./components/parking-space.vue")),
  ],
  [
    BacklogTypeEnum.followUp,
    defineAsyncComponent(() => import("./components/follow-up-visit.vue")),
  ],
  [
    BacklogTypeEnum.contractExpiration,
    defineAsyncComponent(() => import("./components/contract-expiration.vue")),
  ],
  // 客户建档异常
  [
    BacklogTypeEnum.customerRecordError,
    defineAsyncComponent(
      () => import("./components/customer-record-error.vue"),
    ),
  ],
  [
    BacklogTypeEnum.offlineFirstVisit,
    defineAsyncComponent(() => import("./components/offline-first-visit.vue")),
  ],
  [
    BacklogTypeEnum.healthRecommendation,
    defineAsyncComponent(() => import("./components/document.vue")),
  ],
  [
    BacklogTypeEnum.consultation,
    defineAsyncComponent(() => import("./components/document.vue")),
  ],
  [
    BacklogTypeEnum.physicalExamination,
    defineAsyncComponent(() => import("./components/document.vue")),
  ],
  [
    BacklogTypeEnum.physicalReport,
    defineAsyncComponent(() => import("./components/document.vue")),
  ],
  [
    BacklogTypeEnum.nutritionRecommendation,
    defineAsyncComponent(() => import("./components/document.vue")),
  ],
  [
    BacklogTypeEnum.doctorLetter,
    defineAsyncComponent(() => import("./components/document.vue")),
  ],
  [
    BacklogTypeEnum.FollowUpSummary,
    defineAsyncComponent(() => import("./components/document.vue")),
  ],
  [
    BacklogTypeEnum.ExpertConsultation,
    defineAsyncComponent(() => import("./components/expert-consultation.vue")),
  ],
  [
    BacklogTypeEnum.WorkOrder,
    defineAsyncComponent(() => import("./components/work-order/index.vue")),
  ],
]);

// 展示待办中的功能模块
export const backlogMap = new Map([
  [
    BacklogTypeEnum.doctorCallBack,
    {
      title: "家医回电",
      icon: "todo/jyhx.png",
      tipIcon: urgencyImg,
      flieName: "JYHD",
    },
  ],
  [
    BacklogTypeEnum.parkingSpace,
    { title: "预留车位", icon: "todo/cwyy.png", flieName: "车位待办",articleType: -1 },
  ],
  // 待完成随访的
  [
    BacklogTypeEnum.followUp,
    {
      title: "随访",
      icon: "todo/jbsf.png",
      flieName: "sf",
    },
  ],
  // 签约到期的列表
  [
    BacklogTypeEnum.contractExpiration,
    {
      title: "签约到期",
      icon: "todo/qydq.png",
      flieName: "qydq",
    },
  ],
  // 客户建档异常
  [
    BacklogTypeEnum.customerRecordError,
    {
      title: "客户建档异常",
      icon: "todo/khjd.png",
      flieName: "khjd",
    },
  ],
  // 线下首诊
  [
    BacklogTypeEnum.offlineFirstVisit,
    {
      title: "线下首诊",
      icon: "todo/xxsz.png",
      flieName: "线下首诊",
      articleType: -2
    },
  ],
  // 健康建议书
  [
    BacklogTypeEnum.healthRecommendation,
    {
      title: "健康建议书",
      icon: "todo/yyjy.png",
      flieName: "健康建议书",
      articleType: 1
    },
  ],
  // 问诊小结
  [
    BacklogTypeEnum.consultation,
    {
      title: "问诊小结",
      icon: "todo/wzxj.png",
      flieName: "问诊小结",
      articleType: 2
    },
  ],
  // 体检方案建议书
  [
    BacklogTypeEnum.physicalExamination,
    {
      title: "体检方案建议书",
      icon: "todo/tjfa.png",
      flieName: "体检方案建议书",
      articleType: 3
    },
  ],
  // 体检报告解读
  [
    BacklogTypeEnum.physicalReport,
    {
      title: "体检报告解读",
      icon: "todo/tjbg.png",
      flieName: "提交报告解读建议",
      articleType: 4
    },
  ],
  // 营养建议书
  [
    BacklogTypeEnum.nutritionRecommendation,
    {
      title: "营养建议书",
      icon: "todo/yyjy.png",
      flieName: "营养建议书",
      articleType: 5
    },
  ],
  // 家医来信
  [
    BacklogTypeEnum.doctorLetter,
    {
      title: "家医来信",
      icon: "todo/jylx.png",
      flieName: "家医来信",
      articleType: 6
    },
  ],
  // 随访小结
  [
    BacklogTypeEnum.FollowUpSummary,
    {
      title: "随访小结",
      icon: "todo/sfxj.png",
      flieName: "随访小结",
      articleType: 7
    },
  ],
   // 专家咨询
  [
    BacklogTypeEnum.ExpertConsultation,
    {
      title: "专家咨询",
      icon: "todo/sfxj.png",
      flieName: "专家咨询",
    },
  ],
  // 工单
  [
    BacklogTypeEnum.WorkOrder,
    {
      title: "工单",
      icon: "todo/workorder.png",
      flieName: "工单",
      articleType: -3
    },
  ],
]);


export const ProcessedTypeInfo = {
  1: {
    title: "家医回电",
    content: {
      客户信息: ["patientName", "sex", "contactMobile"],
      发起时间: "startTime",
      任务完成时间: "handleTime",
      处理人: "handleUser",
      需求: "callContent",
    },
  },
  // 2: {
  //   title: "预留车位",
  //   content: {
  //     客户信息: ["name", "sex", "phone"],
  //     要求回电时间: "time",
  //     任务完成时间: "time1",
  //     处理人: "people",
  //   },
  // },
};

/* 家医回电 */
export const doctorCallBackColumns = [
  {
    title: "序号",
    dataIndex: "name",
  },
  {
    title: "患者姓名",
    slotName: "name",
  },
  {
    title: "性别",
    dataIndex: "name",
  },
  {
    title: "年龄",
    dataIndex: "name",
  },
  {
    title: "联系方式",
    dataIndex: "name",
  },
  {
    title: "发起时间",
    dataIndex: "name",
  },
  {
    title: "等待时长",
    dataIndex: "name",
  },
  {
    title: "操作",
    slotName: "optional",
  },
];

/* 预留车位 */
export const parkingSpaceColumns = [
  {
    title: "序号",
    dataIndex: "name",
  },
  {
    title: "患者姓名",
    slotName: "name",
  },
  {
    title: "性别",
    dataIndex: "name",
  },
  {
    title: "年龄",
    dataIndex: "name",
  },
  {
    title: "联系方式",
    dataIndex: "name",
  },
  {
    title: "就诊机构",
    dataIndex: "name",
  },
  {
    title: "就诊时间",
    dataIndex: "name",
  },
  {
    title: "输入车位",
    dataIndex: "name",
  },
  {
    title: "操作",
    slotName: "optional",
  },
];
