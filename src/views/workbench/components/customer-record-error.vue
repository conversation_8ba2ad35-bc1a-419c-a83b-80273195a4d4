<!--
 * @Description: 签约到期
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-03-31 15:55:13
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-05-30 22:29:37
-->
<template>
  <a-table
    :data="tableData"
    :pagination="paginationInfo"
    @page-change="handlePageChange"
    @page-size-change="handlePageSizeChange"
  >
    <template #tr="{ record }">
      <tr class="my-tr" @dblclick.prevent="handlePatientInfo(record)" />
    </template>
    <!-- 定义表格的列 -->
    <template #columns>
      <a-table-column
        title="序号"
        :width="65"
      >
        <template #cell="{ rowIndex }">
          <span>{{ rowIndex + 1 }}</span>
        </template>
      </a-table-column>
      <a-table-column
        title="患者姓名"
        data-index="patientName"
        :min-width="150"
      >
        <template #cell="{ record }">
          <user-info :record="{ name: record.patientName }" />
        </template>
      </a-table-column>
      <a-table-column
        title="性别"
        data-index="gender"
      >
        <template #cell="{ record }">
          <span>{{ record.gender === "1" ? "男" : "女" }}</span>
        </template>
      </a-table-column>
      <a-table-column
        title="年龄"
        data-index="age"
      ></a-table-column>
      <a-table-column
        title="联系方式"
        data-index="contactMobile"
      ></a-table-column>
      <a-table-column
        title="异常场景"
        data-index="scene"
      ></a-table-column>
      <a-table-column title="操作">
        <template #cell="{ record }">
          <div class="table-operate">
            <a-button
              size="mini"
              type="outline"
              :loading="loading"
              @click="handleDispose(record, true)"
            >
              处理
            </a-button>
            <a-button
              size="mini"
              type="outline"
              status="danger"
              :loading="loading"
              @click="handleDispose(record, false)"
            >
              不处理
            </a-button>
          </div>
        </template>
      </a-table-column>
    </template>
  </a-table>
</template>

<script setup lang="ts">
import userInfo from '@/components/bd-table/components/user-info.vue';
import { useAppStore, useUserStore } from '@/store';
import { onMounted, reactive, ref } from 'vue';
import { GetSignContractListUsingPOSTHxPagePatientRenewalListResVo, GetSignContractListUsingPOSTPatientRenewalListResVo, postBdManageApiIhAbnormalList, postBdManageApiIhChangeAbnormalStatus } from '@/batchApi';

const appStore = useAppStore()
const userStore = useUserStore();
const loading = ref(false);

const paginationInfo = reactive({
  current: 1,
  total: 0,
  pageSize: 15,
  showTotal: true
})

const handlePageChange = (
  page: number
) => {
  console.log('page', page)
  paginationInfo.current = page;
  init();
}

const handlePageSizeChange = (
  pageSize: number
) => {
  paginationInfo.pageSize = pageSize;
  init();
  console.log('pageSize', pageSize)
}


const tableData = ref<GetSignContractListUsingPOSTHxPagePatientRenewalListResVo['content']>([]);

const handleDispose = (
  _record: GetSignContractListUsingPOSTPatientRenewalListResVo,
  _isDispose: boolean
) => {
  loading.value = true;
  postBdManageApiIhChangeAbnormalStatus({
    syncRecordId: _record?.syncRecordId,
    status: _isDispose ? 1 : 2 
  }).then(_res => {
    loading.value = false;
    init();
    console.log('_res', _res)
  })
  console.log('_record_isDispose', _record, _isDispose)
}

// // 点击去随访
// const handleToFollowUp = (
//   _record: any
// ) => {
//   // TODO: 展示对应的模块的信息
//   appStore.openPatientCenter(_record)
//   patientCenterStore.changeSecondaryMenu(true, '随访', [
//     {
//       name: '随访列表',
//       component: markRaw(followUpIndex),
//       componentParams: {
//         patientId: _record.patientId
//       }
//     }
//   ]);
//   console.log('_record', _record)
// }

// 点击展示患者的信息
const handlePatientInfo = (
  _record: any
) => {
  appStore.openPatientCenter(_record)
  console.log('_record', _record)
}

const init = () => {
  postBdManageApiIhAbnormalList({
    pageNum: paginationInfo.current,
    pageSize: paginationInfo.pageSize,
    query: {
      teamId: userStore.accountInfo.teamId as unknown as number
    }
  }).then(_res => {
    if (
      Array.isArray(_res.data?.content)
    ) {
      tableData.value = _res.data?.content
    } else {
      tableData.value = []
    }
    paginationInfo.total = _res.data?.total ?? 0;
  })
}

onMounted(() => {
  init()
})

</script>

<style lang="scss" scoped>
.table-operate {
  display: flex;
  align-items: center;
  justify-content: start;
  column-gap: var(--spacing-4);
}
</style>