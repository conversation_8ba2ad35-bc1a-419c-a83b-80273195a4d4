<!--
* @description 预留车位
* @fileName parkingSpace.vue
* <AUTHOR>
* @date 2024/11/20 17:09:07
!-->
<script setup lang="ts">
import { reactive } from "vue";
import * as apis from "@/batchApi";
import { to } from "await-to-js";
import { Message } from "@arco-design/web-vue";
import { done } from "nprogress";
import dayjs from "dayjs";
import { ToDoType } from "../config";
import ParkingSpaceItem from "./parking-space-item.vue";

interface State {
  loading: boolean;
  confirmLoading: boolean;
  visible: boolean;
  activeKey: number;
  searchValue: string | undefined;
  total: number;
  pagination: { pageNum: number; pageSize: number };
  list: apis.PageUsingPOSTToDo[];
  xId: undefined | number;
}

const defaultPagination = { pageNum: 1, pageSize: 10 };
const pageState = reactive<State>({
  loading: false,
  confirmLoading: false,
  visible: false,
  activeKey: 0,
  searchValue: undefined,
  total: 30,
  pagination: defaultPagination,
  list: [],
  xId: undefined,
});

const form = reactive({
  carport: "",
});

const getToDoList = async () => {
  pageState.loading = true;
  const [err, res] = await to(
    apis.postBdManageApiTodoPage({
      pageNum: pageState.pagination.pageNum,
      pageSize: pageState.pagination.pageSize,
      query: {
        keyword: pageState.searchValue,
        status: pageState.activeKey,
        type: ToDoType.parkingLot,
      },
    }),
  );
  pageState.loading = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    pageState.list = res?.data?.content as apis.PageUsingPOSTToDo[];
    pageState.total = res?.data?.total as number;
  }
};

const handleTbasChange = (key: number | string) => {
  pageState.activeKey = key as number;
  pageState.searchValue = undefined;
  pageState.pagination.pageNum = 1;
  getToDoList();
};

const handlePageChange = (current: number) => {
  pageState.pagination.pageNum = current;
  getToDoList();
};

const handlePageSizeChange = (pageSize: number) => {
  pageState.pagination.pageSize = pageSize;
  getToDoList();
};

const handleConfirmParkingSpace = async (
  value: string,
  xid: number | undefined,
) => {
  if (!value) return Message.info("请输入车位");
  pageState.confirmLoading = true;
  const [err, res] = await to(
    apis.postBdManageApiTodoCarportUpdate({
      carport: value,
      id: xid,
    }),
  );
  pageState.confirmLoading = false;
  if (err) {
    throw err;
  }
  if (res.code === "1") {
    Message.success("成功");
    getToDoList();
  }
};

const handleWithdraw = async (xId: number) => {
  const [err, res] = await to(
    apis.getBdManageApiTodoCarportCancel({ id: xId }),
  );
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    Message.success("撤回成功");
    getToDoList();
  }
};

const handleEdit = (record: apis.PageUsingPOSTToDo) => {
  pageState.visible = true;
  pageState.xId = record?.xid;
  form.carport = record.carCarport as string;
};

const handleCancel = () => {
  pageState.visible = false;
  pageState.xId = undefined;
};

const handleBeforeOk = async () => {
  await handleConfirmParkingSpace(form.carport, pageState.xId).then(() => {
    done();
  });
};

getToDoList();
</script>

<template>
  <a-spin style="width: 100%" :loading="pageState.loading" tip="加载中...">
    <a-tabs v-model="pageState.activeKey" @change="handleTbasChange">
      <template #extra>
        <a-input-search
          v-model="pageState.searchValue"
          :style="{ width: '100%' }"
          placeholder="请输入姓名/手机号"
          search-button
          @search="getToDoList"
          @press-enter="getToDoList"
        />
      </template>
      <a-tab-pane key="0" title="待处理">
        <a-empty v-if="!pageState.list?.length" />
        <div v-else>
          <ParkingSpaceItem
            v-for="item in pageState.list"
            :key="item?.xid"
            :data="item"
          >
            <template #operation>
              <a-input-search
                :style="{ width: '268px' }"
                placeholder="请输入车位"
                search-button
                :loading="pageState.confirmLoading"
                @search="value => handleConfirmParkingSpace(value, item?.xid)"
              >
                <template #button-default>确定车位</template>
              </a-input-search>
            </template>
          </ParkingSpaceItem>
          <div class="paginationView">
            <a-pagination
              :total="pageState.total"
              :page-size="pageState.pagination.pageSize"
              :current="pageState.pagination.pageNum"
              size="medium"
              show-total
              show-jumper
              show-page-size
              @change="handlePageChange"
              @page-size-change="handlePageSizeChange"
            />
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="1" title="已处理">
        <div>
          <ParkingSpaceItem
            v-for="item in pageState.list"
            :key="item?.xid"
            :data="item"
            :is-processed="true"
          >
            <template #operation>
              <a-space>
                <a-button
                  type="outline"
                  @click="handleWithdraw(item?.xid as number)"
                  >撤回</a-button
                >
                <a-button type="outline" @click="handleEdit(item)"
                  >变更</a-button
                >
              </a-space>
            </template>
          </ParkingSpaceItem>
          <div class="paginationView">
            <a-pagination
              :total="pageState.total"
              :page-size="pageState.pagination.pageSize"
              :current="pageState.pagination.pageNum"
              size="medium"
              show-total
              show-jumper
              show-page-size
              @change="handlePageChange"
              @page-size-change="handlePageSizeChange"
            />
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
    <a-modal
      v-model:visible="pageState.visible"
      title="变更车位"
      @cancel="handleCancel"
      @before-ok="handleBeforeOk"
    >
      <a-form
        :model="form"
        :label-col-props="{ span: 5 }"
        :wrapper-col-props="{ span: 18 }"
      >
        <a-form-item
          field="carport"
          label="变更车位"
          :rules="[{ required: true, message: '请填写变更车位' }]"
        >
          <a-input v-model="form.carport" />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-spin>
</template>

<style lang="scss" scoped>
.paginationView {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
</style>
