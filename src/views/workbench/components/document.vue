<!--
* @description 文书待办
* @fileName document.vue
* <AUTHOR>
* @date 2025/06/11 10:27:29
!-->
<script setup lang="ts">
import { computed, reactive, ref } from "vue";
import * as apis from "@/batchApi";
import { to } from "await-to-js";
import { isEmpty } from "lodash";
import evaluateItem from "@/views/patient-center/components/evaluate-item/index.vue";
import { EPushStatus } from "@/types/enums";
import { Message } from "@arco-design/web-vue";
import { addFormContent } from "@/api/patients";
import getPdfPath from "@/utils/form";
import {
  postBdManageApiPatientDocAdd,
  postBdManageApiPatientDocUpdateById,
} from "@/batchApi";
import { ToDoType } from "../config";

interface State {
  activeKey: undefined | number;
  btnLoading: boolean;
  loading: boolean;
  total: number;
  pagination: { pageNum: number; pageSize: number };
  list: apis.PageUsingPOST_4ToDo[];
  record: apis.PageUsingPOST_4ToDo;
}

const props = defineProps<{ articleType: number | undefined }>();
const defaultPagination = { pageNum: 1, pageSize: 10 };
const formIframeRef = ref();
const pageState = reactive<State>({
  activeKey: undefined,
  loading: false,
  btnLoading: false,
  total: 0,
  pagination: defaultPagination,
  list: [],
  record: {},
});

const getList = async () => {
  pageState.loading = true;
  const [err, res] = await to(
    apis.postBdManageApiTodoPage({
      pageNum: pageState.pagination.pageNum,
      pageSize: pageState.pagination.pageSize,
      query: {
        type: ToDoType.document,
        articleType: props.articleType,
      },
    }),
  );
  pageState.loading = false;
  if (err) {
    throw err;
  }
  if (res?.data) {
    const defaultRecord = res?.data.content?.at(0);
    pageState.record = defaultRecord as State["record"];
    pageState.list = res?.data.content as State["list"];
    pageState.total = res?.data?.total as number;
  }
};

const formatGender = (gender: number) => {
  return gender === 1 ? "男" : gender === 2 ? "女" : "未知";
};

const formatTag = (pushStatus: number) => {
  return pushStatus === 0 ? "未推送" : pushStatus === 1 ? "已推送" : "未知";
};

const handlePageChange = (current: number) => {
  pageState.pagination.pageNum = current;
  getList();
};

const handlePageSizeChange = (pageSize: number) => {
  pageState.pagination.pageSize = pageSize;
  getList();
};

// 保存当前的模板数据
const handleSave = (_isPush = false, _isRedis = false) => {
  if (isEmpty(pageState.record)) {
    Message.info("请先选择一个患者！");
    return;
  }

  const formId = pageState.record?.formId ?? "";
  const schemaId = pageState.record?.schemaId ?? "";
  const templateId = pageState.record?.templateId ?? "";
  const orderId = pageState.record?.orderId ?? "";

  if (
    typeof formId !== "string" ||
    formId === "" ||
    typeof schemaId !== "string" ||
    schemaId === "" ||
    typeof templateId !== "string" ||
    templateId === ""
  ) {
    return;
  }

  formIframeRef.value?.submitForm((_values: any) => {
    // 这个地方的内容 不应该可以触发两次
    if (_values === false) {
      pageState.btnLoading = false;
      return;
    }
    // 获取编写的时候 对应的模板名称 如果没有编写文书模板的名称 则设置为未编写
    let templateName = "未编写";
    try {
      const clericalCover0Value = JSON.parse(_values?.clericalCover0);
      templateName = `${clericalCover0Value.title}${clericalCover0Value.subTitle}`;
    } catch (error) {
      console.error("error", error);
    }
    addFormContent({
      formId,
      orderId,
      schemaId,
      content: JSON.stringify(_values),
    }).then(_formRes => {
      if (
        typeof _formRes?.data?.contentId === "string" &&
        _formRes?.data?.contentId !== ""
      ) {
        invokeSave(
          formId,
          schemaId,
          templateId,
          orderId,
          _formRes?.data?.contentId,
          templateName,
          _isPush,
          _isRedis,
        );
      }
    });
    console.log("form_data", _values);
  });
  pageState.btnLoading = true;
};

const invokeSave = (
  formId = "",
  schemaId = "",
  templateId = "",
  orderId = "",
  contentId = "",
  templateName = "",
  _isSubmit = false,
  _isRedis = false,
) => {
  // 获取当前数据的id
  const { docId: id } = pageState.record;
  let postBody;
  if (
    // 推送的时候 需要生成一下pdf路径
    _isSubmit
  ) {
    postBody = getPdfPath(
      {
        formId,
        schemaId,
        contentId,
        patientId: pageState.record?.patientId as number,
      },
      true,
    );
  }
  if (typeof id === "string" && id !== "") {
    postBdManageApiPatientDocUpdateById({
      contentId: contentId as unknown as number,
      formId: formId as unknown as number,
      schemaId: schemaId as unknown as number,
      templateId: templateId as unknown as number,
      orderId: orderId as unknown as string,
      isPush: _isSubmit,
      isRedis: _isRedis,
      patientId: pageState.record?.patientId,
      id: id as unknown as number,
      name: templateName,
      postBody,
    })
      .then(_res => {
        getList();
        console.log("_res", _res);
      })
      .finally(() => {
        pageState.btnLoading = false;
      });
  } else {
    postBdManageApiPatientDocAdd({
      contentId: contentId as unknown as number,
      /** 表单ID */
      formId: formId as unknown as number,
      /** 结构ID */
      schemaId: schemaId as unknown as number,
      /** 文书模板ID */
      templateId: templateId as unknown as number,
      orderId: orderId as unknown as string,
      /** 是否推送(0.否1.是) */
      isPush: _isSubmit,
      isRedis: _isRedis,
      /** 患者ID */
      patientId: pageState.record?.patientId,
      name: templateName,
      postBody,
    })
      .then(_res => {
        console.log("_res", _res);
        getList();
      })
      .finally(() => {
        pageState.btnLoading = false;
      });
  }
};

const showEmpty = computed(() => {
  const defaultObj = {
    status: true,
    emptyDescription: "请先选择一个患者!",
  };
  if (isEmpty(pageState.record)) return defaultObj;
  if (
    !pageState.record.formId ||
    !pageState.record?.schemaId ||
    !pageState.record?.patientId
  ) {
    return {
      status: true,
      emptyDescription: "患者数据不正确，请联系管理员，或切换数据！",
    };
  }
  return { status: false, emptyDescription: "" };
});

const showTool = computed(() => {
  if (
    !isEmpty(pageState.record) &&
    pageState.record?.pushStatus !== EPushStatus.FINISH
  ) {
    return true;
  }
  return false;
});
const optTypeStr = computed(() => {
  const status = pageState.list?.find(
    item => item?.xid === pageState.record?.xid,
  )?.pushStatus;
  if (status === 1) {
    return "view";
  }
  return "edit";
});

getList();
</script>

<template>
  <div v-if="!pageState?.list?.length" class="emptyView">
    <a-empty />
  </div>
  <div v-else class="documentView">
    <a-spin style="width: 100%" :loading="pageState.loading" tip="加载中...">
      <div class="userListView">
        <a-scrollbar
          class="content"
          style="
            height: calc(100vh - 120px);
            overflow: auto;
            margin-bottom: 16px;
          "
        >
          <div
            v-for="item in pageState.list"
            :key="item?.xid"
            :class="[
              'infoViwe',
              { active: item?.xid === pageState.record?.xid },
            ]"
            @click="pageState.record = item"
          >
            <div class="user">
              <span style="margin-bottom: 8px"
                ><label> 患者姓名：</label>{{ item?.patientName }}
                {{ formatGender(item?.gender as number) }}
                {{ item?.age ?? "-" }}岁
              </span>
              <span><label> 电话：</label>{{ item?.phone }}</span>
            </div>
            <a-tag bordered color="orange">{{
              formatTag(item?.pushStatus as number)
            }}</a-tag>
          </div>
        </a-scrollbar>

        <a-pagination
          :total="pageState.total"
          :page-size="pageState.pagination.pageSize"
          :current="pageState.pagination.pageNum"
          size="mini"
          @change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        />
      </div>
    </a-spin>

    <!-- 文书 -->
    <div class="document-content">
      <div
        style="height: calc(100vh - 120px); overflow: auto; margin-bottom: 16px"
      >
        <div v-if="showEmpty.status" class="document-content-empty">
          <a-empty :description="showEmpty.emptyDescription">
            <template #image>
              <icon-exclamation-circle-fill />
            </template>
          </a-empty>
        </div>
        <evaluate-item
          v-else
          ref="formIframeRef"
          :name="pageState.record.articleTypeName ?? ''"
          :uuid="`${pageState.record?.formId ?? ''}-${pageState.record?.schemaId ?? ''}`"
          :patient-id="pageState.record?.patientId ?? ''"
          :form-id="pageState.record?.formId ?? ''"
          :schema-id="pageState.record?.schemaId ?? ''"
          :content-id="pageState.record?.contentId ?? ''"
          :opt-type="optTypeStr"
          :custom-submit="true"
        />
      </div>
      <div v-if="showTool" class="tool">
        <a-space>
          <a-button
            type="primary"
            :loading="pageState.btnLoading"
            @click="handleSave(false)"
          >
            <template #icon>
              <icon-save />
            </template>
            <template #default>保存</template>
          </a-button>
          <a-button
            type="primary"
            :loading="pageState.btnLoading"
            @click="handleSave(true)"
          >
            <template #icon>
              <icon-save />
            </template>
            <template #default>保存并推送</template>
          </a-button>
        </a-space>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.emptyView {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.documentView {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 310px 1fr;
  column-gap: 40px;

  .userListView {
    border-right: 1px solid #e5e5e5;
    padding-right: 16px;

    .active {
      border: 1px solid #0052d9 !important;
    }
    .infoViwe {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      margin-bottom: 16px;

      .user {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        > span {
          color: #000000;
          font-size: 14px;

          > label {
            color: #666666;
          }
        }
      }
    }
  }

  .document-content {
    position: relative;
    height: 100%;

    .document-content-empty {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
    }
    .tool {
      position: absolute;
      bottom: 20px;
      right: 20px;
    }
  }
}
</style>
