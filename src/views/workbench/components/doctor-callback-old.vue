<!--
* @description 家医回电的历史代码
* @fileName DoctorCallBack.vue
* <AUTHOR>
* @date 2024/11/20 17:08:38
!-->
<script setup lang="ts">
import { reactive } from "vue";
import * as apis from "@/batchApi";
import { Message } from "@arco-design/web-vue";

type List = apis.GetPatCallListUsingPOST_1CallListRespVo;
type IState = {
  loading: boolean;
  data: List[];
};

const filedObj = {
  客户信息: ["patientName", "sex", "contactMobile"],
  发起时间: "startTime",
  等待时长: "awaitDate",
  需求: "callContent",
};

const pageState = reactive<IState>({
  loading: false,
  data: [],
});

const props = defineProps<{ refreshFunc: () => void }>();

const getDoctorCallBackList = async () => {
  try {
    /** 状态 1：代表已完成，0：代表待办 */
    pageState.loading = true;
    const { data } = await apis.postBdManageApiCallGetPatCallList({
      status: 0,
      type: 1,
    });
    pageState.data = data as List[];
    pageState.loading = false;
  } catch (error) {
    pageState.loading = false;
    console.log("🤞", error);
  }
};

const convertData = (
  record: apis.GetPatCallListUsingPOST_1CallListRespVo,
  file: string
) => {
  if (file === "sex") {
    /* 1：男   2：女   3：未知  */
    return record[file] === 1 ? "男" : record[file] === 2 ? "女" : "未知";
  }
  return record[file as keyof typeof record];
};

const handleCallBack = async (callId: number) => {
  try {
    pageState.loading = true;
    const { code } = await apis.postBdManageApiCallPatCallHandle({ callId });
    if (code === "1") {
      getDoctorCallBackList();
      props?.refreshFunc();
      pageState.loading = false;
      Message.success({
        content: "处理成功",
      });
    }
    pageState.loading = false;
  } catch (error) {
    pageState.loading = false;
    console.log("👸", error);
  }
};

getDoctorCallBackList();
</script>

<template>
  <a-spin :loading="pageState.loading" class="task-container">
    <!-- <div class="task-container"> -->
    <div v-if="!pageState.data?.length" class="empty-view">
      <a-empty />
    </div>
    <a-card
      v-for="item in pageState.data"
      v-else
      :key="item.callId"
      :class="['card-view', { 'card-vie-after': item.status === 2 }]"
    >
      <div class="task-item-view">
        <div class="content">
          <div class="title">家医回电</div>
          <div class="info">
            <span v-for="(files, name) in filedObj" :key="name">
              {{ name }}：
              <template v-if="Array.isArray(files)">
                <label v-for="file in files" :key="file" class="file-value">
                  {{ convertData(item, file) }}
                </label>
              </template>
              <template v-else>
                <label class="file-value">
                  {{ item[files as keyof typeof item] }}
                </label>
              </template>
            </span>
          </div>
        </div>
        <a-space class="space-view">
          <a-button
            size="mini"
            type="outline"
            @click="() => handleCallBack(item.callId as number)"
            >已回电</a-button
          >
        </a-space>
      </div>
    </a-card>
    <!-- </div> -->
  </a-spin>
</template>

<style lang="scss" scoped>
.task-container {
  height: calc(100% - 64px);
  overflow: scroll;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-7);

  .empty-view {
    height: 100%;
    display: flex;
    align-items: center;
  }

  .card-vie-after {
    border: 1px solid #ff5252;
    &::after {
      position: absolute;
      top: 0;
      left: 0;
      content: "急";
      width: 20px;
      height: 18px;
      text-align: center;
      border-radius: 3px 0px 4px 0px;
      background: #ff5252;
      font-size: var(--font-size-caption);
      color: white;
    }
  }
  .card-view {
    position: relative;
    border-radius: var(--border-4);

    .task-item-view {
      display: flex;
      justify-content: space-between;
      .content {
        .title {
          font-size: var(--font-size-title-1);
          color: var(--text-span-color);
          margin-bottom: var(--spacing-7);
        }
        .info {
          display: flex;
          flex-wrap: wrap;
          gap: var(--spacing-7) var(--spacing-12);
          color: var(--text-gray-color);
          .file-value {
            color: var(--text-span-color);
            margin-left: var(--spacing-3);
          }
        }
      }

      .space-view {
        margin-left: 7.25rem;
      }
    }
  }
}
</style>
