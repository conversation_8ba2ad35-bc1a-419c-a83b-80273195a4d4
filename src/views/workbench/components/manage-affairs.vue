<!--
* @description 已处理事务
* @fileName ManageAffairs.vue
* <AUTHOR>
* @date 2024/11/20 17:48:26
!-->
<script setup lang="ts">
import { reactive, computed } from "vue";
import * as apis from "@/batchApi";
import dayjs from "dayjs";
import { ProcessedTypeInfo } from "../config";

type List = apis.GetPatCallListUsingPOSTCallListRespVo;
type IState = {
  affairsForm: Pick<apis.postBdManageApiCallGetPatCallListRequest, "type"> & {
    date?: any;
    startDate?: string;
    endDate?: string;
  };
  loading: boolean;
  data: List[];
};

const pageState = reactive<IState>({
  affairsForm: {
    type: 3,
    startDate: dayjs().subtract(7, "day").format("YYYY-MM-DD"),
    endDate: dayjs().format("YYYY-MM-DD"),
  },
  loading: true,
  data: [],
});

const defaultDate = computed(() => {
  return [
    dayjs().subtract(7, "day").format("YYYY-MM-DD"),
    dayjs().format("YYYY-MM-DD"),
  ];
});

const convertData = (
  record: apis.GetPatCallListUsingPOSTCallListRespVo,
  file: string
) => {
  if (file === "sex") {
    /* 1：男   2：女   3：未知  */
    return record[file] === 1 ? "男" : record[file] === 2 ? "女" : "未知";
  }
  return record[file as keyof typeof record];
};

const getProcessedList = async (
  params?: apis.postBdManageApiCallGetPatCallListRequest
) => {
  try {
    /** 状态 1：代表已完成，0：代表待办 */
    pageState.loading = true;
    const { date, ...otherDate } = pageState.affairsForm || {};
    const { data } = await apis.postBdManageApiCallGetPatCallList({
      status: 1,
      ...otherDate,
      ...params,
    });
    pageState.data = data as List[];
    pageState.loading = false;
  } catch (error) {
    pageState.loading = false;
    console.log("🤞", error);
  }
};

getProcessedList();
</script>

<template>
  <div class="manage-affairs-view">
    <a-form :model="pageState.affairsForm" layout="inline">
      <a-form-item field="type" label="任务类型">
        <a-select
          v-model="pageState.affairsForm.type"
          placeholder="请选择类型"
          @change="
            (type: any) => {
              getProcessedList({type} as any);
            }
          "
        >
          <a-option :value="3">全部</a-option>
          <a-option :value="1">家医回电</a-option>
          <!-- <a-option :value="2">预留车位</a-option> -->
        </a-select>
      </a-form-item>
      <a-form-item field="date" label="完成时间">
        <a-range-picker
          v-model="pageState.affairsForm.date"
          style="width: 240px; margin: 0 24px 24px 0"
          format="YYYY-MM-DD"
          :default-value="defaultDate"
          @change="
            (value: any) => {
              getProcessedList({startDate: value?.[0],endDate: value?.[1]});
            }
          "
        />
      </a-form-item>
    </a-form>
    <div v-if="!pageState.data?.length" class="empty-view">
      <a-empty />
    </div>
    <div v-else class="task-container">
      <a-card v-for="item in pageState.data" :key="item.callId">
        <div class="task-item-view">
          <div class="content">
            <div class="title">
              {{
                ProcessedTypeInfo[item.type as keyof typeof ProcessedTypeInfo]
                  ?.title
              }}
            </div>
            <div class="info">
              <span
                v-for="(files, name) in ProcessedTypeInfo[item.type as keyof typeof ProcessedTypeInfo]?.content"
                :key="name"
              >
                {{ name }}：
                <template v-if="Array.isArray(files)">
                  <label v-for="file in files" :key="file" class="file-value">
                    {{ convertData(item, file) }}
                  </label>
                </template>
                <template v-else>
                  <label class="file-value">
                    {{ item[files as keyof typeof item] }}
                  </label>
                </template>
              </span>
            </div>
          </div>
          <!-- <a-space class="space-view">
            <a-button size="mini" type="primary">撤回</a-button>
            <a-button size="mini" type="primary">变更</a-button>
          </a-space> -->
        </div>
      </a-card>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.manage-affairs-view {
  height: 100%;
  overflow: hidden;

  .task-container {
    height: calc(100% - 64px);
    overflow: scroll;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-7);
    .task-item-view {
      display: flex;
      justify-content: space-between;
      .content {
        .title {
          font-size: var(--font-size-title-1);
          color: var(--text-span-color);
          margin-bottom: var(--spacing-7);
        }
        .info {
          display: flex;
          flex-wrap: wrap;
          gap: var(--spacing-7) var(--spacing-12);
          color: var(--text-gray-color);
          .file-value {
            color: var(--text-span-color);
            margin-right: var(--spacing-3);
          }
        }
      }

      .space-view {
        margin-left: 7.25rem;
      }
    }
  }
}
</style>
