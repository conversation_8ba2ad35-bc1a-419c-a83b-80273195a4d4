<!--
 * @Description: 快捷入口
 * @Author: yangrong<PERSON>
 * @Date: 2024-12-17 16:59:06
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-04-15 15:08:36
-->
<template>
  <h2 class="quick-access-title">快捷入口</h2>
  <div class="quick-access-container">
    <div
      v-for="(item, index) in items"
      :key="index"
      class="quick-access-item"
      :class="{
        active: whiteList?.includes(item.title),
        disabled: !whiteList?.includes(item.title),
      }"
      @click="handleQuickService(item?.title)"
    >
      <img :src="item.icon" alt="" />
      <span>{{ item.title }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import taskIcon from "@/assets/images/quick-access/task.png";
import userIcon from "@/assets/images/quick-access/user.png";
import medicalIcon from "@/assets/images/quick-access/medical.png";
import insureIcon from "@/assets/images/quick-access/insure.png";
import healthIcon from "@/assets/images/quick-access/health.png";
import followIcon from "@/assets/images/quick-access/follow.png";
import orderIcon from "@/assets/images/quick-access/order.png";
import synergyIcon from "@/assets/images/quick-access/synergy.png";
import subscribeIcon from "@/assets/images/quick-access/subscribe.png";
import useQuickAccessStore from "@/store/modules/quick-access";
import workOrderIcon from "@/assets/images/workOrder.png";

const quickAccessStore = useQuickAccessStore();

const whiteList = ["保险服务", "服务预约", "创建工单"];

const items = ref([
  {
    icon: taskIcon,
    title: "任务中心",
  },
  {
    icon: userIcon,
    title: "用户中心",
  },
  {
    icon: medicalIcon,
    title: "就医服务",
  },
  // {
  //   icon: insureIcon,
  //   title: '保险服务'
  // },
  {
    icon: healthIcon,
    title: "健康管理",
  },
  {
    icon: followIcon,
    title: "推荐跟踪",
  },
  {
    icon: orderIcon,
    title: "我的订单",
  },
  {
    icon: synergyIcon,
    title: "协同订单",
  },
  {
    icon: insureIcon,
    title: "保险服务",
  },
  {
    icon: subscribeIcon,
    title: "服务预约",
  },
  {
    icon: workOrderIcon,
    title: "创建工单",
  },
]);

// 触发快捷服务
const handleQuickService = (title: string) => {
  const eventMap: Record<string, () => void> = {
    保险服务: () => quickAccessStore.toggleServiceReservation(true),
    服务预约: () => quickAccessStore.toggleServiceAppointment(true),
    创建工单: () => quickAccessStore.toggleWorkOrderVisibleStatus(true),
  };
  const eventFunc = eventMap[title];
  eventFunc?.();
};
</script>

<style lang="scss" scoped>
.quick-access-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 18px;
  color: #1a1a1a;
}

.quick-access-container {
  margin-top: 16px;
  // display: grid;
  // grid-template-columns: repeat(6, 1fr);
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  .quick-access-item {
    padding-top: 12px;
    padding-bottom: 8px;
    &.disabled {
      display: none;
    }
    &.active {
      cursor: pointer;
    }
    > img {
      display: block;
      width: 54px;
      height: 54px;
      margin: 0 auto;
    }
    > span {
      color: #3d3d3d;
      font-size: 14px;
      display: block;
      margin-top: 12px;
      text-align: center;
    }
  }
}
</style>
