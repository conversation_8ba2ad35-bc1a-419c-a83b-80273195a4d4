<!--
* @description 专家咨询
* @fileName expert-consultation-item.vue
* <AUTHOR>
* @date 2025/06/08 21:12:12
!-->
<script setup lang="ts">
import { computed } from "vue";
import { getImageSrc } from "@/utils";
import * as apis from "@/batchApi";

type IParkingSpaceItemProps = {
  data?: apis.PageUsingPOSTToDo;
  isProcessed?: boolean;
};

const props = defineProps<IParkingSpaceItemProps>();

const gender = computed(() => {
  /** 就诊人性别,1男 2女 3未知 */
  const gender = props?.data?.gender;
  return gender === 1 ? "男" : gender === 2 ? "女" : "未知";
});
</script>

<template>
  <div class="expertConsultationItemView">
    <!-- left -->
    <div class="infoView">
      <div class="baseInfo">
        <img :src="getImageSrc('peopleMan.png')" />
        <span>{{ data?.patientName }} {{ gender }} {{ data?.phone }}</span>
      </div>
      <div class="otherIngo">
        <span class="labelText">权益包名称</span>
        <span>{{ data?.offlineAppointTime ?? "-" }}</span>
        <template v-if="isProcessed">
          <span style="margin-left: 52px">
            <label class="labelText">处理人</label>
            <label style="font-weight: bold; margin-left: 4px">{{
              data?.signInName ?? "-"
            }}</label></span
          >
          <span style="margin-left: 52px"
            ><label class="labelText">任务完成时间</label
            >{{ data?.leaveTime ?? "-" }}</span
          >
        </template>
        <template v-else>
          <span style="margin-left: 52px">
            <label class="labelText">发起时间</label>2025/05/03 13:00:34 向
            <label style="color: #0052d9">「向志明专家团队」</label>
            <label style="margin-left: 4px">发起咨询</label></span
          >
          <span style="margin-left: 52px"
            ><label class="labelText">等待时长</label
            ><label style="font-weight: bold; color: #ff7d00; margin-left: 4px"
              >3小时34分</label
            ></span
          >
        </template>
      </div>
    </div>
    <!-- right -->
    <div>
      <slot name="operation" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.expertConsultationItemView {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f9fbff;
  border-radius: 8px;
  padding: 16px 20px;
  margin-bottom: 16px;

  &::after {
    content: "";
    width: 4px;
    height: 16px;
    background: #0052d9;
    position: absolute;
    top: 20px;
    left: 0;
  }

  .infoView {
    .baseInfo {
      font-size: 14px;
      font-weight: bold;
      color: #3d3d3d;
      display: flex;
      align-items: center;

      > img {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }

      .adress {
        color: #0052d9;
        margin-left: 60px;
      }
    }
  }

  .otherIngo {
    font-size: 12px;
    color: #000000;
    margin-top: 12px;

    .labelText {
      color: #86909c;
      margin-right: 12px;
    }
  }
}
</style>
