<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2025-03-31 10:38:58
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-04-24 15:58:55
-->
<template>
  <a-table
    :data="tableData"
    :pagination="paginationInfo"
    @page-change="handlePageChange"
    @page-size-change="handlePageSizeChange"
  >
    <template #tr="{ record }">
      <tr class="my-tr" @dblclick.prevent="handlePatientInfo(record)" />
    </template>
    <!-- 定义表格的列 -->
    <template #columns>
      <a-table-column
        title="序号"
        :width="65"
      >
        <template #cell="{ rowIndex }">
          <span>{{ rowIndex + 1 }}</span>
        </template>
      </a-table-column>
      <a-table-column
        title="患者姓名"
        data-index="patientName"
        :width="150"
      >
        <template #cell="{ record }">
          <user-info :record="{ name: record.patientName }" />
        </template>
      </a-table-column>
      <a-table-column
        title="性别"
        data-index="gender"
      >
        <template #cell="{ record }">
          <span>{{ record.gender === "1" ? "男" : "女" }}</span>
        </template>
      </a-table-column>
      <a-table-column
        title="年龄"
        data-index="age"
      ></a-table-column>
      <a-table-column
        title="联系方式"
        data-index="contactMobile"
      ></a-table-column>
      <a-table-column
        title="本次随访时间"
        data-index="minTime"
      ></a-table-column>
      <a-table-column
        :width="200"
        title="随访内容"
        data-index="packName"
      >
        <template #cell="{ record }">
          <span class="overflowHidden">{{ record.packName }}</span>
        </template>
      </a-table-column>
      <a-table-column title="操作">
        <template #cell="{ record }">
          <div class="table-operate">
            <a-button
              size="mini"
              type="outline"
              @click="handleToFollowUp(record)"
            >
              去随访
            </a-button>
          </div>
        </template>
      </a-table-column>
    </template>
  </a-table>
</template>

<script setup lang="ts">
import userInfo from '@/components/bd-table/components/user-info.vue';
import { useAppStore, usePatientCenterStore, useUserStore } from '@/store';
import { markRaw, onMounted, reactive, ref } from 'vue';
import { GetBdFollowListUsingPOSTHxPageBdFollowupListResVo, postBdManageApiFollowupGetBdFollowList } from '@/batchApi';
import followUpList from '../../patient-center/components/followup-history/list.vue'

const appStore = useAppStore()
const userStore = useUserStore();
// 设置患者360中页面的内容
const patientCenterStore = usePatientCenterStore()

const tableData = ref<GetBdFollowListUsingPOSTHxPageBdFollowupListResVo['content']>([]);

const paginationInfo = reactive({
  current: 1,
  total: 0,
  pageSize: 15,
  showTotal: true
})

const handlePageChange = (
  page: number
) => {
  console.log('page', page)
  paginationInfo.current = page;
  init();
}

const handlePageSizeChange = (
  pageSize: number
) => {
  paginationInfo.pageSize = pageSize;
  init();
  console.log('pageSize', pageSize)
}

// 点击去随访
const handleToFollowUp = (
  _record: any
) => {
  // TODO: 展示对应的模块的信息
  appStore.openPatientCenter(_record)
  patientCenterStore.changeSecondaryMenu(true, '随访', [
    {
      name: '',
      component: markRaw(followUpList),
      componentParams: {
        patientId: _record.patientId
      }
    }
  ]);
  console.log('_record', _record)
}

const init = () => {
  postBdManageApiFollowupGetBdFollowList({
    pageNum: paginationInfo.current,
    pageSize: paginationInfo.pageSize,
    query: {
      teamId: userStore.accountInfo.teamId as unknown as number
    }
  }).then(_res => {
    if (
      Array.isArray(_res.data?.content)
    ) {
      tableData.value = _res.data.content;
    }
    paginationInfo.total = _res.data?.total ?? 0;
    console.log('_res', _res)
  })
}

// 点击展示患者的信息
const handlePatientInfo = (
  _record: any
) => {
  appStore.openPatientCenter(_record)
  console.log('_record', _record)
}

onMounted(() => {
  init();
})

</script>

<style lang="scss" scoped>
.overflowHidden {
  white-space: nowrap;    /* 禁止换行 */
  overflow: hidden;       /* 隐藏溢出内容 */
  text-overflow: ellipsis;/* 超出用省略号表示 */
  width: 200px;           /* 需设置容器宽度 */
  display: inline-block;
}
</style>