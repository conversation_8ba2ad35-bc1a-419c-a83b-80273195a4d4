<!--
* @description 预留车位
* @fileName parkingSpace.vue
* <AUTHOR>
* @date 2024/11/20 17:09:07
!-->
<script setup lang="ts">
import { reactive, ref } from "vue";
import * as apis from "@/batchApi";
import { to } from "await-to-js";
import doctorCallbackItem from './doctor-callback-item.vue';

interface State {
  loading: boolean;
  confirmLoading: boolean;
  visible: boolean;
  activeKey: number;
  searchValue: string | undefined;
  total: number;
  list: apis.GetPatCallListUsingPOST_1CallListRespVo[];
  xId: undefined | number;
}

const pageState = reactive<State>({
  loading: false,
  confirmLoading: false,
  visible: false,
  activeKey: 0,
  searchValue: undefined,
  total: 30,
  list: [],
  xId: undefined,
});

const loading = ref(false);

const getToDoList = async () => {
  pageState.loading = true;
  const [err, res] = await to(
    apis.postBdManageApiCallGetPatCallList({
      type: 1,
      status: pageState.activeKey
    }),
  );
  pageState.loading = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    pageState.list = res?.data;
  }
};

const handleTbasChange = (key: number | string) => {
  pageState.activeKey = key as number;
  pageState.searchValue = undefined;
  getToDoList();
};

// 家医回电已经进行回电的处理
const handleCalledBack = (_callId: number) => {
  loading.value = true;
  apis.postBdManageApiCallPatCallHandle({ callId: _callId })
    .then(_res => {
      console.log('_res', _res)
      getToDoList();
    })
    .finally(() => {
      loading.value = false;
    })
}

getToDoList();
</script>

<template>
  <a-spin style="width: 100%" :loading="pageState.loading" tip="加载中...">
    <a-tabs v-model="pageState.activeKey" @change="handleTbasChange">
      <!-- 家医回电没有搜索框展示 -->
      <!-- <template #extra>
        <a-input-search
          v-model="pageState.searchValue"
          :style="{ width: '100%' }"
          placeholder="请输入姓名/手机号"
          search-button
          @search="getToDoList"
          @press-enter="getToDoList"
        />
      </template> -->
      <!-- 展示待处理的家医回电的信息 -->
      <a-tab-pane :key="0" title="待处理">
        <a-empty v-if="!pageState.list?.length" />
        <div v-else>
          <doctor-callback-item
            v-for="item in pageState.list"
            :key="item?.callId"
            :data="item"
          >
            <template #operation>
              <a-button type="primary" :loading="loading" @click="() => handleCalledBack(item.callId as number)">确认已回电</a-button>
            </template>
          </doctor-callback-item>
        </div>
      </a-tab-pane>
      <!-- 展示已经处理的家医回电信息 -->
      <a-tab-pane :key="1" title="已处理">
        <div>
          <doctor-callback-item
            v-for="item in pageState.list"
            :key="item?.callId"
            :data="item"
            :is-processed="true"
          >
            <template #operation>
              <span>已回电</span>
            </template>
          </doctor-callback-item>
        </div>
      </a-tab-pane>
    </a-tabs>
  </a-spin>
</template>

<style lang="scss" scoped>

</style>
