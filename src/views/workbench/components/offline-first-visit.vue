<!--
* @description 线下首诊
* @fileName parkingSpace.vue
* <AUTHOR>
* @date 2024/11/20 17:09:07
!-->
<script setup lang="ts">
import { reactive } from "vue";
import to from "await-to-js";
import * as apis from "@/batchApi";
import day from "dayjs";
import { Message } from "@arco-design/web-vue";
import { ToDoType } from "../config";
import offlineFirstVisitItem from "./offlineFirstVisitItem.vue";

interface State {
  loading: boolean;
  btnLoading: boolean;
  activeKey: number;
  searchValue: string | undefined;
  total: number;
  pagination: { pageNum: number; pageSize: number };
  list: apis.PageUsingPOSTToDo[];
}
const defaultPagination = { pageNum: 1, pageSize: 10 };
const pageState = reactive<State>({
  loading: false,
  btnLoading: false,
  activeKey: 0,
  searchValue: undefined,
  total: 0,
  pagination: defaultPagination,
  list: [],
});

const getToDoList = async () => {
  pageState.loading = true;
  const [err, res] = await to(
    apis.postBdManageApiTodoPage({
      pageNum: pageState.pagination.pageNum,
      pageSize: pageState.pagination.pageSize,
      query: {
        keyword: pageState.searchValue,
        status: pageState.activeKey,
        type: ToDoType.firstVisit,
      },
    }),
  );
  pageState.loading = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    pageState.list = res?.data?.content as apis.PageUsingPOSTToDo[];
    pageState.total = res?.data?.total as number;
  }
};

const handleTbasChange = (key: number | string) => {
  pageState.activeKey = key as number;
  pageState.searchValue = undefined;
  pageState.pagination.pageNum = 1;
  getToDoList();
};

const handlePageChange = (current: number) => {
  pageState.pagination.pageNum = current;
  getToDoList();
};

const handlePageSizeChange = (pageSize: number) => {
  pageState.pagination.pageSize = pageSize;
  getToDoList();
};

const handleSignIn = async ({
  xid,
  type,
}: {
  xid: number | undefined;
  type: 1 | 2;
}) => {
  pageState.btnLoading = true;
  const [err, res] = await to(
    apis.postBdManageApiTodoOfflineUpdate({
      /** 操作类型 1签到,2离开 */
      handler: type,
      time: day().format("YYYY-MM-DD HH:mm:ss"),
      id: xid,
    }),
  );
  pageState.btnLoading = false;
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    Message.success("成功");
    getToDoList();
  }
};

getToDoList();
</script>

<template>
  <a-spin style="width: 100%" :loading="pageState.loading" tip="加载中...">
    <a-tabs
      v-model="pageState.activeKey"
      style="width: 100%"
      @change="handleTbasChange"
    >
      <template #extra>
        <a-input-search
          v-model="pageState.searchValue"
          :style="{ width: '100%' }"
          placeholder="请输入姓名/手机号"
          search-button
          @search="getToDoList"
          @press-enter="getToDoList"
        />
      </template>
      <a-tab-pane key="0" title="待处理">
        <a-empty v-if="!pageState.list?.length" />
        <div v-else>
          <offlineFirstVisitItem
            v-for="item in pageState.list"
            :key="item?.xid"
            :data="item"
          >
            <template #operation>
              <a-button
                v-if="!item?.signInTime"
                :loading="pageState.btnLoading"
                type="primary"
                style="margin-right: 8px"
                @click="handleSignIn({ xid: item?.xid, type: 1 })"
                >签到</a-button
              >
              <a-button
                v-if="item?.signInTime"
                :loading="pageState.btnLoading"
                type="primary"
                status="warning"
                @click="handleSignIn({ xid: item?.xid, type: 2 })"
                >离开</a-button
              >
            </template>
          </offlineFirstVisitItem>
          <div class="paginationView">
            <a-pagination
              :total="pageState.total"
              :page-size="pageState.pagination.pageSize"
              :current="pageState.pagination.pageNum"
              size="medium"
              show-total
              show-jumper
              show-page-size
              @change="handlePageChange"
              @page-size-change="handlePageSizeChange"
            />
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="1" title="已处理">
        <a-empty v-if="!pageState.list?.length" />
        <div v-else>
          <offlineFirstVisitItem
            v-for="item in pageState.list"
            :key="item.xid"
            :data="item"
            :is-processed="true"
          />
          <div class="paginationView">
            <a-pagination
              :total="pageState.total"
              :page-size="pageState.pagination.pageSize"
              :current="pageState.pagination.pageNum"
              size="medium"
              show-total
              show-jumper
              show-page-size
              @change="handlePageChange"
              @page-size-change="handlePageSizeChange"
            />
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
  </a-spin>
</template>

<style lang="scss" scoped>
.paginationView {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
</style>
