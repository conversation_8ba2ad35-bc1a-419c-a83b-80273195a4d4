<!--
* @description 停车单个
* @fileName parking-space-item.vue
* <AUTHOR>
* @date 2025/06/08 21:12:12
!-->
<script setup lang="ts">
import { getImageSrc } from "@/utils";
import * as apis from "@/batchApi";
import { computed } from "vue";
import dayjs from "dayjs";

type IParkingSpaceItemProps = {
  data?: apis.GetPatCallListUsingPOST_1CallListRespVo;
  isProcessed?: boolean;
};

const props = defineProps<IParkingSpaceItemProps>();

const gender = computed(() => {
  /** 就诊人性别,1男 2女 3未知 */
  const gender = props?.data?.sex;
  return gender === 1 ? "男" : gender === 2 ? "女" : "未知";
});
</script>

<template>
  <div class="parkingSpaceItemView">
    <!-- left -->
    <div class="infoView">
      <div class="baseInfo">
        <img :src="getImageSrc('peopleMan.png')" />
        <span>{{ data?.patientName }} {{ gender }} {{ data?.contactMobile }}</span>
      </div>
      <div class="otherIngo">
        <span>
          <label class="labelText">发起时间</label>
          {{ data?.startTime ?? "-" }}
        </span>
        <span v-if="props.isProcessed">
          <label class="labelText">处理时间</label>
          {{ data?.handleTime ?? "-" }}
        </span>
        <span v-if="props.isProcessed">
          <label class="labelText">处理人</label>
          {{ data?.handleUser ?? "-" }}
        </span>
        <span>
          <label class="labelText">等待时长</label>
          {{ data?.awaitDate ?? "-" }}
        </span>
        <span>
          <label class="labelText">需求</label>
          {{ data?.callContent ?? "-" }}
        </span>
      </div>
    </div>
    <!-- right -->
    <div>
      <slot name="operation" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.parkingSpaceItemView {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f9fbff;
  border-radius: 8px;
  padding: 16px 20px;
  margin-bottom: 16px;

  &::after {
    content: "";
    width: 4px;
    height: 16px;
    background: #0052d9;
    position: absolute;
    top: 20px;
    left: 0;
  }

  .infoView {
    .baseInfo {
      font-size: 14px;
      font-weight: bold;
      color: #3d3d3d;
      display: flex;
      align-items: center;

      > img {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }

      .adress {
        color: #0052d9;
        margin-left: 60px;
      }
    }
  }

  .otherIngo {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    font-size: 12px;
    color: #000000;
    margin-top: 12px;

    >span {
      &:not(:first-child) {
        margin-left: 52px;
      }
    }

    .labelText {
      color: #86909c;
      margin-right: 12px;
    }
  }
}
</style>
