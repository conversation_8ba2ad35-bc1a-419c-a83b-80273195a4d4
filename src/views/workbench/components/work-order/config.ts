import { RenderInfo } from "@/types/global";
import { TableColumnData } from "@arco-design/web-vue";
import * as apis from '@/batchApi'
import { UlfillmentModeMap } from "@/views/work-order-manage/config";

export const doctorColumn: Array<TableColumnData> = [
  {
    title: "姓名",
    width: 120,
    dataIndex: "doctorName",
  },
  {
    title: "用户ID/工号",
    width: 200,
    dataIndex: "doctorUserId",
  },
  {
    title: "联系电话",
    width: 130,
    dataIndex: "doctor<PERSON><PERSON>",
  },
  {
    title: "职称",
    dataIndex: "majorName",
  },
  {
    title: "头衔",
    dataIndex: "titleName",
  },
];
