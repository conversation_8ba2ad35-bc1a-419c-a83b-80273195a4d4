<!--
* @description 工单待办
* @fileName index.vue
* <AUTHOR>
* @date 2025/07/12 00:36:15
!-->
<script setup lang="ts">
import { reactive } from "vue";
import { WorkOrderStatusEnum } from "@/views/work-order-manage/config";
import CompleteItem from "./completeItem.vue";
import Pending from "./pending.vue";

interface State {
  activeKey: WorkOrderStatusEnum.COMPLETED | WorkOrderStatusEnum.PENDING;
  searchValue: string | undefined;
}
const pageState = reactive<State>({
  activeKey: WorkOrderStatusEnum.PENDING,
  searchValue: undefined,
});

const handleTbasChange = (key: State["activeKey"] | string | number) => {
  pageState.activeKey = key as State["activeKey"];
};
</script>

<template>
  <a-tabs
    v-model="pageState.activeKey"
    style="width: 100%"
    @change="handleTbasChange"
  >
    <template #extra>
      <!-- <a-input-search
          v-model="pageState.searchValue"
          :style="{ width: '100%' }"
          placeholder="请输入姓名/手机号"
          search-button
          @search="getToDoList"
          @press-enter="getToDoList"
        /> -->
    </template>
    <a-tab-pane :key="WorkOrderStatusEnum.PENDING" title="待处理">
      <Pending :active-key="pageState.activeKey" />
    </a-tab-pane>
    <a-tab-pane :key="WorkOrderStatusEnum.COMPLETED" title="已处理">
      <CompleteItem :active-key="pageState.activeKey" />
    </a-tab-pane>
  </a-tabs>
</template>

<style lang="scss" scoped>
.paginationView {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
</style>
