<!--
* @description 待处理
* @fileName pending.vue
* <AUTHOR>
* @date 2025/07/12 01:42:11
!-->
<script setup lang="ts">
import { reactive, watch, ref, computed } from "vue";
import { renderCell, getImageSrc } from "@/utils";
import * as apis from "@/batchApi";
import to from "await-to-js";
import {
  renderWorkOrderDetails,
  renderBaseInfo,
  WorkOrderStatusEnum,
  WorkOrderStatusMap,
  PriorityMap,
  PriorityEnum,
} from "@/views/work-order-manage/config";
import { Message, TableRowSelection } from "@arco-design/web-vue";
import { color } from "echarts";
import { useUserStore } from "@/store";
import { doctorColumn } from "./config";

type Props = {
  activeKey: WorkOrderStatusEnum.PENDING | WorkOrderStatusEnum.COMPLETED;
};
type IRecord = apis.MyTicketsUsingPOSTTicketQueryResVo;
interface IState {
  loading: boolean;
  loadingTask: boolean;
  loadingDetalis: boolean;
  loadingTable: boolean;
  loadingComplete: boolean;

  visible: boolean;
  detailsData: apis.GetTicketUsingGETTicketDetailResVo;
  status: WorkOrderStatusEnum.COMPLETED | WorkOrderStatusEnum.PENDING;
  currentRecord: IRecord | undefined;

  doctorList: apis.GetTeamDoctorUsingPOSTDoctorInfoDto[];

  pagination: { pageNum: number; pageSize: number };
  list: apis.postBdManageApiTicketAssigneeMyResponse["data"];
  total: number;
}

const defaultPagination = { pageNum: 1, pageSize: 10 };
const userStore = useUserStore();
const props = defineProps<Props>();
const pageState = reactive<IState>({
  loading: false,
  loadingDetalis: false,
  loadingTask: false,
  loadingTable: false,
  loadingComplete: false,

  visible: false,
  detailsData: {},
  status: WorkOrderStatusEnum.PENDING,
  currentRecord: undefined,
  doctorList: [],

  pagination: defaultPagination,
  list: [],
  total: 0,
});
const selectedKeys = ref<string[]>([]);

const rowSelection: TableRowSelection = {
  type: "radio",
};

/* 待办列表 */
const getToDoList = async (name?: string) => {
  pageState.loading = true;
  const [err, res] = await to(
    apis.postBdManageApiTicketAssigneeMy({
      pageNum: pageState.pagination.pageNum,
      pageSize: pageState.pagination.pageSize,
      status: [
        WorkOrderStatusEnum.PENDING,
        WorkOrderStatusEnum.SUSPENDED,
      ] as any,
      customerName: name,
    }),
  );
  pageState.loading = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    pageState.currentRecord = res?.data?.at(0);
    pageState.list = res?.data as IState["list"];
    pageState.total = res?.total as number;
  }
};

/* 详情 */
const getManagementUserAdminDetail = async (id: number | undefined) => {
  pageState.loadingDetalis = true;
  const [err, res] = await to(
    apis.getBdManageApiTicketById({ id: id as number }),
  );
  pageState.loadingDetalis = false;
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    pageState.detailsData = res?.data as IState["detailsData"];
  }
};

/* 更新状态 */
const updateTaskStatus = async () => {
  pageState.loadingTask = true;
  const [err, res] = await to(
    apis.postBdManageApiTicketAssigneeStatusById(
      { id: pageState.currentRecord?.xid as number },
      { status: WorkOrderStatusEnum.SUSPENDED },
      {},
    ),
  );
  pageState.loadingTask = false;
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    Message.success("成功");
    getToDoList();
  }
};

const isShowSuspended = computed(() => {
  const statusRecord = pageState.detailsData?.assignees?.find(
    item => item?.xid === pageState.currentRecord?.xid,
  );
  if (
    [WorkOrderStatusEnum.PENDING]?.includes(
      statusRecord?.status as WorkOrderStatusEnum,
    )
  ) {
    return true;
  }
  return false;
});

/* 转交他人 */
const handlePassOn = async () => {
  pageState.visible = true;
  pageState.loadingTable = true;
  const [err, res] = await to(
    apis.postBdManageApiServiceReservationGetTeamDoctor({}),
  );
  pageState.loadingTable = false;
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    const result = res?.data?.filter(
      item => item?.doctorUserId !== userStore?.accountInfo?.userId,
    ); // 筛出自己
    pageState.doctorList = result as IState["doctorList"];
  }
};

/* 提交转交 */
const handleShift = async (): Promise<boolean> => {
  return new Promise(resolve => {
    if (!selectedKeys.value?.length) {
      Message.error("请选择需要转交的人");
      resolve(false);
      return;
    }
    apis
      .postBdManageApiTicketAssigneeTransferById(
        { id: pageState.currentRecord?.xid as number },
        { toUserId: selectedKeys?.value?.at(0) as unknown as number },
        {},
      )
      .then(res => {
        if (res?.code === "1") {
          Message.success("成功");
          getToDoList();
          selectedKeys.value = [];
          pageState.visible = false;
          pageState.doctorList = [];
          resolve(true);
        }
      })
      .catch(() => {
        resolve(false);
      });
  });
};

/* 完成 */
const handleComplete = async () => {
  pageState.loadingComplete = true;
  const [err, res] = await to(
    apis.postBdManageApiTicketAssigneeFinishById(
      { id: pageState.currentRecord?.xid as number },
      {},
    ),
  );
  pageState.loadingComplete = false;
  if (err) {
    throw err;
  }
  if (res.code === "1") {
    Message.success("成功");
    getToDoList();
  }
};

watch(
  () => pageState.currentRecord,
  newValue => {
    if (pageState.currentRecord?.ticketId) {
      getManagementUserAdminDetail(newValue?.ticketId);
    }
  },
);

watch(
  () => props.activeKey,
  () => {
    if (props.activeKey === WorkOrderStatusEnum.PENDING) {
      getToDoList();
    }
  },
);

const handlePageChange = (current: number) => {
  pageState.pagination.pageNum = current;
  getToDoList();
};

const handlePageSizeChange = (pageSize: number) => {
  pageState.pagination.pageSize = pageSize;
  getToDoList();
};

getToDoList();
</script>

<template>
  <div class="container">
    <a-empty v-if="!pageState.list?.length" />
    <a-scrollbar
      v-else
      class="content"
      style="height: calc(100vh - 180px); overflow: auto; margin-bottom: 16px"
    >
      <div class="conainer">
        <div class="left">
          <a-spin
            style="width: 100%"
            :loading="pageState.loading"
            tip="加载中..."
          >
            <a-scrollbar
              class="content"
              style="
                height: calc(100vh - 120px);
                overflow: auto;
                margin-bottom: 16px;
              "
            >
              <div
                v-for="item in pageState.list"
                :key="item?.xid"
                :class="[
                  'userCard',
                  item.xid === pageState.currentRecord?.xid ? 'active' : '',
                ]"
                @click="pageState.currentRecord = item"
              >
                <a-row
                  :gutter="[8, 0]"
                  style="border-bottom: 1px solid #e5ecfc; margin-bottom: 12px"
                >
                  <a-col :span="12">
                    <div class="topSty">
                      <img :src="getImageSrc('man.png')" />
                      <span>{{ item?.customerName }}</span>
                    </div>
                  </a-col>
                  <a-col :span="12">
                    <div class="tagView">
                      <span
                        :style="{
                          color: WorkOrderStatusMap.get(
                            item.status as WorkOrderStatusEnum,
                          )?.color,
                          marginLeft: '8px',
                        }"
                        >{{
                          WorkOrderStatusMap.get(
                            item.status as WorkOrderStatusEnum,
                          )?.title
                        }}</span
                      >
                    </div>
                  </a-col>
                </a-row>

                <a-row :gutter="[10, 6]">
                  <a-col :span="8" style="color: #86909c">工单类型</a-col>
                  <a-col :span="16">{{ item?.typeName }}</a-col>
                  <a-col :span="8" style="color: #86909c">计划执行时间</a-col>
                  <a-col :span="16">{{ item?.plannedExecuteTime }}</a-col>
                </a-row>
              </div>
            </a-scrollbar>
          </a-spin>
        </div>

        <div v-if="pageState.list?.length" class="rightView">
          <a-spin
            style="width: 100%"
            :loading="pageState.loadingDetalis"
            tip="加载中..."
          >
            <div class="cardView">
              <div class="title">基础信息</div>
              <a-row :gutter="[0, 10]">
                <template v-for="item in renderBaseInfo" :key="item.attrName">
                  <a-col :span="item.span?.[0]" style="color: #86909c">
                    {{ item?.label }}
                  </a-col>
                  <a-col :span="item.span?.[1]">
                    <template v-if="item?.render">
                      <component
                        :is="renderCell(item.render(pageState.detailsData))"
                      />
                    </template>
                    <template v-else>
                      {{
                        pageState.detailsData?.[
                          item.attrName as keyof IState["detailsData"]
                        ] ?? "-"
                      }}
                    </template>
                  </a-col>
                </template>
              </a-row>
            </div>
            <div class="cardView">
              <div class="title">工单详情</div>
              <a-row :gutter="[0, 10]">
                <template
                  v-for="item in renderWorkOrderDetails"
                  :key="item.attrName"
                >
                  <a-col :span="item.span?.[0]" style="color: #86909c">
                    {{ item?.label }}
                  </a-col>
                  <a-col :span="item.span?.[1]">
                    <template v-if="item?.render">
                      <component
                        :is="renderCell(item.render(pageState.detailsData))"
                      />
                    </template>
                    <template v-else>
                      {{
                        pageState.detailsData?.[
                          item.attrName as keyof IState["detailsData"]
                        ] ?? "-"
                      }}
                    </template>
                  </a-col>
                </template>
                <template v-if="pageState.detailsData?.assignees?.length">
                  <a-col :span="4" style="color: #86909c"> 处理人 </a-col>
                  <a-col :span="19" style="color: #86909c">
                    <a-timeline>
                      <a-timeline-item
                        v-for="item in pageState.detailsData?.assignees"
                        :key="item?.xid"
                      >
                        <template #label>
                          <div class="tipText">
                            <span class="text">{{
                              item?.execUserName ?? "-"
                            }}</span>
                            <span>执行状态:</span>
                            <span
                              :style="{
                                color: WorkOrderStatusMap.get(
                                  item?.status as WorkOrderStatusEnum,
                                )?.color,
                                marginLeft: '6px',
                              }"
                              >{{
                                item?.status
                                  ? WorkOrderStatusMap.get(
                                      item?.status as WorkOrderStatusEnum,
                                    )?.title
                                  : "-"
                              }}</span
                            >
                            &emsp;{{ item?.xupdateTime }}
                          </div>
                        </template>
                      </a-timeline-item>
                    </a-timeline>
                  </a-col>
                </template>
              </a-row>
            </div>
            <div v-if="pageState.detailsData?.logs?.length" class="cardView">
              <div class="title">处理记录</div>
              <a-timeline :style="{ marginRight: '40px', marginLeft: '8px' }">
                <a-timeline-item
                  v-for="item in pageState.detailsData?.logs"
                  :key="item?.xid"
                  :label="item?.action"
                >
                  <template #dot>
                    <icon-check-circle-fill />
                  </template>
                  {{ item?.comment ?? "-" }}
                  <template #label>
                    <div class="tipText">
                      <span class="text">{{ item?.userName ?? "-" }}</span>
                      <span class="time">{{ item?.xcreateTime }}</span>
                    </div>
                  </template>
                </a-timeline-item>
              </a-timeline>
            </div>
          </a-spin>
        </div>
      </div>
    </a-scrollbar>
    <!-- 操作按钮 -->
    <div v-if="pageState.list?.length" class="footer">
      <div class="footerView">
        <div>
          <a-pagination
            :total="pageState.total"
            :page-size="pageState.pagination.pageSize"
            :current="pageState.pagination.pageNum"
            size="mini"
            @change="handlePageChange"
            @page-size-change="handlePageSizeChange"
          />
        </div>
        <a-space>
          <a-button
            v-if="isShowSuspended"
            :loading="pageState.loadingTask"
            type="primary"
            status="warning"
            @click="updateTaskStatus"
            >挂起</a-button
          >
          <a-button
            v-if="
              [
                WorkOrderStatusEnum.PENDING,
                WorkOrderStatusEnum.SUSPENDED,
              ]?.includes(pageState.detailsData?.status as WorkOrderStatusEnum)
            "
            type="primary"
            @click="handlePassOn"
            >转交他人</a-button
          >
          <a-button
            v-if="
              [
                WorkOrderStatusEnum.PENDING,
                WorkOrderStatusEnum.SUSPENDED,
              ]?.includes(pageState.detailsData?.status as WorkOrderStatusEnum)
            "
            :loading="pageState.loadingComplete"
            type="primary"
            @click="handleComplete"
            >完成</a-button
          >
        </a-space>
      </div>
    </div>
    <!-- 转移他人 -->
    <a-modal
      v-model:visible="pageState.visible"
      :width="680"
      title="请选择转交人员"
      @before-ok="handleShift"
    >
      <a-table
        v-model:selected-keys="selectedKeys"
        :loading="pageState.loadingTable"
        row-key="doctorUserId"
        :scroll="{ y: 200 }"
        :columns="doctorColumn"
        :data="pageState.doctorList"
        :row-selection="rowSelection"
        :pagination="false"
      />
    </a-modal>
  </div>
</template>

<style lang="scss" scoped>
.conainer {
  width: 100%;
  height: calc(100vh - 180px);
  display: grid;
  grid-template-columns: 340px 1fr;

  .left {
    width: 320px;
    .active {
      border: 1px solid #0052d9 !important;
    }
    .userCard {
      padding: 16px;
      padding-bottom: 12px;
      border-radius: 4px;
      background: #f9fbff;
      border: 1px solid rgba(134, 144, 156, 0.1);
      margin-bottom: 16px;
      .topSty {
        display: flex;
        align-items: center;
        color: #3d3d3d;
        font-size: 16px;
        font-weight: bold;
        padding-bottom: 12px;
        // border-bottom: 1px solid #e5ecfc;
        // margin-bottom: 12px;
        > img {
          width: 20px;
          height: 20px;
          margin-right: 8px;
        }
      }
      .tagView {
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }
    }
  }
}

.footerView {
  display: flex;
  justify-content: space-between;
}

.cardView {
  background: white;
  margin-bottom: 12px;
  .title {
    color: #000;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
  }

  .tipText {
    padding: 12px;
    border-radius: 4px;
    background: #f9fbff;
    > span:first-child {
      color: #1d2129;
      font-size: 12px;
      margin-right: 12px;
    }
  }
}
</style>
