<!--
* @description 工单已完成
* @fileName completeItem.vue
* <AUTHOR>
* @date 2025/07/12 01:25:10
!-->
<script setup lang="ts">
import { reactive, watch } from "vue";
import { getImageSrc } from "@/utils";
import * as apis from "@/batchApi";
import { WorkOrderStatusEnum } from "@/views/work-order-manage/config";
import to from "await-to-js";

type Props = {
  activeKey: WorkOrderStatusEnum.PENDING | WorkOrderStatusEnum.COMPLETED;
};

interface IState {
  loading: boolean;
  pagination: { pageNum: number; pageSize: number };
  list: apis.GetTicketLogsUsingPOSTTicketLogResVo[];
  total: number;
}

const defaultPagination = { pageNum: 1, pageSize: 10 };
const pageState = reactive<IState>({
  loading: false,
  pagination: defaultPagination,
  list: [],
  total: 0,
});
const props = defineProps<Props>();

const getList = async () => {
  pageState.loading = true;
  const [err, res] = await to(
    apis.postBdManageApiTicketLogMy({
      pageNum: pageState.pagination.pageNum,
      pageSize: pageState.pagination.pageSize,
    }),
  );
  pageState.loading = false;
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    pageState.list = res?.data as IState["list"];
    pageState.total = res?.total as number;
  }
};

watch(
  () => props.activeKey,
  () => {
    if (props.activeKey === WorkOrderStatusEnum.COMPLETED) {
      getList();
    }
  },
);

const format = (gender: number | undefined) => {
  if (!gender) return "未知";
  return gender === 1 ? "男" : gender === 2 ? "女" : "未知";
};

const handlePageChange = (current: number) => {
  pageState.pagination.pageNum = current;
  getList();
};

const handlePageSizeChange = (pageSize: number) => {
  pageState.pagination.pageSize = pageSize;
  getList();
};
</script>

<template>
  <div>
    <a-spin style="width: 100%" :loading="pageState.loading" tip="加载中...">
      <a-empty v-if="!pageState.list?.length" />
      <a-scrollbar
        class="content"
        style="height: calc(100vh - 180px); overflow: auto; margin-bottom: 16px"
      >
        <div
          v-for="item in pageState.list"
          :key="item?.xid"
          class="expertConsultationItemView"
        >
          <div class="infoView">
            <div class="baseInfo">
              <img :src="getImageSrc('peopleMan.png')" />
              <span
                >{{ item?.customerName }} {{ format(item?.gender) }}
                {{ item?.contactMobile }}</span
              >
            </div>
            <div class="otherIngo">
              <span class="labelText">处理人</span>
              <span>{{ item?.userName ?? "-" }}</span>
              <span style="margin-left: 52px">
                <label class="labelText">任务完成时间</label>
                {{ item?.xcreateTime ?? "-" }}
              </span>
              <span style="margin-left: 52px"
                ><label class="labelText">处理结果</label
                >{{ item?.comment }}</span
              >
            </div>
          </div>
        </div>
      </a-scrollbar>
      <div class="paginationView">
        <a-pagination
          :total="pageState.total"
          :page-size="pageState.pagination.pageSize"
          :current="pageState.pagination.pageNum"
          size="medium"
          show-total
          show-jumper
          show-page-size
          @change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        />
      </div>
    </a-spin>
  </div>
</template>

<style lang="scss" scoped>
.expertConsultationItemView {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f9fbff;
  border-radius: 8px;
  padding: 16px 20px;
  margin-bottom: 16px;

  &::after {
    content: "";
    width: 4px;
    height: 16px;
    background: #0052d9;
    position: absolute;
    top: 20px;
    left: 0;
  }

  .infoView {
    .baseInfo {
      font-size: 14px;
      font-weight: bold;
      color: #3d3d3d;
      display: flex;
      align-items: center;

      > img {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }

      .adress {
        color: #0052d9;
        margin-left: 60px;
      }
    }
  }

  .otherIngo {
    font-size: 12px;
    color: #000000;
    margin-top: 12px;

    .labelText {
      color: #86909c;
      margin-right: 12px;
    }
  }
}

.paginationView {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
</style>
