<!--
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2024-12-12 18:11:20
 * @LastEditors: yangrong<PERSON>
 * @LastEditTime: 2025-04-30 10:39:40
-->
<template>
  <div class="table-filter">
    <a-button
      type="primary"
      @click="visible = true"
    >
      <template #icon>
        <icon-plus />
      </template>
      <template #default>
        书写文书
      </template>
    </a-button>
    <a-form
      :model="form"
      layout="inline"
    >
      <a-form-item field="typeId" label="文书类型">
        <a-select v-model="form.typeId" :style="{width:'180px'}" placeholder="请选择" allow-clear>
          <a-option
            v-for="_type, _key in typesArr"
            :key="_key"
            :value="_type.typeId"
          >
            {{ _type.typeName }}
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="writeStatus" label="书写状态">
        <a-select v-model="form.writeStatus" :style="{width:'180px'}" placeholder="请选择" allow-clear>
          <a-option :value="EWriteStatus.FINISH">已完成</a-option>
          <a-option :value="EWriteStatus.UN_FINISH">待完成</a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="pushStatus" label="推送状态">
        <a-select v-model="form.pushStatus" :style="{width:'180px'}" placeholder="请选择" allow-clear>
          <a-option :value="EPushStatus.WAIT">待推送</a-option>
          <a-option :value="EPushStatus.FINISH">已推送</a-option>
          <a-option :value="EPushStatus.FAILED">推送失败</a-option>
        </a-select>
      </a-form-item>
    </a-form>
  </div>
  <!-- 表格以外的其他元素 -->
  <a-modal
    v-model:visible="visible"
    :on-before-ok="handleBeforeOk"
    @ok="handleOk"
    @cancel="visible = false"
  >
    <template #title>
      请选择文书模板
    </template>
    <a-form
      ref="modalFormRef"
      :model="modalForm"
    >
      <a-form-item
        field="templateId"
        label="请选择"
        :rules="[{ required: true, message: '请选择' }]"
      >
        <a-select
          v-model="modalForm.templateId"
          :style="{width:'320px'}"
          placeholder="请选择类型"
        >
          <a-option
            v-for="_type, _key in templateList"
            :key="_key"
            :value="_type.templateId"
          >
            <!-- 展示对应的业务的名称 -->
            {{ _type.businessName }}
          </a-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>

</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { postBdManageApiDocQueryDocTemplateList } from '@/batchApi';
import {
  EWriteStatus,
  EPushStatus,
  EPaperStatus
} from '@/types/enums'
import { EOprType } from '../form-editor.vue';

const router = useRouter()

const modalFormRef = ref();
const visible = ref(false)
const form = reactive({
  typeId: undefined,
  writeStatus: undefined,
  pushStatus: undefined
});

const templateList = ref();

const modalForm = reactive({
  templateId: ''
})

const props = defineProps([
  'typesArr'
])

const emits = defineEmits([
  'tableFilter'
])

console.log('_props_', props)

watch([
  () => form.writeStatus,
  () => form.pushStatus,
  () => form.typeId
], () => {
  emits('tableFilter', {
    typeId: form.typeId,
    writeStatus: form.writeStatus,
    pushStatus: form.pushStatus
  })
})

// 弹窗点击确定的前置事件
const handleBeforeOk = async () => {
  const errors = await modalFormRef.value.validate();
  console.log('errors', errors)
  if (!errors) {
    return true
  }
  return false;
}

// 前置事件执行成功后执行
const handleOk = () => {
  const {
    templateId
  } = modalForm
  console.log('templateId', templateId);
  const templateEle = templateList.value.find((_fi: any) => _fi.templateId === templateId);
  router.push({
    name: 'paperwork-editor-content',
    query: {
      subcategory: templateEle.typeId,
      subcategoryName: templateEle.typeName,
      business: templateEle.businessId,
      businessName: templateEle.businessName,
      type: EOprType.Add,
      // 表单相关的数据 - 部分通过后端接口进行返回
      formId: templateEle?.formId,
      schemaId: templateEle?.schemaId,
      templateId,
      env: import.meta.env.VITE_FORM_ENV ?? '1', // 当前是开发环境的标识
    }
  })
}

const init = () => {
  postBdManageApiDocQueryDocTemplateList({
    status: EPaperStatus.ENABELE
  }).then(_res => {
    if (
      Array.isArray(_res.data)
    ) {
      templateList.value = _res.data
    }
  })
}

onMounted(() => {
  init() 
})

</script>

<style lang="scss" scoped>
.table-filter {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  :deep(.arco-form-layout-inline .arco-form-item) {
    margin-bottom: 0px !important;
  }
}
</style>