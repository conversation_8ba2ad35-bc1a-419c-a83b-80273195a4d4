<!--
 * @Description: 用于其他服务进行调用的文书待办列表
 * @Author: yangrongxin
 * @Date: 2025-06-09 15:39:44
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-06-19 14:22:36
-->
<template>
  <div
    v-if="typeList.length !== 0"
    class="external-todo-items-container"
  >
    <!-- 左边的文书类型的展示 -->
    <div class="type-container">
      <div
        v-for="_item, _index in typeList"
        :key="_index"
        class="type-item"
        :class="{
          active: _item?.formId === formId && _item?.schemaId === schemaId && _item?.templateId === templateId
        }"
        @click="handleChoose(_item)"
      >
        <span>
          {{ _item.articleTypeName }}
        </span>
      </div>
    </div>
    <div class="evaluate-item-wrapper">
      <div class="evaluate-item-container">
        <evaluate-item
          v-if="formId && schemaId"
          ref="formIframeRef"
          name="测试业务"
          :uuid="formId + schemaId + contentId + patientId"
          :patient-id="patientId"
          :form-id="formId"
          :schema-id="schemaId"
          :content-id="contentId"
          :custom-submit="true"
          opt-type="edit"
        />
        <div
          v-else
          class="evaluate-item-empty-container"
        >
          <a-empty description="请选中一条待办记录" />
        </div>
      </div>
      <!-- 底部的操作按钮 -->
      <div class="external-todo-items-operating-buttons">
        <a-button
          type="primary"
          :disabled="disabledBtn"
          :loading="saveLoading"
          @click="handleSave"
        >
          保存
        </a-button>
        <a-button
          type="primary"
          :disabled="disabledBtn"
          :loading="saveLoading"
          @click="handleSaveAndPush"
        >
          保存并推送
        </a-button>
      </div>
    </div>
  </div>
  <div
    v-else
    class="external-todo-items-empty"
  >
    <a-empty description="该患者暂无待办数据" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import { PageUsingPOSTToDo, postBdManageApiInternetDocAdd, postBdManageApiPatientDocAdd, postBdManageApiPatientDocUpdateById, postBdManageApiTodoPage } from '@/batchApi';
import { addFormContent } from '@/api/patients';
import getPdfPath from '@/utils/form';
import { setToken } from '@/utils/auth';
import evaluateItem from '../patient-center/components/evaluate-item/index.vue';

const route = useRoute();
// 调用获取当前表单的数据
const formIframeRef = ref();
// 表单id 769021034736124753
const formId = ref('');
// 表单结构id 624165581746078042
const schemaId = ref('');
// 模板id
const templateId = ref('');
// 内容id
const contentId = ref();
// 表单的数据id
const documentId = ref();

// 按钮的加载状态管理
const saveLoading = ref(false);
const patientId = ref();

// 类型列表
const typeList = ref<PageUsingPOSTToDo[]>([]);
// const id = route.query?.id;
const orderId = route.query?.orderId ?? '123321';
// const patientId = route.query?.patientId ?? '1921888276380205056'
const accountId = route.query?.accountId ?? '2dd6a369-076f-ef11-90f6-005056bb97ac'
console.log('route', route.query)

const disabledBtn = computed(() => {
  return !patientId.value 
})

const handleChoose = (_item: PageUsingPOSTToDo) => {
  formId.value = _item.formId as unknown as string;
  schemaId.value = _item.schemaId as unknown as string;
  templateId.value = _item.templateId as unknown as string;
  contentId.value = _item.contentId as unknown as string;
  documentId.value = _item.docId as unknown as string;
  patientId.value = _item.patientId as unknown as string;
}

const handleSave = () => {
  handleSaveContent();
}

const handleSaveAndPush = () => {
  handleSaveContent(true);
}

const handleSaveContent = (
  // 是否进行推送
  _isPush = false,
) => {
  if (
    !accountId
  ) {
    Message.info('请选择一个患者！')
    return;
  } 

  if (
    typeof formId.value !== 'string' ||
    formId.value === '' ||
    typeof schemaId.value !== 'string' ||
    schemaId.value === '' ||
    typeof templateId.value !== 'string' ||
    templateId.value === ''
  ) {
    return 
  }

  saveLoading.value = true;
  formIframeRef.value?.submitForm((
    _values: any
  ) => {
    if (
      _values === false
    ) {
      return 
    }
    let templateName = '未编写';
    try {
      const clericalCover0Value = JSON.parse(_values?.clericalCover0);
      templateName = `${clericalCover0Value.title}${clericalCover0Value.subTitle}`      
    } catch (error) {
      console.error('error', error)
    }
    addFormContent({
      formId: formId.value,
      schemaId: schemaId.value,
      content: JSON.stringify(_values)
    }).then(_formRes => {
      if (
        typeof _formRes?.data?.contentId === 'string' &&
        _formRes?.data?.contentId !== ''
      ) {
        invokeSave(
          formId.value,
          schemaId.value,
          templateId.value,
          _formRes?.data?.contentId,
          templateName,
          _isPush,
          false
        )
      }
    })
  })
}

const invokeSave = (
  formId = '',
  schemaId = '',
  templateId = '',
  contentId = '',
  templateName = '',
  _isSubmit = false,
  _isRedis = false
) => {
  let postBody;
  if (
    // 推送的时候 需要生成一下pdf路径
    _isSubmit
  ) {
    postBody = getPdfPath({
      formId,
      schemaId,
      contentId,
      patientId: patientId.value as string
    }, true)
    console.log('postBody', postBody);
  }
  if (
    typeof documentId.value === 'string' &&
    documentId.value !== ''
  ) {
    postBdManageApiPatientDocUpdateById({
      contentId: contentId as unknown as number,
      formId: formId as unknown as number,
      schemaId: schemaId as unknown as number,
      templateId: templateId as unknown as number,
      isPush: _isSubmit,
      isRedis: _isRedis,
      patientId: patientId.value as unknown as number,
      id: documentId.value as unknown as number,
      name: templateName,
      orderId: orderId as unknown as number,
      postBody
    }).then((_res) => {
      // handleReturn()
      console.log('_res', _res)
    }).finally(() => {
      saveLoading.value = false;
      getToDoPage();
    })
  } else {
    postBdManageApiInternetDocAdd({
      contentId: contentId as unknown as number,
      /** 表单ID */
      formId: formId as unknown as number,
      /** 结构ID */
      schemaId: schemaId as unknown as number,
      /** 文书模板ID */
      templateId: templateId as unknown as number,
      /** 是否推送(0.否1.是) */
      isPush: _isSubmit,
      isRedis: _isRedis,
      /** 患者ID */
      patientId: patientId.value as unknown as number,
      name: templateName,
      // 订单id
      orderId: orderId as unknown as number,
      postBody      
    }).then(_res => {
      // handleReturn()
      console.log('_res', _res)
    }).finally(() => {
      saveLoading.value = false;
      getToDoPage();
    })
  }
}

const getToDoPage = () => {
  // 调用接口获取数据的类型
  postBdManageApiTodoPage({
    pageNum: 1,
    pageSize: 999,
    query: {
      // patientId: patientId as unknown as number,
      accountId: accountId as unknown as number,
      type: 1,
      status: 0
    }
  }).then(_res => {
    if (
      Array.isArray(_res.data?.content)
    ) {
      typeList.value = _res.data?.content.filter(_fi => _fi.formId);
    }
  })
}

onMounted(() => {
  // 如果当前页面有token 存入最新的token 再进行请求
  const token = route.query?.token ?? '';
  if (
    typeof token === 'string' &&
    token !== ''
  ) {
    setToken(token);
  }
  getToDoPage()
})

</script>

<style scoped lang="scss">
.external-todo-items-container {
  display: flex;
  min-height: 100vh;
  overflow-x: auto;
  overflow-y: hidden;
  border-top: 1px solid var(--border-split-color);
  >.type-container {
    width: 160px;
    flex: none;
    border-right: 1px solid var(--border-split-color);
    // background-color: red;
    // 每一个类型的元素
    >.type-item {
      display: flex;
      align-items: center;
      cursor: pointer;
      &:not(:first-child) {
        margin-top: var(--spacing-6);
      }
      height: 38px;
      >span {
        color: #000;
        font-size: var(--font-size-title-1);
        margin-left: var(--spacing-7);
      }
      &.active {
        >span {
          color: var(--primary-color);
          font-weight: bold;
        }
        background-color: var(--card-item-bg-color);
        &::before {
          display: block;
          content: '';
          width: 4px;
          height: 38px;
          background-color: var(--primary-color);
        }
      }
    }
  }
  >.evaluate-item-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    >.evaluate-item-container {
      flex: 1;
      min-width: 736px;
      // 展示为空
      >.evaluate-item-empty-container {
        display: flex;
        align-items: center;
        height: 100%;
      }
    }
    >.external-todo-items-operating-buttons {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      column-gap: var(--spacing-7);
      height: 64px;
      padding: 0 var(--spacing-7);
      border-top: 1px solid var(--border-split-color);
    }
  }
}

.external-todo-items-empty {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
// .external-todo-items-wrapper {
//   display: flex;
//   flex-direction: column;
  
//   >.external-todo-items-operating-buttons {
//     border-top: 
//     height: 64px;
//   }
// }

</style>