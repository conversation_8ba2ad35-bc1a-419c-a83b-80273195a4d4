<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-12-13 09:23:24
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-26 23:13:37
-->
<template>
  <div class="form-editor-container">
    <div class="form-editor-toolBar">
      <div class="types">
        <div class="item">
          <span>所属分类：</span>
          <span>{{ query.subcategoryName }}</span>
        </div>
        <div class="item">
          <span>关联的业务：</span>
          <span>{{ query.businessName }}</span>
        </div>
      </div>
      <div class="tools">
        <a-button @click="handleReturn">
          <template #icon>
            <icon-arrow-left />
          </template>
          <template #default>返回</template>
        </a-button>
        <a-button
          v-if="query.type !== EOprType.View"
          type="primary"
          :loading="tsLoading"
          @click="handleTemporaryStorage"
        >
          <template #icon>
            <icon-save />
          </template>
          <template #default>暂存</template>
        </a-button>
        <a-button
          v-if="query.type !== EOprType.View"
          type="primary"
          :loading="loading"
          @click="handleSave(false)"
        >
          <template #icon>
            <icon-save />
          </template>
          <template #default>保存</template>
        </a-button>
        <a-button
          v-if="query.type !== EOprType.View"
          type="primary"
          :loading="loading"
          @click="handleSave(true)"
        >
          <template #icon>
            <icon-save />
          </template>
          <template #default>保存并推送</template>
        </a-button>
      </div>
    </div>
    <!-- 增加一个用于进行患者 -->
    <div class="form-patient-info">
      <bd-user-select
        v-model="patientInfo"
        :default-patient-id="query.patientId"
        :disabled="query?.contentId"
      >
        <template #default="{ data }">
          <div v-if="data" class="patient-base-info">
            <span>{{ data?.name }}</span>
            <span>{{ data?.age }}岁</span>
            <span>{{ String(data?.gender) === "1" ? "男" : "女" }}</span>
            <span>{{ data?.idCard }}</span>
            <span>{{ data?.contactMobile }}</span>
          </div>
          <div v-else class="patient-base-info">请选择一个患者</div>
        </template>
      </bd-user-select>
    </div>
    <evaluate-item
      v-if="patientInfo?.patientId"
      ref="formIframeRef"
      :name="query?.subcategoryName"
      :uuid="query?.formId ?? '' + query?.schemaId ?? ''"
      :patient-id="patientInfo.patientId"
      :form-id="query?.formId"
      :schema-id="query?.schemaId"
      :content-id="query?.contentId ?? undefined"
      :opt-type="query.type"
      :custom-submit="true"
    />
    <div v-else class="iframeEmpty">请选择一个患者</div>
  </div>
</template>

<script setup lang="ts">
import {
  postBdManageApiDocCreateOrUpdateTemplate,
  postBdManageApiPatientDocAdd,
  postBdManageApiPatientDocUpdateById,
} from "@/batchApi";
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { Message } from "@arco-design/web-vue";
import getPdfPath from "@/utils/form";
import bdUserSelect from "@/components/bd-user-select/index.vue";
import { addFormContent } from "@/api/patients";
import evaluateItem from "../patient-center/components/evaluate-item/index.vue";

const postMsgType = "HxFormEngine";
const iframeUrl = ref();
// const addUrl = ref('http://localhost:8808/#/design');
// 编辑入口
// const editUrl = ref("http://localhost:8808/#/design?formId=707887111331646550&schemaId=707887111331646551&formVersion=1&formName=%E6%A0%87%E5%87%86%E6%96%87%E4%B9%A6&contentId=");
const route = useRoute();
const router = useRouter();
const formIframeRef = ref();
const tsLoading = ref(false);
const loading = ref(false);
const patientInfo = ref();

const { query } = route;

// 返回到上一个页面
const handleReturn = () => {
  router.back();
};

// 暂存当前的模板数据
const handleTemporaryStorage = () => {
  handleSave(false, true);
};

// 保存当前的模板数据
const handleSave = (_isPush = false, _isRedis = false) => {
  console.log("_query", query);

  if (!patientInfo.value) {
    Message.info("请先选择一个患者！");
    return;
  }

  const formId = query?.formId ?? "";
  const schemaId = query?.schemaId ?? "";
  const templateId = query?.templateId ?? "";

  if (
    typeof formId !== "string" ||
    formId === "" ||
    typeof schemaId !== "string" ||
    schemaId === "" ||
    typeof templateId !== "string" ||
    templateId === ""
  ) {
    return;
  }

  formIframeRef.value?.submitForm((_values: any) => {
    // 这个地方的内容 不应该可以触发两次
    if (_values === false) {
      loading.value = false;
      return;
    }
    console.log("_values", _values);
    console.log("_query", query);
    // 获取编写的时候 对应的模板名称 如果没有编写文书模板的名称 则设置为未编写
    let templateName = "未编写";
    try {
      const clericalCover0Value = JSON.parse(_values?.clericalCover0);
      if (
        typeof clericalCover0Value.title === 'string' &&
        clericalCover0Value.title !== '' &&
        clericalCover0Value.title !== 'undefined' &&
        typeof clericalCover0Value.subTitle === 'string' &&
        clericalCover0Value.subTitle !== 'undefined' &&
        clericalCover0Value.subTitle !== ''
      ) {
        templateName = `${clericalCover0Value.title}${clericalCover0Value.subTitle}`;
      }
    } catch (error) {
      console.error("error", error);
    }
    addFormContent({
      formId,
      schemaId,
      content: JSON.stringify(_values),
    }).then(_formRes => {
      if (
        typeof _formRes?.data?.contentId === "string" &&
        _formRes?.data?.contentId !== ""
      ) {
        invokeSave(
          formId,
          schemaId,
          templateId,
          _formRes?.data?.contentId,
          templateName,
          _isPush,
          _isRedis,
        );
      }
    });
    console.log("form_data", _values);
  });
  loading.value = true;
};

const invokeSave = (
  formId = "",
  schemaId = "",
  templateId = "",
  contentId = "",
  templateName = "",
  _isSubmit = false,
  _isRedis = false,
) => {
  // 获取当前数据的id
  const { id } = query;
  let postBody;
  if (
    // 推送的时候 需要生成一下pdf路径
    _isSubmit
  ) {
    postBody = getPdfPath(
      {
        formId,
        schemaId,
        contentId,
        patientId: patientInfo.value?.patientId,
      },
      true,
    );
    console.log("postBody", postBody);
  }
  const orderId = query?.orderId ?? "";

  if (typeof id === "string" && id !== "") {
    postBdManageApiPatientDocUpdateById({
      contentId: contentId as unknown as number,
      formId: formId as unknown as number,
      schemaId: schemaId as unknown as number,
      templateId: templateId as unknown as number,
      orderId: orderId as unknown as string,
      isPush: _isSubmit,
      isRedis: _isRedis,
      patientId: patientInfo.value?.patientId,
      id: id as unknown as number,
      name: templateName,
      postBody,
    })
      .then(_res => {
        handleReturn();
        console.log("_res", _res);
      })
      .finally(() => {
        loading.value = false;
      });
  } else {
    postBdManageApiPatientDocAdd({
      contentId: contentId as unknown as number,
      /** 表单ID */
      formId: formId as unknown as number,
      /** 结构ID */
      schemaId: schemaId as unknown as number,
      /** 文书模板ID */
      templateId: templateId as unknown as number,
      /** 是否推送(0.否1.是) */
      isPush: _isSubmit,
      isRedis: _isRedis,
      /** 患者ID */
      patientId: patientInfo.value?.patientId,
      name: templateName,
      postBody,
    })
      .then(_res => {
        handleReturn();
        console.log("_res", _res);
      })
      .finally(() => {
        loading.value = false;
      });
  }
};
</script>

<script lang="ts">
export const enum EOprType {
  Add = "add",
  Edit = "edit",
  View = "view",
}
</script>

<style lang="scss" scoped>
.form-editor-container {
  height: 100%;
  background-color: white;
  // 第三方平台中的操作栏
  .form-editor-toolBar {
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-split-color);
    > .types {
      display: flex;
      gap: var(--spacing-10);
      > .item {
        > span:nth-of-type(1) {
          font-weight: bold;
        }
      }
    }
    > .tools {
      display: flex;
      gap: 16px;
    }
  }
  // 当前选择患者数据的操作栏
  .form-patient-info {
    padding: var(--spacing-7) 0;
    border-bottom: 1px solid var(--border-split-color);
    // bd-user-select 组件内部的样式
    > div {
      display: flex;
      flex-direction: row-reverse;
      justify-content: space-between;
      background-color: white;
      padding: 0 var(--spacing-7);
      :deep(.arco-input-search) {
        width: 500px !important;
        background-color: white;
      }
      > .patient-base-info {
        font-weight: bold;
        display: flex;
        align-items: center;
        column-gap: var(--spacing-7);
      }
    }
  }
  > iframe {
    width: 100%;
    height: calc(100% - 150px);
  }
  .iframeEmpty {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }
}
</style>
