<!--
 * @Description: 需要进行编辑的表单
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-04-27 14:37:05
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-26 14:34:38
-->
<template>
  <div class="table-wrapper">
    <!-- 新增模板处理 -->
    <table-filter
      :types-arr="typeArr"
      @table-filter="getTemplateList"
    />
    <!-- 表格的内容 -->
    <div class="table-container">
      <a-table
        :data="tableData"
        :pagination="false"
        :loading="loading"
        :scroll="scroll"
        scrollbar
      >
        <template #columns>
          <a-table-column
            title="序号"
            :width="65"
          >
            <template #cell="{ rowIndex }">
              <span>{{ rowIndex + 1 }}</span>
            </template>
          </a-table-column>
          <a-table-column
            title="文书类型"
            data-index="typeName"
            :width="120"
          ></a-table-column>
          <a-table-column
            title="客户姓名"
            data-index="patientName"
            :width="120"
          ></a-table-column>
          <a-table-column
            title="文书名称"
            data-index="name"
            :width="120"
          ></a-table-column>
          <a-table-column
            title="年龄"
            data-index="age"
            :width="65"
          ></a-table-column>
          <a-table-column
            title="联系方式"
            data-index="phone"
            :width="120"
          ></a-table-column>
          <a-table-column
            title="所属业务"
            data-index="businessName"
            :width="120"
          ></a-table-column>
          <a-table-column
            title="查看次数"
            data-index="viewCount"
            :width="90"
          ></a-table-column>
          <a-table-column
            title="下载次数"
            data-index="downloadCount"
            :width="90"
          ></a-table-column>
          <a-table-column
            title="书写状态"
            data-index="writeStatus"
            :width="90"
          >
            <template #cell="{ record }">
              <span v-if="record.writeStatus === EWriteStatus.UN_FINISH" :style="{ color: '#F53F3F' }">待完成</span>
              <span v-else-if="record.writeStatus === EWriteStatus.FINISH" :style="{ color: '#00B42A' }">已完成</span>
            </template>
          </a-table-column>
          <a-table-column
            title="推送状态"
            data-index="pushStatus"
            :width="90"
          >
            <template #cell="{ record }">
              <span v-if="record.pushStatus === EPushStatus.FAILED" :style="{ color: '#F53F3F' }">推送失败</span>
              <span v-else-if="record.pushStatus === EPushStatus.FINISH" :style="{ color: '#00B42A' }">已推送</span>
              <span v-else-if="record.pushStatus === EPushStatus.WAIT" :style="{ color: '#666' }">待推送</span>
            </template>
          </a-table-column>
          <a-table-column
            title="书写完成时间"
            data-index="writeCompleteDate"
            :width="120"
          >
            <template #cell="{ record }">
              <span>{{ record.writeCompleteDate ? dayjs(record.writeCompleteDate).format('YYYY-MM-DD') : '' }}</span>
            </template>
          </a-table-column>
          <a-table-column
            title="推送时间"
            data-index="pushDate"
            :width="120"
          >
            <template #cell="{ record }">
              <span>{{ record.pushDate ? dayjs(record.pushDate).format('YYYY-MM-DD') : '' }}</span>
            </template>
          </a-table-column>
          <a-table-column
            title="操作"
            :width="200"
          >
            <template #cell="{ record }">
              <div class="table-operate">
                <!-- 只要是未推送的数据都可以进行编辑 -->
                <a-button
                  v-if="record.pushStatus !== EPushStatus.FINISH"
                  size="mini"
                  type="outline"
                  @click="handleEdit(record)"
                >
                  编辑
                </a-button>
                <!-- 已经书写完成 并且 未推送的可以进行推送 -->
                <a-popconfirm
                  content="确认要向患者推送此条记录?"
                  type="info"
                  :on-before-ok="(done) => handlePush(record, done)"
                >
                  <a-button
                    v-if="record.writeStatus === EWriteStatus.FINISH && record.pushStatus !== EPushStatus.FINISH"
                    size="mini"
                    type="outline"
                  >
                    推送
                  </a-button>
                </a-popconfirm>
                <!-- 已经推送成功 只能进行查看 -->
                <a-button
                  v-if="record.pushStatus === EPushStatus.FINISH"
                  size="mini"
                  type="outline"
                  @click="handleViewForm(record)"
                >
                  查看
                </a-button>
                <a-button
                  v-if="record.pushStatus === EPushStatus.FINISH"
                  size="mini"
                  type="outline"
                  @click="handleCopyForm(record)"
                >
                  复制
                </a-button>
              </div>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import {
  EWriteStatus,
  EPushStatus
} from '@/types/enums'
import getPdfPath, {
  IRecordProps
} from '@/utils/form';
import { useClipboard } from '@vueuse/core';
import { getBdManageApiPatientDocQueryById, postBdManageApiDocCreateOrUpdateTemplate, postBdManageApiDocGetDocTypeBusiness, postBdManageApiDocGetDocTypeBusinessResponse, postBdManageApiDocQueryDocTemplateList, postBdManageApiDocQueryDocTemplateListResponse, postBdManageApiPatientDocPushById, postBdManageApiPatientDocQueryByParams, QueryByParamsUsingPOSTWenShuChaXunFanCanXinXi } from '@/batchApi';
import tableFilter from './components/table-filter.vue';
import { EOprType } from './form-editor.vue';
import { Message } from '@arco-design/web-vue';

const { copy } = useClipboard();
const loading = ref(false);
const tableData = ref<postBdManageApiDocQueryDocTemplateListResponse['data']>([]);
const typeArr = ref<postBdManageApiDocGetDocTypeBusinessResponse['data']>([]);
const router = useRouter();

const scroll = ref({
  y: '100%'
})

// 获取一个表单的pdf的下载路径
// const getPdfPath = (_record: QueryByParamsUsingPOSTWenShuChaXunFanCanXinXi): string => {
//   if (
//     !_record
//   ) {
//     return ''
//   }
//   const inlineFormId = _record.formId ?? '679950718165057541'; 
//   const inlineSchemaId = _record.schemaId ?? '689740248086744574';
//   const baseParams: any = {
//     formId: inlineFormId,
//     schemaId: inlineSchemaId,
//     env: import.meta.env.VITE_FORM_ENV ?? '1', // 当前是开发环境的标识
//     envType: 'web',
//     triggerPoint: 'outFormSubmit', // 外部表单提交的标识
//     uuid: `${inlineFormId}${inlineSchemaId}`,
//     patientId: _record.patientId,
//     oprtType: 'view',
//     contentId: _record.contentId,
//   }
//   const token = localStorage.getItem('token');
//   if (
//     typeof token === 'string' &&
//     token !== ''
//   ) {
//     baseParams.token = token;
//   }
//   const searchParams = new URLSearchParams(baseParams);
//   const pdfPath = `${import.meta.env.VITE_FORM_BASE_URL}/#/formInstance?${searchParams.toString()}`
  
//   return pdfPath;
// }

// 复制表单的内容
const handleCopyForm = (
  _record: QueryByParamsUsingPOSTWenShuChaXunFanCanXinXi
) => {
  debugger
  const _path = getPdfPath({
    formId: _record.formId as unknown as string,
    schemaId: _record.schemaId as unknown as string,
    patientId: _record.patientId as unknown as string,
    contentId: _record.contentId as unknown as string ?? undefined
  }, true)
  copy(window.location.origin + _path);
  Message.success("复制成功！")
}

// 进行文书的推送
const handlePush = (
  _record: QueryByParamsUsingPOSTWenShuChaXunFanCanXinXi,
  _done?: (closed: boolean) => void
) => {
  if (
    typeof _record.id === 'string' &&
    _record.id !== ''
  ) {
    const pdfPath = getPdfPath(_record as IRecordProps, true);
    postBdManageApiPatientDocPushById({
      id: _record.id,
      orderId: _record.orderId,
      postBody: pdfPath
    }).then(_res => {
      console.log('_res', _res)
      // 重新查询列表的数据
      getTemplateList();
      if (
        typeof _done === 'function'
      ) {
        _done(true)
      }
    }).catch(() => {
      if (
        typeof _done === 'function'
      ) {
        _done(false)
      }
    })
  }
}

// 查看表单的结构
const handleViewForm = (
  _record: QueryByParamsUsingPOSTWenShuChaXunFanCanXinXi
) => {
  router.push({
    name: 'paperwork-editor-content',
    query: {
      formId: _record.formId,
      schemaId: _record.schemaId,
      contentId: _record.contentId,
      templateId: _record.templateId,
      orderId: _record.orderId,
      subcategoryName: _record.typeName,
      businessName: _record.businessName,
      patientId: _record.patientId,
      type: EOprType.View,
      // 当前是医生进行查看 而不是患者
      isPatient: '2'
    }
  })
}

const handleEdit = (
  _record: QueryByParamsUsingPOSTWenShuChaXunFanCanXinXi
) => {
  // 根据当前的id获取对应的数据
  getBdManageApiPatientDocQueryById({
    id: _record.id as unknown as number
  }).then(_res => {
    if (
      _res.data
    ) {
      router.push({
        name: 'paperwork-editor-content',
        query: {
          id: _record.id,
          formId: _record.formId,
          schemaId: _record.schemaId,
          contentId: _record.contentId,
          templateId: _res.data.templateId,
          orderId: _record.orderId,
          subcategoryName: _record.typeName,
          businessName: _record.businessName,
          patientId: _record.patientId,
          type: EOprType.Edit
        }
      })
    }
  })
  console.log('_record', _record)
}

const getInitEnums = () => {
  postBdManageApiDocGetDocTypeBusiness({})
    .then(_initRes => {
      if (
        Array.isArray(_initRes.data) &&
        _initRes.data.length !== 0
      ) {
        typeArr.value = _initRes.data
      }
      console.log('_initRes', _initRes)
    })
}

const getTemplateList = (_reqParams = {}) => {
  loading.value = true;
  postBdManageApiPatientDocQueryByParams(_reqParams).then(_res => {
    if (
      Array.isArray(_res.data)
    ) {
      tableData.value = _res.data
    }
    loading.value = false;
    console.log('_res', _res)
  })
}

onMounted(() => {
  getInitEnums();
  getTemplateList();
})

</script>

<style lang="scss" scoped>
.table-wrapper {
  background-color: white;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  .table-operate {
    display: flex;
    gap: 8px;
  }
  .table-container {
    overflow-y: scroll;
  }
}
</style>