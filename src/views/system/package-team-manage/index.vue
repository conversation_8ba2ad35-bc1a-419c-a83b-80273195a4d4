<!--
 * @Description: 服务包团队管理
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-07-15 11:39:26
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 16:31:36
-->
<template>
  <div class="package-team-manage-container">
    <tableFilter
      placeholder="请输入外部服务包名称"
      @search="handleSearch"
    >
      <template #operate>
        <a-button
          :loading="pageState.synchronizeLoading"
          type="primary"
          @click="handleSynchronize"
        >
          <template #icon> <icon-sync /> </template>同步信息</a-button
        >
      </template>
    </tableFilter>
    <div class="containerView">
      <a-table
        :loading="pageState.loading"
        row-key="xid"
        :columns="teamInfoColumn"
        :data="pageState.data"
        :pagination="pageState.isShowPagination || pagination"
        @page-change="(current: number) => (pagination.current = current)"
        @page-size-change="
          (pageSize: number) => (pagination.pageSize = pageSize)
        "
        @change="handleTableChange"
      >
        <!-- 家医来信 -->
        <template #operate="{ record }">
          <a-space>
            <a-button
              size="mini"
              type="outline"
              @click="handleView(record)"
              >查看</a-button
            >
            <a-button size="mini" type="outline" @click="handleEdit(record)">
              编辑
            </a-button>
          </a-space>
        </template>
      </a-table>
    </div>
    <!-- 新增编辑 -->
    <AddAndEditForm
      v-model:visible="pageState.visible"
      :type="pageState.type"
      :edit-id="pageState.editId"
    />
    <!-- 详情 -->
    <!-- <Details v-model:visible-look="pageState.visibleLook" /> -->
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive } from 'vue';
import {
  FormInstance,
  Message,
  PaginationProps,
  TableChangeExtra,
  TableData,
} from "@arco-design/web-vue";
import * as apis from "@/batchApi";
import to from 'await-to-js';
import { teamInfoColumn, ModalType, IModalType } from "../config";
import tableFilter from "../components/table-filter.vue";
import Details from "./details.vue";
import AddAndEditForm from "./addAndEditForm.vue";

type Record = any;

interface IState {
  loading: boolean;
  organLoading: boolean;
  synchronizeLoading: boolean;
  visible: boolean;
  isShowPagination: boolean;
  disabled: boolean;
  data: Record[];
  form: apis.postBdManageApiOrganUpdateRequest;
  type: Extract<IModalType, "add" | "edit" | "look">;
  organList: apis.DictTypeUsingGETSysDictData[];
  keyWord: string | undefined;
  // 查看编辑数据使用的id
  editId: number | undefined;
}

// eslint-disable-next-line no-undef
const pageState = reactive<IState>({
  loading: false,
  organLoading: false,
  synchronizeLoading: false,
  visible: false,
  disabled: false,
  isShowPagination: false,
  data: [{ name: "张三" }],
  organList: [],
  type: ModalType.add,
  form: {},
  keyWord: undefined,
  editId: undefined,
});

const pagination = reactive<PaginationProps>({
  size: "small",
  showTotal: true,
  showJumper: true,
  showPageSize: true,
  total: 0,
  current: 1,
  pageSize: 10,
});

const handleSearch = ({ keyWord }: { keyWord: string }) => {
  pageState.keyWord = keyWord;
  getList();
};

const handleSynchronize = async () => {
  pageState.synchronizeLoading = true;
  const [err, res] = await to(apis.getBdManageApiTeamEquityPackageSyncTeamEquityPackageInfo());
  pageState.synchronizeLoading = false;
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    Message.success("同步成功");
    getList();
  }
};

const handleTableChange = (_data: TableData, extra: TableChangeExtra) => {
  const { page, pageSize, type, filters } = extra || {};
  // 如果有filter条件隐藏分页
  if (type === "filter" && filters) {
    const hasValidArray = Object.values(filters)?.some(
      v => Array.isArray(v) && v.length > 0,
    );
    pageState.isShowPagination = hasValidArray;
  }
  if (type === "pagination") {
    pagination.current = page;
    pagination.pageSize = pageSize;
    getList();
  }
};

const handleEdit = (record: Record) => {
  pageState.editId = record?.id;
  pageState.type = ModalType.edit;
  pageState.visible = true;
};

const handleView = (record: Record) => {
  pageState.editId = record?.id;
  pageState.type = ModalType.look;
  pageState.visible = true;
}

const getList = async () => {
  pageState.loading = true;
  let teamName;
  if (
    typeof pageState.keyWord === 'string' &&
    pageState.keyWord !== ''
  ) {
    teamName = pageState.keyWord;
  }
  const [err, res] = await to(
    apis.postBdManageApiTeamEquityPackageQueryTeamEquityPackagePage({
      pageNumber: pagination.current,
      pageSize: pagination.pageSize,
      teamName
    }),
  );
  pageState.loading = false;
  if (err) {
    throw err;
  }
  if (res.code === "1") {
    pageState.data = res.data?.records as Record[];
    pagination.total = res?.data?.total;
  }
};

onMounted(() => {
  getList();
})

</script>

<style scoped lang="scss">
.containerView {
  background-color: white;
  padding: 16px;
  overflow: auto;
}

.package-team-manage-container {
  overflow-y: auto;
}
</style>