<!--
* @description 服务人员详情
* @fileName details.vue
* <AUTHOR>
* @date 2025/07/08 15:51:48
!-->
<script setup lang="ts">
import { ref, reactive } from "vue";
import { infoColumn } from "./config";

const visibleLook = defineModel<boolean>("visibleLook", { required: true });

const handleOnOk = () => {};

const data = {
  name: "11",
};
</script>

<template>
  <a-drawer
    unmount-on-close
    :width="520"
    :visible="visibleLook"
    :mask-closable="true"
    :on-before-ok="handleOnOk"
    body-class="appointmentView"
    :footer="false"
    title="详细信息"
    @cancel="visibleLook = false"
  >
    <a-spin
      :loading="false"
      :style="{
        width: '100%',
      }"
    >
      <div class="contentView">
        <!-- baseInfo -->
        <div class="cardView baseInfo">
          <a-avatar :size="72">
            <img
              alt="avatar"
              src="https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/3ee5f13fb09879ecb5185e440cef6eb9.png~tplv-uwbnlip3yd-webp.webp"
            />
          </a-avatar>
          <div>
            <span>张山山</span>
            <span>北京协和医院 心内科</span>
            <span>15876340124</span>
          </div>
        </div>
        <!-- info -->
        <div class="cardView">
          <a-row :gutter="[0, 10]">
            <template v-for="item in infoColumn(data)" :key="item.attrName">
              <a-col :span="item.span[0]" style="color: #86909c">
                {{ item?.label }}
              </a-col>
              <a-col :span="item.span[1]">
                {{ item?.attrName }}
              </a-col>
            </template>
          </a-row>
        </div>
        <div class="cardView">
          <div class="title">擅长领域</div>
          <div>
            专注于复杂心血管疾病的诊疗，尤其在冠心病、心力衰竭、心律失常等方面有丰富经验。擅长心血管介入治疗，包括冠状动脉造影、冠状动脉支架植入术等。同时在高血压、高脂血症等慢性病的长期管理方面也有独到见解。
          </div>
        </div>
        <div class="cardView">
          <div class="title">社会任职</div>
          <div>
            专注于复杂心血管疾病的诊疗，尤其在冠心病、心力衰竭、心律失常等方面有丰富经验。擅长心血管介入治疗，包括冠状动脉造影、冠状动脉支架植入术等。同时在高血压、高脂血症等慢性病的长期管理方面也有独到见解。
          </div>
        </div>
        <div class="cardView">
          <div class="title">简介</div>
          <div>
            专注于复杂心血管疾病的诊疗，尤其在冠心病、心力衰竭、心律失常等方面有丰富经验。擅长心血管介入治疗，包括冠状动脉造影、冠状动脉支架植入术等。同时在高血压、高脂血症等慢性病的长期管理方面也有独到见解。
          </div>
        </div>
      </div>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>
.contentView {
  font-size: 14px;
  font-weight: normal;

  .cardView {
    background: white;
    padding: 20px;
    margin-bottom: 12px;
    .title {
      color: #86909c;
      margin-bottom: 8px;
    }
  }

  .baseInfo {
    display: flex;
    gap: 16px;
    color: rgba(0, 0, 0, 0.7);
    > div {
      display: flex;
      flex-direction: column;
      gap: 2px;
      > span:first-child {
        color: #000000;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
}
</style>
