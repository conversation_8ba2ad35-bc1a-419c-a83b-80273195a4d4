<!--
* @description 服务人员新增
* @fileName add.vue
* <AUTHOR>
* @date 2025/07/08 15:52:27
!-->
<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from "vue";
import { getImageSrc } from "@/utils";
import { FormInstance, Message } from "@arco-design/web-vue";
import * as apis from "@/batchApi";
import { getToken } from "@/utils/auth";
import DoctorSelect from "./doctorSelect.vue";
import { IModalType, ModalType } from "../config";
import baseInfo from './components/base-info.vue';
import serviceTeam from './components/service-team.vue';

type IState = {
  form: apis.QueryTeamEquityPackageByIdUsingGETTeamEquityPackageVo;
};

type Props = {
  type: IModalType;
  editId: number | undefined
};

const {
  type,
  editId
} = defineProps<Props>();

const loading = ref(false);

const modalFormRef = ref<FormInstance>();
const visible = defineModel<boolean>("visible", { required: true });
const pageState = reactive<IState>({
  // 表单的内容
  form: {},
});

// 当前标题的状态
const title = computed(() => {
  switch(type) {
    case ModalType.edit: return "编辑";
    case ModalType.look: return "查看";
    default: return "新增";
  }
});

// 当前是不是查看的状态
const isView = computed(() => {
  return type === ModalType.look;
})

const handleOnOk = async (): Promise<boolean> => {
  const modalError = await modalFormRef.value?.validate();
  
  return new Promise(resolve => {
    if (modalError) {
      resolve(false);
      return;
    }
    const params = {
      ...pageState.form,
      tagList: pageState.form.tagList?.map(_tagId => {
        return {
          id: _tagId
        }
      })
    } as any;
    console.log('params', params)
    const fetchApi = {
      [ModalType.edit]: apis.postBdManageApiTeamEquityPackageEditTeamEquityPackage,
    };
    console.log("🧜‍♀️", params);
    fetchApi?.[type as Extract<IModalType, "edit" | "view">](params)
      .then(res => {
        if (res.code === "1") {
          Message.success("成功");
          resolve(true);
          visible.value = false;
        }
      })
      .catch(() => {
        resolve(false);
      });
  });
};

const cancel = () => {
  visible.value = false;
};

// 在可见性变化的时候 获取数据
watch(() => visible.value, () => {
  queryDetail()
})

// 获取详情信息
const queryDetail = () => {
  loading.value = true;
  apis.getBdManageApiTeamEquityPackageQueryTeamEquityPackageById({
    id: editId as number
  }).then(_res => {
    if (
      typeof _res.data === 'object' &&
      _res.data !== null
    ) {
      const _data = _res.data as any;
      // 使用form来管理全部编辑数据
      pageState.form = {
        ..._data,
        tagList: _data.tagList?.map((_item: any) => _item?.id),
        originalTagList: _data.tagList
      };
    }
    console.log('_res', _res)
  }).finally(() => {
    loading.value = false;
  })
}

// 提交数据
const handleSubmit = (_values: any) => {
  console.log('_values', _values);
}

</script>

<template>
  <!-- 抽屉的内容 -->
  <a-drawer
    class="package-team-manage-drawer-container"
    unmount-on-close
    :width="520"
    :visible="visible"
    :mask-closable="true"
    :on-before-ok="handleOnOk"
    :title="title"
    :footer="isView ? false : true"
    @cancel="cancel"
  >
    <a-spin
      :loading="loading"
      :style="{
        width: '100%',
      }"
    >
      <div class="package-base-info">
        <div>
          <span>权益包产品类型编码</span>
          <span>{{ pageState.form?.equityPackageCode }}</span>
        </div>
        <div>
          <div>
            <span>外部服务包编码</span>
            <span>{{ pageState.form?.extServicePackageCode }}</span>
          </div>
        </div>
        <div>
          <span>团队ID</span>
          <span>{{ pageState.form?.teamId }}</span>
        </div>
      </div>
      <hr class="split" />
      <a-form
        ref="modalFormRef"
        layout="vertical"
        :model="pageState.form"
        @submit="handleSubmit"
      >
        <!-- 基础信息 -->
        <base-info
          ref="baseInfoRef"
          v-model:form="pageState.form"
          :view="isView"
        />
        <hr class="split" />
        <!-- 服务团队 -->
        <service-team
          v-model:form="pageState.form"
          :view="isView"
          title="服务团队"
        />
        <hr class="split" />
        <!-- 特约专家团队 -->
        <service-team
          v-model:form="pageState.form"
          :view="isView"
          title="特约专家团队"
          professor
        />
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>
// 展示服务包的内容
.package-base-info {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  >div {
    span {
      display: block;
      text-align: center;
      font-size: var(--font-size-body-3);
      &:nth-of-type(1) {
        color: var(--text-gray-deep-color);
      }
      &:nth-of-type(2) {
        color: black;
        margin-top: var(--spacing-4);
      }
    }
    &:nth-child(2) {
      display: flex;
      align-items: center;
      justify-content: space-between;
      &:before, &:after {
        content: '';
        display: block;
        width: 1px;
        height: 28px;
        background-color: rgba(0, 82, 217, 0.06);
      }
    }
  }
}
// 展示分割线的样式
.split {
  border: none;
  width: 100%;
  height: 12px;
  background-color: var(--backlog-item-color);
}
</style>
