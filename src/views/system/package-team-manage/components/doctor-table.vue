<!--
 * @Description: 医生信息选择表
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-07-15 18:44:34
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 17:43:12
-->
<template>
  <!-- @vue-expect-error 类型不匹配的错误 -->
  <a-table
    v-model:selected-keys="selectedKeys"
    row-key="userId"
    :columns="columns"
    :data="tableData"
    :row-selection="rowSelection"
    :pagination="false"
    :scroll="{
      y: 400
    }"
    @selection-change="handleSelectionChange"
  >
    <template #name-filter="{ filterValue, setFilterValue, handleFilterConfirm, handleFilterReset}">
      <div class="custom-filter">
        <a-space direction="vertical">
          <a-input :model-value="filterValue[0]" @input="(value)=>setFilterValue([value])" />
          <div class="custom-filter-footer">
            <a-button @click="handleFilterConfirm">确认</a-button>
            <a-button @click="handleFilterReset">重置</a-button>
          </div>
        </a-space>
      </div>
    </template>
  </a-table>
</template>

<script setup lang="ts">
import { postBdManageApiServicePersonnelManagementQueryManagementUserAdminList, QueryManagementUserAdminListUsingPOSTFuWuRenYuanGuanLiFanHuiXinXi } from '@/batchApi';
import { IconSearch } from '@arco-design/web-vue/es/icon';
import { computed, h, onMounted, reactive, ref } from 'vue';

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false,
});

const {
  selectIds = []
} = defineProps<{
  selectIds: (string | number)[]
}>()

const selectedKeys = ref<(string | number)[]>([]);

const columns = [
  {
    title: '姓名',
    dataIndex: 'name',
    filterable: {
      // @ts-expect-error 没有对应的定义信息
      filter: (value, record) => record.name.includes(value),
      slotName: 'name-filter',
      icon: () => h(IconSearch)
    }
  },
  {
    title: '用户ID/工号',
    dataIndex: 'userId',
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
  },
  {
    title: '职称',
    dataIndex: 'professionalTitle',
  },
  {
    title: '头衔',
    dataIndex: 'userTitleName',
  },
]

const tableData = ref<QueryManagementUserAdminListUsingPOSTFuWuRenYuanGuanLiFanHuiXinXi[]>([]);

onMounted(() => {
  getServiceMember();
})

const handleSelectionChange = (
  _rowKeys: (string | number)[]
) => {
  selectedKeys.value = _rowKeys;
  console.log('_rowKeys', _rowKeys)
}

const getServiceMember = () => {
  postBdManageApiServicePersonnelManagementQueryManagementUserAdminList({}).then(_res => {
    if (
      Array.isArray(_res.data)
    ) {
      tableData.value = _res.data.map(_item => {
        const hasIndex = selectIds.findIndex(_fi => _fi === (_item?.userId || "")); 
        return {
          ..._item,
          disabled: hasIndex !== -1 
        }
      });
    }
    console.log('doctor_res', _res, Array.isArray(_res.data))
  })
}

const userList = computed(() => {
  return tableData.value.filter(_fi => selectedKeys.value.includes(_fi.userId as unknown as string) && _fi)
})

console.log('userList', userList)

defineExpose({
  userList,
})

</script>

<style scoped lang="scss">
.custom-filter {
  padding: 20px;
  background: var(--color-bg-5);
  border: 1px solid var(--color-neutral-3);
  border-radius: var(--border-radius-medium);
  box-shadow: 0 2px 5px rgb(0 0 0 / 10%);
}

.custom-filter-footer {
  display: flex;
  justify-content: space-between;
}
</style>