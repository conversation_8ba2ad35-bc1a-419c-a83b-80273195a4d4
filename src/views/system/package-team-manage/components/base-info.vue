<!--
 * @Description: 基础信息
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-07-15 15:49:07
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-26 22:29:06
-->
<template>
  <div
    v-if="!view"
  >
    <h2>基础信息</h2>
    <a-form-item
      field="teamAvatar"
      label="团队照片"
      help="推荐尺寸：375 * 155"
      :rules="[
        {
          required: true,
          message: '请输入团队照片',
        },
      ]"
    >
      <a-upload
        :file-list="fileList"
        :action="uploadUrl"
        accept="image/*"
        :show-file-list="false"
        :headers="{
          authorization: `Bearer ${token}`,
          accesstoken: token as string,
        }"
        :on-before-upload="onBeforeUpload"
        @success="handleUploadSuccess"
        @progress="onProgress"
      >
        <template #upload-button>
          <a-spin
            :loading="uploading"
          >
            <a-avatar
              :trigger-icon-style="{ color: '#3491FA' }"
              :auto-fix-font-size="false"
              :size="72"
              :style="{ marginBottom: '20px', marginTop: '20px' }"
            >
              <img :src="form.teamAvatar || avatarPng" />
              <template #trigger-icon>
                <IconCamera />
              </template>
            </a-avatar>
          </a-spin>
        </template>
      </a-upload>
    </a-form-item>
    <a-form-item
      field="teamName"
      label="团队名称"
      :rules="[
        {
          required: true,
          message: '请输入团队名称',
        },
      ]"
    >
      <a-input
        v-model="form.teamName"
        placeholder="请输入团队名称..."
      />
    </a-form-item>
    <a-form-item
      field="teamBrief"
      label="团队介绍"
      :rules="[
        {
          required: true,
          message: '请输入团队介绍',
        },
      ]"
    >
      <a-textarea
        v-model="form.teamBrief"
        placeholder="请输入团队介绍..."
        allow-clear
      />
    </a-form-item>
    <a-form-item
      field="tagList"
      label="团队标签"
      :rules="[
        {
          required: true,
          message: '请输入团队标签',
        },
      ]"
    >
      <a-cascader
        v-model="form.tagList"
        :options="tagOptions"
        :style="{width: '100%'}"
        placeholder="请选择团队标签"
        :format-label="formatLabel"
        multiple
        :max-tag-count="2"
        allow-clear
      />
    </a-form-item>
  </div>
  <div
    else
    class="view-container"
  >
    <div class="title">
      <img :src="form.teamAvatar || avatarPng" alt="">
      <div class="teamInfo">
        <h2>{{ form.teamName }}</h2>
        <div class="tagInfo">
          <!-- @vue-expect-error -->
          <div
            v-for="item, index in form.originalTagList"
            :key="index"
            class="tagItem"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
    <div class="intro">
      <span>简介</span>
      <span>{{ form.teamBrief }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import avatarPng from '@/assets/images/avatar.png'
import { getToken } from '@/utils/auth';
import { computed, onMounted, ref, watch } from 'vue';
import { getImageSrc } from "@/utils";
import * as apis from "@/batchApi";
import to from 'await-to-js';
import { CascaderOption, FileItem } from '@arco-design/web-vue';

// 表单 form
const form = defineModel<apis.QueryTeamEquityPackageByIdUsingGETTeamEquityPackageVo>("form", { required: true });
const tagOptions = ref<CascaderOption[]>([])
const fileList = ref([]);
const token = getToken();
const uploadUrl = `${import.meta.env.VITE_API_BASE_URL}/base/upload/uploadImgMin`;
const tagList = ref<apis.PageUsingPOST_2PatientTag[]>();
const uploading = ref(false);

const {
  view
} = defineProps<{
  view?: boolean
}>()

watch(() => fileList.value, (_value) => {
  console.log('_value', _value)
})

const onBeforeUpload = () => {
  uploading.value = true;
  return true;
}

// 展示标签列表
const getTagList = async () => {  
  const [err, res] = await to(Promise.all([
    // 获取标签的分类
    apis.getBdManageApiSystemDictDataTypeByDictType({
      dictType: 'tag_type',
    }),
    // 获取类型的分页数据
    apis.postBdManageApiTagPage({
      pageNum: 1,
      pageSize: 999
    })
  ]))
  if (
    err
  ) {
    throw err
  }
  // 拼接数据实现 级联选择器
  console.log('dict_res', res)
  const options: CascaderOption[] = [];
  if (
    Array.isArray(res) &&
    res.length === 2
  ) {
    const typeDict = res[0]
    const dictContentRes = res[1]
    if (
      Array.isArray(typeDict.data) &&
      Array.isArray(dictContentRes.data?.content)
    ) {
      const dictContent = dictContentRes.data?.content
      typeDict.data.forEach(_item => {
        const typeItem = {
          label: _item.dictLabel,
          value: _item.dictValue,
          // @ts-ignore number 和 string 类型不能简单对应
          children: dictContent.filter(_fi => String(_fi.type) === String(_item.dictValue)).map(_dict => {
            return {
              label: _dict.name,
              value: _dict.id
            }
          })
        }
        options.push(typeItem);
      })
    }
    tagOptions.value = options;
  }
  // if (
  //   Array.isArray(res.data?.content)
  // ) {
  //   // @ts-ignore
  //   tagList.value = res.data?.content?.map(_item => {
  //     return {
  //       label: _item.name,
  //       value: _item.id
  //     }
  //   })
  // }
}

const handleUploadSuccess = (_files: FileItem) => {
  uploading.value = false;
  console.log('_files', _files)
  console.log('type', typeof _files);
  if (
    typeof _files?.response?.data?.uri === 'string' &&
    _files?.response?.data?.uri !== ''
  ) {
    form.value.teamAvatar = _files?.response?.data?.uri;
  }
}

const formatLabel = (options: any[]) => {
  const labels = options.map(option => option.label)
  return labels[1]
}


onMounted(() => {
  getTagList();
})

defineExpose({
  tagList
})

</script>

<style scoped lang="scss">
// 展示的元素
.view-container {
  >.title {
    display: flex;
    align-items: center;
    column-gap: var(--spacing-8);
    >img {
      width: 52px;
      height: 52px;
      border-radius: 50%;
    }
    >.teamInfo {
      >h2 {
        font-weight: bold;
        font-size: var(--font-size-title-1);
        margin-top: 0px;
        margin-bottom: var(--spacing-4);
      }
      >.tagInfo {
        display: flex;
        align-items: center;
        column-gap: var(--spacing-4);
        >.tagItem {
          color: var(--primary-color);
          font-size: var(--font-size-body-1);
          padding: 0 var(--spacing-4);
          border: 1px solid var(--primary-color);
          border-radius: var(--border-radius-medium);
        }
      }
    }
  }
  >.intro {
    padding: var(--spacing-6);
    border-radius: var(--border-radius-large);
    background-color: var(--intro-bg-color);
    margin-top: var(--spacing-7);
    >span {
      font-size: var(--font-size-body-3);
      &:nth-child(1) {
        color: var(--text-gray-deep-color);
      }
      &:nth-child(2) {
        color: black;
        margin-left: var(--spacing-6);
      }
    }
  }
}
</style>