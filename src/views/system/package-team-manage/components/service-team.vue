<!--
 * @Description: 服务团队信息
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-07-15 16:03:37
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 22:27:24
-->
<template>
  <!-- 带控制的标题 -->
  <div class="title">
    <h2>{{ title }}</h2>
    <a-button
      v-if="!view"
      type="outline"
      size="mini"
      @click="handleAdd"
    >
      <template
        #default
      >
        添加
      </template>
      <template
        #icon
      >
        <icon-plus />
      </template>
    </a-button>
  </div>
  <!-- 展示当前的元素 -->
  <div
    class="user-list"
    :class="{
      'split-4': splitNumber === 4,
      'split-6': splitNumber === 6
    }"
  >
    <div
      v-for="item, index in userList"
      :key="index"
      :class="{ 'user-list-item': !$slots.item }"
    >
      <!-- 没有单独的slot元素的时候 展示这个内容 -->
      <div
        v-if="!$slots.item"
        class="header"
        :class="{ 'onlyHeader': professor }"
      >
        <!-- 头像 -->
        <img :src="item.avatar || avatarPng" alt="">
        <!-- 姓名 -->
        <span>{{ item.name }}</span>
        <!-- 职位 -->
        <span>{{ item.departmentName }}</span>
        <!-- 删除按钮 -->
        <div
          v-if="item.isCrm !== 1 && !view"
          class="delete"
          @click="handDelete(item.userId)"
        >
          <icon-delete />
        </div>
      </div>
      <!-- 当有内容的时候 展示这个 slot 的内容 -->
      <slot
        v-if="$slots.item"
        name="item"
        :record="item"
      >

      </slot>
      <!-- 头衔 -->
      <div
        v-if="!professor"
        class="footer"
      >
        {{ item.userTitleName }}        
      </div>
    </div>
  </div>
  <div
    v-if="userList?.length === 0"
    class="addHits"
  >
    {{ view ? '暂无信息' : '暂无信息，请进行添加' }}
  </div>
</template>

<script setup lang="ts">
import { Modal } from '@arco-design/web-vue';
import { computed, h, ref } from 'vue';
import { QueryTeamEquityPackageByIdUsingGETTeamEquityPackageVo } from '@/batchApi';
import avatarPng from '@/assets/images/avatar.png'
import doctorTable from './doctor-table.vue';

const form = defineModel<QueryTeamEquityPackageByIdUsingGETTeamEquityPackageVo>("form", { required: true });

const {
  title,
  professor = false,
  view = false,
  splitNumber = 4
} = defineProps<{
  title: string,
  professor?: boolean
  view?: boolean
  splitNumber?: number
}>();

const doctorTableRef = ref();

const userList = computed(() => {
  return form.value.userList?.filter(_fi => _fi.isServiceTeam === (professor ? 0 : 1)) 
})

const handleAddMember = (
  done: (closed: boolean) => void
) => {
  // 获取当前提交的数据 获取到数据之后 才可以编辑对应的内容
  console.log('userList', form.value.userList)
  console.log('userList_value', doctorTableRef.value?.userList)
  // 过滤出当前选中人的数据
  doctorTableRef.value?.userList.forEach((_item: any) => {
    if (
      Array.isArray(form.value.userList)
    ) {
      form.value.userList.push({
        userId: _item.userId,
        name: _item.name,
        departmentName: _item.departmentName,
        userTitle: _item.userTitle,
        isServiceTeam: professor ? 0 : 1
      })
    }
  })
  
  done(true)
}

const handDelete = (_userId: string | number | undefined) => {
  const itemIndex = form.value.userList?.findIndex(_fi => _fi.userId as unknown as string === _userId);
  if (
    itemIndex !== -1 &&
    itemIndex !== undefined
  ) {
    form.value.userList?.splice(itemIndex, 1);
  }
}

const handleAdd = () => {
  console.log('handleAdd')
  Modal.open({
    width: '1200px',
    title: '请选择需要添加的人员',
    content: () => h(doctorTable, {
      ref: doctorTableRef,
      // @ts-expect-error
      selectIds: userList.value?.map(_user => _user.userId)
    }),
    onBeforeOk: handleAddMember
  });
}

</script>

<style scoped lang="scss">
.title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  >h2 {
    font-size: var(--font-size-title-1);
  }
  >button {
  }
}

.user-list {
  display: grid;
  column-gap: var(--spacing-7);
  row-gap: var(--spacing-7);
  &.split-4 {
    grid-template-columns: repeat(4, 1fr);
  }
  &.split-6 {
    grid-template-columns: repeat(6, 1fr);
  }
  >.user-list-item {
    width: 108px;
    // height: 126px;
    border-radius: var(--border-radius-large);
    background-color: var(--item-hit-bg-color);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    >.header {
      position: relative;
      text-align: center;
      >img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-top: var(--spacing-6);
      }
      >span {
        display: block;
        &:nth-child(2) {
          color: black;
          font-size: var(--font-size-body-3);
          // margin-top: var(--spacing-4);
          // margin-bottom: var(--spacing-3);
        }
        &:nth-child(3) {
          color: var(--text-gray-deep-color);
          font-size: var(--font-size-body-1);
        }
      }
      >div.delete {
        color: var(--primary-color);
        cursor: pointer;
        position: absolute;
        top: var(--spacing-2);
        right: var(--spacing-2);
      }
    }
    >.onlyHeader {
      padding-bottom: var(--spacing-6);
    }
    >.footer {
      padding-top: var(--spacing-3);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 32px;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-image: url("../../../../assets/images/system/card-button-bg.png")
    }
  }
}

.addHits {
  height: 100px;
  color: var(--text-hit-color);
  border-radius: var(--border-radius-large);
  background-color: var(--backlog-item-color);
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>