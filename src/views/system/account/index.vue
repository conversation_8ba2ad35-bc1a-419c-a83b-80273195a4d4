<!--
* @description 账号配置
* @fileName index.vue
* <AUTHOR>
* @date 2025/07/02 09:50:46
!-->
<script setup lang="ts">
import { reactive, ref } from "vue";
import {
  FormInstance,
  PaginationProps,
  TableChangeExtra,
  TableData,
} from "@arco-design/web-vue";
import { to } from "await-to-js";
import * as apis from "@/batchApi";
import { userColumn } from "../config";
import tableFilter from "../components/table-filter.vue";
import modal from './modal.vue';

interface IState {
  loading: boolean;
  data: any[];
  visible: boolean;
  title: string;
  form: any;
}

const title = ref("新增账号");

const pageState = reactive<IState>({
  loading: false,
  data: [],
  visible: false,
  title: "新增账号",
  form: {
    status: "1"
  },
});

const pagination = reactive<PaginationProps>({
  size: "small",
  showTotal: true,
  showJumper: true,
  showPageSize: true,
  total: 0,
  current: 1,
  pageSize: 10,
});

// 获取账号列表
const getList = async (
  _userName?: string
) => {
  pageState.loading = true;
  const [err, res] = await to(
    apis.postBdManageApiUserRoleUserSelectUserRoleUserPage({
      pageNumber: pagination.current,
      pageSize: pagination.pageSize,
      userName: _userName 
    }),
  );
  pageState.loading = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    pageState.data = res.data?.records as any;
    pagination.total = res.data.total as number;
  }
};

const handleStop = (
  done: (closed: boolean) => void,
  record: apis.SelectUserRoleUserPageUsingPOSTUserAdminRoleVo
) => {
  apis.postBdManageApiUserRoleUserUpdateUserRole({
    ...record,
    status: record.status === 1 ? 2 : 1,
    // 
    roleUserList: record.list?.map((_item) => {
      return {
        userRoleId: _item.userRoleId
      }
    }),
    userTeamList: record.userTeamList?.map((_item) => {
      return {
        teamId: _item.teamId
      }
    })
  }).then(_res => {
    console.log('_res', _res)
    done(true);
    getList();
  }).catch(_err => {
    console.error('_err', _err)
    done(false);
  })
  console.log('record', record);
};

const handleTableChange = (_data: TableData, extra: TableChangeExtra) => {
  const { page, pageSize } = extra || {};
  pagination.current = page;
  pagination.pageSize = pageSize;
  getList();
};

const handleSearch = ({ keyWord }: { keyWord: string }) => {
  pagination.current = 1;
  pagination.pageSize = 10;
  getList(keyWord);
};

// 点击进行编辑
const handleEdit = (_record: apis.SelectUserRoleUserPageUsingPOSTUserAdminRoleVo) => {
  console.log('_record', _record)
  pageState.form = {
    ..._record,
    status: String(_record.status),
    roleUserList: _record.list?.map(_item => {
      return _item.userRoleId
    }),
    userTeamList: _record.userTeamList?.map(_item => {
      return _item.teamId
    })
  };
  title.value = "编辑账号"
  pageState.visible = true
}

getList();
</script>

<template>
  <div>
    <tableFilter
      placeholder="请输入用户姓名"
      @search="handleSearch"
    >
      <!-- <template #operate>
        <a-button type="primary" @click="pageState.visible = true">
          <template #icon>
            <icon-plus />
          </template>
          新增账号
        </a-button>
      </template> -->
    </tableFilter>
    <div class="containerView">
      <a-table
        :loading="pageState.loading"
        row-key="name"
        :columns="userColumn"
        :data="pageState.data"
        :pagination="pagination"
        @page-change="current => (pagination.current = current)"
        @page-size-change="pageSize => (pagination.pageSize = pageSize)"
        @change="handleTableChange"
      >
        <template #list="{ record }">
          <span
            v-for="item, index in record.list"
            :key="item.userId"
          >
            {{ item.userRoleName ?? '-' }}
            <span
              v-if="record.list.length !==0 && (index !==  record.list.length -1)"
            >
              、
            </span>
          </span>
        </template>
        <template #status="{ record }">
          {{ record.status === 1 ? '启用' : '禁用' }}
        </template>
        <template #operate="{ record }">
          <a-space>
            <a-button
              size="mini"
              type="outline"
              @click="() => handleEdit(record)"
            >
              编辑
            </a-button>
            <a-popconfirm
              content="确认要停用吗？"
              type="info"
              :on-before-ok="e => handleStop(e, record)"
            >
              <a-button size="mini" type="outline">
                {{ record.status === 1 ? "停用" : "启用" }}
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
      <modal
        v-model:form="pageState.form"
        v-model:visible="pageState.visible"
        :title="title"
        @reload="getList"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containerView {
  background-color: white;
  padding: 16px;
  overflow: auto;
}
</style>
