<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2025-07-17 16:45:25
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-23 16:22:59
-->
<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    title-align="start"
    @before-ok="handleOk"
    @cancel="cancel"
  >
    <a-form
      ref="modalFormRef"
      :model="form"
      :label-col-props="{ span: 5 }"
      :wrapper-col-props="{ span: 18 }"
      label-align="right"
    >
      <a-form-item
        field="name"
        label="账户使用人"
        :rules="[
          {
            required: true,
            message: '请输入账户使用人',
          },
        ]"
      >
        <a-input v-model="form.name" placeholder="请输入账户使用人" />
      </a-form-item>
      <a-form-item
        field="roleUserList"
        label="角色"
        :rules="[
          {
            required: true,
            message: '请选择角色',
          },
        ]"
      >
        <a-select
          v-model="form.roleUserList"
          placeholder="请选择角色"
          multiple
        >
          <a-option
            v-for="item, index in roleList"
            :key="index"
            :value="item.id"
          >
            {{ item.roleName }}
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item
        field="phone"
        label="登陆手机号"
        :rules="validationRules.mobile"
      >
        <a-input v-model="form.phone" disabled placeholder="请输入登陆手机号" />
      </a-form-item>
      <a-form-item
        field="userTeamList"
        label="所属团队"
        :rules="[
          {
            required: true,
            message: '请选择所属团队',
          },
        ]"
      >
        <a-select
          v-model="form.userTeamList"
          multiple
          placeholder="请选择所属团队"
        >
          <a-option
            v-for="item, index in teamList"
            :key="index"
            :value="item.teamId"
          >
            {{ item.name }}
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item
        field="status"
        label="状态"
        :rules="[
          {
            required: true,
            message: '请选择状态',
          },
        ]"
      >
        <a-radio-group
          v-model="form.status"
          :default-value="'1'"
        >
          <a-radio value="1">启用</a-radio>
          <a-radio value="2">停用</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import * as apis from "@/batchApi";
import { FormInstance } from '@arco-design/web-vue';
import { validationRules } from "@/utils/validator";

const visible = defineModel<boolean>("visible", { required: true, default: false })
const form = defineModel<any>("form", { required: true })
const emits = defineEmits(['reload']);
const {
  title
} = defineProps<{
  title: string
}>()
const modalFormRef = ref<FormInstance>();

const roleList = ref<apis.QueryUserRoleListUsingPOSTUserRole[]>([]);
const teamList = ref<apis.QueryUserTeamPageUsingPOSTUserTeamVo[]>([]);

const handleOk = async (): Promise<boolean> => {
  const modalError = await modalFormRef.value?.validate();
  return new Promise(resolve => {
    if (modalError) {
      resolve(false);
    }
    console.log("formData", form);
    if (
      title === "编辑账号"
    ) {
      apis.postBdManageApiUserRoleUserUpdateUserRole({
        ...form.value,
        roleUserList: form.value.roleUserList.map((_item: string) => {
          return {
            userRoleId: _item
          }
        }),
        userTeamList: form.value.userTeamList.map((_item: string) => {
          return {
            teamId: _item
          }
        })
      }).then(_res => {
        console.log('_res', _res)
        resolve(true);
        emits("reload")
      }).catch(_err => {
        console.error('_err', _err)
        resolve(false);
      })
    } else {
      console.log('暂无内容')
      // apis.postBdManageApiUserRoleUserAddUserRole({
      //   ...form.value,
      //   roleUserList: form.value.roleUserList.map((_item: string) => {
      //     return {
      //       userRoleId: _item
      //     }
      //   }),
      //   userTeamList: form.value.userTeamList.map((_item: string) => {
      //     return {
      //       teamId: _item
      //     }
      //   })
      // })
      //   .then(_res => {
      //     console.log('_res', _res)
      //     resolve(true);
      //   })
      //   .catch((_err) => {
      //     console.error('_err', _err)
      //     resolve(false);
      //   })
    }
  });
};

const cancel = () => {
  modalFormRef.value?.resetFields();
  form.value = {};
  visible.value = false;
};

const getBaseDataList = () => {
  apis.postBdManageApiUserRoleQueryUserRoleList({})
    .then(_res => {
      console.log('role_res', _res);
      if (
        Array.isArray(_res.data)
      ) {
        roleList.value = _res.data
      }
    })
  apis.postBdManageApiUserTeamQueryUserTeamPage({
    pageNumber: 1,
    pageSize: 999
  })
    .then(_res => {
      console.log('team_res', _res);
      if (
        Array.isArray(_res.data?.records)
      ) {
        teamList.value = _res.data?.records
      }
    })
}

onMounted(() => {
  getBaseDataList();
})

</script>

<style scoped lang="scss">

</style>