<!--
* @description 标签管理
* @fileName index.vue
* <AUTHOR>
* @date 2025/07/02 10:04:12
!-->
<script setup lang="ts">
import { computed, reactive, ref, onMounted } from "vue";
import {
  FormInstance,
  Message,
  PaginationProps,
  TableChangeExtra,
  TableData,
} from "@arco-design/web-vue";
import { to } from "await-to-js";
import * as apis from "@/batchApi";
import { tagMangeColumn, ModalType, IModalType } from "../config";
import tableFilter from "../components/table-filter.vue";

type Record = apis.PageUsingPOST_2PatientTag;

interface IState {
  loading: boolean;
  tagLoading: boolean;
  visible: boolean;
  isShowPagination: boolean;
  disabled: boolean;
  data: Record[];
  form: apis.postBdManageApiTagAddRequest;
  type: IModalType;
  tagList: apis.DictTypeUsingGETSysDictData[];
  keyWord: string | undefined;
  editID: number | undefined;
}

const modalFormRef = ref<FormInstance>();
const pageState = reactive<IState>({
  loading: false,
  tagLoading: false,
  visible: false,
  disabled: false,
  isShowPagination: false,
  data: [],
  tagList: [],
  type: ModalType.add,
  form: {},
  keyWord: undefined,
  editID: undefined,
});

const pagination = reactive<PaginationProps>({
  size: "small",
  showTotal: true,
  showJumper: true,
  showPageSize: true,
  total: 0,
  current: 1,
  pageSize: 10,
});

const title = computed(() => {
  return pageState.type === ModalType.look
    ? "查看"
    : pageState.type === ModalType.edit
      ? "编辑"
      : "新增";
});

const getList = async () => {
  pageState.loading = true;
  const [err, res] = await to(
    apis.postBdManageApiTagPage({
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      query: {
        name: pageState.keyWord,
      },
    }),
  );
  pageState.loading = false;
  if (err) {
    throw err;
  }
  if (res.code === "1") {
    pageState.data = res.data?.content as Record[];
    pagination.total = res?.data?.total;
  }
};

const handleLookUp = (record: Record) => {
  pageState.type = ModalType.look;
  modalFormRef.value?.setFields({
    type: { value: `${record?.type}` },
    name: { value: record?.name },
  });
  pageState.visible = true;
  pageState.disabled = true;
};

const handleEdit = (record: Record) => {
  modalFormRef.value?.setFields({
    type: { value: `${record?.type}` },
    name: { value: record?.name },
  });
  pageState.editID = record?.id;
  pageState.type = ModalType.edit;
  pageState.visible = true;
};

const handleTableChange = (_data: TableData, extra: TableChangeExtra) => {
  const { page, pageSize, type, filters } = extra || {};
  // 如果有filter条件隐藏分页
  if (type === "filter" && filters) {
    const hasValidArray = Object.values(filters)?.some(
      v => Array.isArray(v) && v.length > 0,
    );
    pageState.isShowPagination = hasValidArray;
  }
  if (type === "pagination") {
    pagination.current = page;
    pagination.pageSize = pageSize;
    getList();
  }
};

const handleOk = async (): Promise<boolean> => {
  const modalError = await modalFormRef.value?.validate();
  return new Promise(resolve => {
    if (modalError) {
      resolve(false);
      return;
    }
    const params = {
      ...pageState.form,
      id: pageState.editID,
    } as apis.postBdManageApiTagAddRequest;
    const fetchApi = {
      [ModalType.add]: apis.postBdManageApiTagAdd,
      [ModalType.edit]: apis.postBdManageApiTagUpdate,
    };
    fetchApi?.[pageState.type as Extract<IModalType, "add" | "edit">](params)
      .then(res => {
        if (res.code === "1") {
          Message.success("成功");
          resolve(true);
          cancel();
          getList();
        }
      })
      .catch(() => {
        resolve(false);
      });
  });
};
const handleDictData = async (_dictType: string) => {
  const [err, res] = await to(
    apis.getBdManageApiSystemDictDataTypeByDictType({
      dictType: _dictType,
    }),
  );
  if (err) {
    throw err;
  }
  if (res.data) {
    return res.data;
  }
  return [];
};

// onUpdated(async () => {
//   if (pageState.visible) {
//     pageState.tagLoading = true;
//     pageState.tagList = await handleDictData("org_type");
//     pageState.tagLoading = false;
//   }
// });

const cancel = () => {
  pageState.visible = false;
  pageState.disabled = false;
  pageState.editID = undefined;
  modalFormRef.value?.resetFields();
};

const handleSearch = ({ keyWord }: { keyWord: string }) => {
  pageState.keyWord = keyWord;
  getList();
};

onMounted(async () => {
  pageState.tagLoading = true;
  pageState.tagList = await handleDictData("tag_type");
  pageState.tagLoading = false;
});

getList();
</script>

<template>
  <div class="table-wrapper">
    <tableFilter
      placeholder="请输入标签名称"
      @search="handleSearch"
      @handle-reset="handleSearch"
    >
      <template #operate>
        <a-button
          type="primary"
          @click="
            pageState.visible = true;
            pageState.type = ModalType.add;
          "
        >
          <template #icon> <icon-plus /> </template>新增</a-button
        >
      </template>
    </tableFilter>
    <div class="table-container">
      <a-table
        :loading="pageState.loading"
        row-key="id"
        :scroll="{ y: '100%' }"
        :columns="tagMangeColumn({ tagTypeList: pageState.tagList })"
        :data="pageState.data"
        :pagination="pageState.isShowPagination || pagination"
        @page-change="(current: number) => (pagination.current = current)"
        @page-size-change="
          (pageSize: number) => (pagination.pageSize = pageSize)
        "
        @change="handleTableChange"
      >
        <template #operate="{ record }">
          <a-space>
            <a-button size="mini" type="outline" @click="handleLookUp(record)"
              >查看</a-button
            >
            <a-button size="mini" type="outline" @click="handleEdit(record)">
              编辑
            </a-button>
          </a-space>
        </template>
      </a-table>
      <a-modal
        v-model:visible="pageState.visible"
        :title="title"
        title-align="start"
        :footer="!pageState.disabled"
        @before-ok="handleOk"
        @cancel="cancel"
      >
        <a-form
          ref="modalFormRef"
          :model="pageState.form"
          :label-col-props="{ span: 5 }"
          :wrapper-col-props="{ span: 18 }"
          label-align="right"
          :disabled="pageState.disabled"
        >
          <a-form-item
            field="name"
            label="标签名称"
            :rules="[
              {
                required: true,
                message: '请输入编码',
              },
            ]"
          >
            <a-input v-model="pageState.form.name" />
          </a-form-item>
          <a-form-item
            field="type"
            label="类型"
            :rules="[
              {
                required: true,
                message: '请选择类型',
              },
            ]"
          >
            <a-select v-model="pageState.form.type">
              <a-option
                v-for="item in pageState.tagList"
                :key="item?.dictCode"
                :value="item?.dictValue"
                >{{ item?.dictLabel }}</a-option
              >
            </a-select>
          </a-form-item>
        </a-form>
      </a-modal>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.table-wrapper {
  background-color: white;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  .table-operate {
    display: flex;
    gap: 8px;
  }
  .table-container {
    overflow-y: scroll;
  }
}
</style>
