<!--
* @description 账号配置
* @fileName index.vue
* <AUTHOR>
* @date 2025/07/02 09:50:46
!-->
<script setup lang="ts">
import { reactive, ref } from "vue";
import {
  FormInstance,
  PaginationProps,
  TableChangeExtra,
  TableData,
} from "@arco-design/web-vue";
import { to } from "await-to-js";
import * as apis from "@/batchApi";
import { validationRules } from "@/utils/validator";
import { userColumn } from "../config";
import tableFilter from "../components/table-filter.vue";

interface IState {
  loading: boolean;
  data: any[];
  visible: boolean;
  title: string;
  form: any;
}
const modalFormRef = ref<FormInstance>();
const pageState = reactive<IState>({
  loading: false,
  data: [{ name: "张三" }],
  visible: false,
  title: "新增",
  form: {},
});

const pagination = reactive<PaginationProps>({
  size: "small",
  showTotal: true,
  showJumper: true,
  showPageSize: true,
  total: 0,
  current: 1,
  pageSize: 10,
});

const getList = async () => {
  pageState.loading = true;
  const [err, res] = await to(
    apis.postBdManageApiServiceReservationQueryServiceReservationList({
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    }),
  );
  pageState.loading = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    pageState.data = res.data?.records as any;
    pagination.total = res.data.total as number;
  }
};

const handleStop = (done: (closed: boolean) => void, record: any) => {
  setTimeout(() => {
    done(true);
  }, 2000);
};

const handleTableChange = (_data: TableData, extra: TableChangeExtra) => {
  const { page, pageSize } = extra || {};
  pagination.current = page;
  pagination.pageSize = pageSize;
  getList();
};

const handleOk = async (): Promise<boolean> => {
  const modalError = await modalFormRef.value?.validate();
  return new Promise(resolve => {
    if (modalError) {
      resolve(false);
    }
    console.log(pageState.form);
    resolve(true);
  });
};

const cancel = () => {
  pageState.visible = false;
  modalFormRef.value?.resetFields();
};

const handleSearch = ({ keyWord }: { keyWord: string }) => {
  console.log("🤨", keyWord);
};

getList();
</script>

<template>
  <div>
    <tableFilter @search="handleSearch">
      <template #operate>
        <a-button type="primary" @click="pageState.visible = true">
          <template #icon> <icon-plus /> </template>新增</a-button
        >
        <a-button type="primary">
          <template #icon> <icon-sync /> </template>同步信息</a-button
        >
      </template>
    </tableFilter>
    <div class="containerView">
      <a-table
        :loading="pageState.loading"
        row-key="name"
        :columns="userColumn"
        :data="pageState.data"
        :pagination="pagination"
        @page-change="current => (pagination.current = current)"
        @page-size-change="pageSize => (pagination.pageSize = pageSize)"
        @change="handleTableChange"
      >
        <template #operate>
          <a-space>
            <a-button size="mini" type="outline">查看</a-button>
            <a-popconfirm
              content="确认要停用吗？"
              type="info"
              :on-before-ok="e => handleStop(e, record)"
            >
              <a-button size="mini" type="outline"> 停用 </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
      <a-modal
        v-model:visible="pageState.visible"
        :title="pageState.title"
        title-align="start"
        @before-ok="handleOk"
        @cancel="cancel"
      >
        <a-form
          ref="modalFormRef"
          :model="pageState.form"
          :label-col-props="{ span: 5 }"
          :wrapper-col-props="{ span: 18 }"
          label-align="right"
        >
          <a-form-item
            field="name"
            label="账户使用人"
            :rules="[
              {
                required: true,
                message: '请输入账户使用人',
              },
            ]"
          >
            <a-input v-model="pageState.form.name" />
          </a-form-item>
          <a-form-item
            field="post"
            label="角色"
            :rules="[
              {
                required: true,
                message: '请选择角色',
              },
            ]"
          >
            <a-select v-model="pageState.form.post">
              <a-option value="post1">Post1</a-option>
              <a-option value="post2">Post2</a-option>
              <a-option value="post3">Post3</a-option>
              <a-option value="post4">Post4</a-option>
            </a-select>
          </a-form-item>
          <a-form-item
            field="phone"
            label="登陆手机号"
            :rules="validationRules.mobile"
          >
            <a-input v-model="pageState.form.phone" />
          </a-form-item>
        </a-form>
      </a-modal>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containerView {
  background-color: white;
  padding: 16px;
  overflow: auto;
}
</style>
