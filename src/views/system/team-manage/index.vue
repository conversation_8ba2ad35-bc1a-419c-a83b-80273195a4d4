<!--
* @description 账号配置
* @fileName index.vue
* <AUTHOR>
* @date 2025/07/02 09:50:46
!-->
<script setup lang="ts">
import { reactive, ref } from "vue";
import {
  FormInstance,
  Message,
  PaginationProps,
  TableChangeExtra,
  TableData,
} from "@arco-design/web-vue";
import { to } from "await-to-js";
import * as apis from "@/batchApi";
import { teamColumn } from "../config";
import tableFilter from "../components/table-filter.vue";

interface IState {
  loading: boolean;
  data: any[];
  visible: boolean;
  form: any;
  synchronizeLoading: boolean
  keyWord: string
}

const pageState = reactive<IState>({
  synchronizeLoading: false,
  loading: false,
  data: [],
  visible: false,
  form: {},
  keyWord: ""
});

const pagination = reactive<PaginationProps>({
  size: "small",
  showTotal: true,
  showJumper: true,
  showPageSize: true,
  total: 0,
  current: 1,
  pageSize: 10,
});

// 获取账号列表
const getList = async () => {
  pageState.loading = true;
  const [err, res] = await to(
    apis.postBdManageApiUserTeamQueryUserTeamPage({
      pageNumber: pagination.current,
      pageSize: pagination.pageSize,
      // 使用关键字进行查询
      teamName: pageState.keyWord
    }),
  );
  pageState.loading = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    pageState.data = res.data?.records as any;
    pagination.total = res.data.total as number;
  }
};

const handleTableChange = (_data: TableData, extra: TableChangeExtra) => {
  const { page, pageSize } = extra || {};
  pagination.current = page;
  pagination.pageSize = pageSize;
  getList();
};

const handleSearch = ({ keyWord }: { keyWord: string }) => {
  pageState.keyWord = keyWord;
  pagination.current = 1;
  pagination.pageSize = 10;
  getList();
  console.log("🤨", keyWord);
};

// 点击进行团队信息同步
const handleSynchronize = () => {
  Message.success("敬请期待！")
}

getList();
</script>

<template>
  <div>
    <tableFilter
      placeholder="请输入团队名称"
      @search="handleSearch"
    >
      <template #operate>
        <a-button
          :loading="pageState.synchronizeLoading"
          type="primary"
          @click="handleSynchronize"
        >
          <template #icon> <icon-sync /> </template>同步信息</a-button
        >
      </template>
    </tableFilter>
    <div class="containerView">
      <a-table
        :loading="pageState.loading"
        row-key="name"
        :columns="teamColumn"
        :data="pageState.data"
        :pagination="pagination"
        @page-change="current => (pagination.current = current)"
        @page-size-change="pageSize => (pagination.pageSize = pageSize)"
        @change="handleTableChange"
      >
        <template #list="{ record }">
          <span
            v-for="item, index in record.list"
            :key="index"
          >
            {{ item.name }}
            {{ index !== record.list.length - 1 ? '、' : '' }}
          </span>
        </template>
      </a-table>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containerView {
  background-color: white;
  padding: 16px;
  overflow: auto;
}
</style>
