import {
  TableColumnData,
  TableData,
  TableFilterData,
} from "@arco-design/web-vue";
import { IconSearch } from "@arco-design/web-vue/es/icon";
import Filterable from "@/components/filterable/index.vue";
import { h, VNode } from "vue";
import dayjs from "dayjs";
import * as apis from "@/batchApi";
import { RenderInfo } from "@/types/global";
import { IDictionaryState } from "./waitstaff/index.vue";

/* 页面弹窗状态 */
export enum ModalType {
  add = "add",
  look = "look",
  edit = "edit",
}

export type IModalType = keyof typeof ModalType;

/* 账户配置column */
export const userColumn: Array<TableColumnData> = [
  {
    title: "序号",
    dataIndex: "",
    render: ({ rowIndex }) => {
      return rowIndex + 1;
    },
  },
  {
    title: "姓名",
    dataIndex: "name",
    filterable: {
      alignLeft: true,
      filter: (value, record) => record?.name?.includes(value),
      icon: () => h(IconSearch),
      renderContent: ({
        filterValue,
        setFilterValue,
        handleFilterConfirm,
        handleFilterReset,
      }) =>
        h(Filterable, {
          value: filterValue[0],
          onSetFilterValue: setFilterValue,
          onHandleFilterConfirm: handleFilterConfirm as unknown as any,
          onHandleFilterReset: handleFilterReset as unknown as any,
        }),
    },
  },
  {
    title: "角色",
    dataIndex: "list",
    slotName: "list"
  },
  {
    title: "账号",
    dataIndex: "phone",
  },
  {
    title: "状态",
    dataIndex: "status",
    slotName: "status"
  },
  {
    title: "操作",
    slotName: "operate",
  },
];

export const teamColumn: Array<TableColumnData> = [
  {
    title: "序号",
    dataIndex: "",
    render: ({ rowIndex }) => {
      return rowIndex + 1;
    },
  },
  {
    title: "团队名称",
    dataIndex: "name",
    filterable: {
      alignLeft: true,
      filter: (value, record) => record?.name?.includes(value),
      icon: () => h(IconSearch),
      renderContent: ({
        filterValue,
        setFilterValue,
        handleFilterConfirm,
        handleFilterReset,
      }) =>
        h(Filterable, {
          value: filterValue[0],
          onSetFilterValue: setFilterValue,
          onHandleFilterConfirm: handleFilterConfirm as unknown as any,
          onHandleFilterReset: handleFilterReset as unknown as any,
        }),
    },
  },
  {
    title: "团队成员",
    dataIndex: "list",
    slotName: "list"
  },
  {
    title: "团队介绍",
    dataIndex: "brief",
    filterable: {
      alignLeft: true,
      filter: (value, record) => record?.brief?.includes(value),
      icon: () => h(IconSearch),
      renderContent: ({
        filterValue,
        setFilterValue,
        handleFilterConfirm,
        handleFilterReset,
      }) =>
        h(Filterable, {
          value: filterValue[0],
          onSetFilterValue: setFilterValue,
          onHandleFilterConfirm: handleFilterConfirm as unknown as any,
          onHandleFilterReset: handleFilterReset as unknown as any,
        }),
    },
  },
  {
    title: "添加时间",
    dataIndex: "xcreateTime",
  }
]

// 账号分配 column
export const accountColumn: Array<TableColumnData> = [
  {
    title: "序号",
    width: 60,
    align: 'center',
    render: ({ rowIndex }) => {
      return rowIndex + 1;
    },
  },
  {
    title: '姓名',
    dataIndex: 'name'
  },
  {
    title: '角色',
    dataIndex: 'roleName'
  },
  {
    title: '账号',
    dataIndex: 'account'
  },
  {
    title: '所属机构',
    dataIndex: 'orgName'
  },
  {
    title: '状态',
    dataIndex: 'orgName'
  },
  {
    title: "操作",
    dataIndex: "name",
    align: 'center',
    slotName: "operate",
  },
]

/* 服务人员column */

export const infoColumn: RenderInfo<apis.QueryManagementUserAdminDetailUsingGETManagementUserAdminDetailVo>[] =
  [
    { label: "类型", attrName: "customerTypeName", span: [6, 18] },
    { label: "头衔", attrName: "userTitleName", span: [6, 18] },
    { label: "职称", attrName: "professionalTitleName", span: [6, 18] },
    { label: "身份证号码", attrName: "certNo", span: [6, 18] },
    {
      label: "执业许可证编号",
      attrName: "practicingLicenseNumber",
      span: [6, 18],
    },
    {
      label: "标签",
      attrName: "tagList",
      span: [6, 18],
      render: data => {
        const list = data?.tagList?.map(item => item.name)?.filter(Boolean);
        return list?.length ? list?.join("-") : "-";
      },
    },
  ];
/* 标签管理column */
type WaitstaffColumn = {
  // 类型
  typeList?: IDictionaryState<apis.DictTypeUsingGETSysDictData[]>["typeList"];
  // 头衔
  titleTypeList?: IDictionaryState<
    apis.DictTypeUsingGETSysDictData[]
  >["titleTypeList"];
  // 职称
  professionalTitleList?: IDictionaryState<
    apis.DictTypeUsingGETSysDictData[]
  >["professionalTitleList"];
  // 标签
  tagList?: IDictionaryState<apis.DictTypeUsingGETSysDictData[]>["tagList"];
  // 机构
  organList?: IDictionaryState<apis.DictTypeUsingGETSysDictData[]>["organList"];
  // 科室
  departmentList?: IDictionaryState<
    apis.DictTypeUsingGETSysDictData[]
  >["departmentList"];
};
export const waitstaffColumn = ({
  typeList,
  titleTypeList,
  professionalTitleList,
  tagList,
  organList,
  departmentList,
}: WaitstaffColumn): Array<TableColumnData> => [
  {
    title: "序号",
    dataIndex: "userId",
    width: 60,
    align: "center",
    render: ({ rowIndex }) => {
      return rowIndex + 1;
    },
  },
  {
    title: "用户ID/工号",
    width: 180,
    dataIndex: "userId",
    filterable: {
      alignLeft: true,
      filter: (value, record) => record?.userId?.includes(value),
      icon: () => h(IconSearch),
      renderContent: ({
        filterValue,
        setFilterValue,
        handleFilterConfirm,
        handleFilterReset,
      }) =>
        h(Filterable, {
          value: filterValue[0],
          onSetFilterValue: setFilterValue,
          onHandleFilterConfirm: handleFilterConfirm as unknown as any,
          onHandleFilterReset: handleFilterReset as unknown as any,
        }),
    },
  },
  {
    title: "姓名",
    width: 100,
    dataIndex: "name",
    filterable: {
      alignLeft: true,
      filter: (value, record) => record?.name?.includes(value),
      icon: () => h(IconSearch),
      renderContent: ({
        filterValue,
        setFilterValue,
        handleFilterConfirm,
        handleFilterReset,
      }) =>
        h(Filterable, {
          value: filterValue[0],
          onSetFilterValue: setFilterValue,
          onHandleFilterConfirm: handleFilterConfirm as unknown as any,
          onHandleFilterReset: handleFilterReset as unknown as any,
        }),
    },
  },
  {
    title: "联系电话",
    dataIndex: "phone",
    width: 130,
    filterable: {
      alignLeft: true,
      filter: (value, record) => record?.phone?.includes(value),
      icon: () => h(IconSearch),
      renderContent: ({
        filterValue,
        setFilterValue,
        handleFilterConfirm,
        handleFilterReset,
      }) =>
        h(Filterable, {
          value: filterValue[0],
          onSetFilterValue: setFilterValue,
          onHandleFilterConfirm: handleFilterConfirm as unknown as any,
          onHandleFilterReset: handleFilterReset as unknown as any,
        }),
    },
  },
  {
    title: "职称",
    dataIndex: "professionalTitle",
    width: 150,
    filterable: {
      filters:
        (professionalTitleList?.map((item:any) => ({
          text: item?.name,
          value: item?.professionalTitleId,
        })) as Array<TableFilterData>) || [],
      alignLeft: true,
      filter: (value, record) => value?.includes(`${record?.professionalTitleId}`),
      multiple: true,
    },
  },
  {
    title: "科室",
    dataIndex: "departmentName",
    width: 150,
    filterable: {
      filters:
        (departmentList?.map((item:any) => ({
          text: item?.secondName,
          value: item?.xid,
        })) as Array<TableFilterData>) || [],
      alignLeft: true,
      filter: (value, record) => value?.includes(`${record?.departmentId}`),
      multiple: true,
    },
  },
  {
    title: "机构",
    dataIndex: "organName",
    width: 180,
    filterable: {
      filters:
        (organList?.map((item:any) => ({
          text: item?.name,
          value: item?.organCode,
        })) as Array<TableFilterData>) || [],
      alignLeft: true,
      filter: (value, record) => value?.includes(`${record?.organCode}`),
      multiple: true,
    },
  },
  {
    title: "类型",
    dataIndex: "customerTypeName",
    width: 150,
    filterable: {
      filters:
        (typeList?.map((item:any) => ({
          text: item?.dictLabel,
          value: item?.dictValue,
        })) as Array<TableFilterData>) || [],
      alignLeft: true,
      filter: (value, record) => value?.includes(`${record?.customerType}`),
      multiple: true,
    },
  },
  {
    title: "头衔",
    dataIndex: "userTitleName",
    width: 150,
    filterable: {
      filters:
        (titleTypeList?.map((item:any) => ({
          text: item?.dictLabel,
          value: item?.dictValue,
        })) as Array<TableFilterData>) || [],
      alignLeft: true,
      filter: (value, record) => value?.includes(`${record?.userTitle}`),
      multiple: true,
    },
  },
  {
    title: "添加时间",
    dataIndex: "xcreateTime",
    render: ({ record }: { record: TableData }) => {
      return record?.xcreateTime
        ? dayjs(record?.xcreateTime).format("YYYY-MM-DD HH:mm:ss")
        : "-";
    },
    sortable: {
      sortDirections: ["ascend", "descend"],
    },
  },
  {
    title: "操作",
    dataIndex: "userId",
    align: "center",
    fixed: "right",
    slotName: "operate",
  },
];

/* 标签管理column */
type TagMangeColumn = {
  tagTypeList?: apis.DictTypeUsingGETSysDictData[];
};
export const tagMangeColumn = ({
  tagTypeList,
}: TagMangeColumn): Array<TableColumnData> => [
  {
    title: "序号",
    dataIndex: "xid",
    align: "center",
    render: ({ rowIndex }) => {
      return rowIndex + 1;
    },
  },
  {
    title: "标签名称",
    dataIndex: "name",
    filterable: {
      alignLeft: true,
      filter: (value, record) => record?.name?.includes(value),
      icon: () => h(IconSearch),
      renderContent: ({
        filterValue,
        setFilterValue,
        handleFilterConfirm,
        handleFilterReset,
      }) =>
        h(Filterable, {
          value: filterValue[0],
          onSetFilterValue: setFilterValue,
          onHandleFilterConfirm: handleFilterConfirm as unknown as any,
          onHandleFilterReset: handleFilterReset as unknown as any,
        }),
    },
  },
  {
    title: "类型",
    dataIndex: "typeName",
    filterable: {
      filters:
        (tagTypeList?.map(item => ({
          text: item?.dictLabel,
          value: item?.dictValue,
        })) as Array<TableFilterData>) || [],
      alignLeft: true,
      filter: (value, record) => value?.includes(`${record?.type}`),
      multiple: true,
    },
  },
  {
    title: "添加时间",
    dataIndex: "xcreateTime",
    render: ({ record }: { record: TableData }) => {
      return record?.xcreateTime
        ? dayjs(record?.xcreateTime).format("YYYY-MM-DD HH:mm:ss")
        : "-";
    },
    sortable: {
      sortDirections: ["ascend", "descend"],
    },
  },
  {
    title: "操作",
    dataIndex: "xid",
    align: "center",
    slotName: "operate",
  },
];

/* 机构管理column */
type OrganMangeColumn = {
  orgTypeList?: apis.DictTypeUsingGETSysDictData[];
};
export const organMangeColumn = ({
  orgTypeList,
}: OrganMangeColumn): Array<TableColumnData> => [
  {
    title: "序号",
    dataIndex: "xid",
    align: "center",
    render: ({ rowIndex }) => {
      return rowIndex + 1;
    },
  },
  {
    title: "机构编码",
    dataIndex: "organCode",
    filterable: {
      alignLeft: true,
      filter: (value, record) => record?.organCode?.includes(value),
      icon: () => h(IconSearch),
      renderContent: ({
        filterValue,
        setFilterValue,
        handleFilterConfirm,
        handleFilterReset,
      }) =>
        h(Filterable, {
          value: filterValue[0],
          onSetFilterValue: setFilterValue,
          onHandleFilterConfirm: handleFilterConfirm as unknown as any,
          onHandleFilterReset: handleFilterReset as unknown as any,
        }),
    },
  },
  {
    title: "类型",
    dataIndex: "typeName",
    filterable: {
      filters:
        (orgTypeList?.map(item => ({
          text: item?.dictLabel,
          value: item?.dictValue,
        })) as Array<TableFilterData>) || [],
      alignLeft: true,
      filter: (value, record) => value?.includes(`${record?.type}`),
      multiple: true,
    },
  },
  {
    title: "机构",
    dataIndex: "name",
    filterable: {
      alignLeft: true,
      filter: (value, record) => record?.name?.includes(value),
      icon: () => h(IconSearch),
      renderContent: ({
        filterValue,
        setFilterValue,
        handleFilterConfirm,
        handleFilterReset,
      }) =>
        h(Filterable, {
          value: filterValue[0],
          onSetFilterValue: setFilterValue,
          onHandleFilterConfirm: handleFilterConfirm as unknown as any,
          onHandleFilterReset: handleFilterReset as unknown as any,
        }),
    },
  },
  {
    title: "添加时间",
    dataIndex: "xcreateTime",
    render: ({ record }: { record: TableData }) => {
      return record?.xcreateTime
        ? dayjs(record?.xcreateTime).format("YYYY-MM-DD HH:mm:ss")
        : "-";
    },
    sortable: {
      sortDirections: ["ascend", "descend"],
    },
  },
  {
    title: "操作",
    dataIndex: "organId",
    slotName: "operate",
    align: "center",
  },
];

/* 科室column */
type DepartmentColumn = {
  departmentTypeList?: apis.PageUsingPOST_1PatientDepartmentVo[];
};
export const departmentColumn = ({
  departmentTypeList,
}: DepartmentColumn): Array<TableColumnData> => [
  {
    title: "序号",
    dataIndex: "xid",
    align: "center",
    render: ({ rowIndex }) => {
      return rowIndex + 1;
    },
  },
  {
    title: "一级科室",
    dataIndex: "firstName",
    filterable: {
      filters:
        (departmentTypeList?.map(item => ({
          text: item?.firstName,
          value: `${item?.xid}`,
        })) as Array<TableFilterData>) || [],
      alignLeft: true,
      filter: (value, record) => {
        return value?.includes(`${record?.pid}`);
      },
      multiple: true,
    },
  },
  {
    title: "二级科室",
    dataIndex: "secondName",
    filterable: {
      alignLeft: true,
      filter: (value, record) => record?.secondName?.includes(value),
      icon: () => h(IconSearch),
      renderContent: ({
        filterValue,
        setFilterValue,
        handleFilterConfirm,
        handleFilterReset,
      }) =>
        h(Filterable, {
          value: filterValue[0],
          onSetFilterValue: setFilterValue,
          onHandleFilterConfirm: handleFilterConfirm as unknown as any,
          onHandleFilterReset: handleFilterReset as unknown as any,
        }),
    },
  },
  {
    title: "添加时间",
    dataIndex: "xCreateTime",
    render: ({ record }: { record: TableData }) => {
      return record?.xcreateTime
        ? dayjs(record?.xcreateTime).format("YYYY-MM-DD HH:mm:ss")
        : "-";
    },
    sortable: {
      sortDirections: ["ascend", "descend"],
    },
  },
  {
    title: "操作",
    dataIndex: "xid",
    slotName: "operate",
    align: "center",
  },
];

/* 团队信息column */
export const teamInfoColumn: Array<TableColumnData> = [
  {
    title: "序号",
    render: ({ rowIndex }) => {
      return rowIndex + 1;
    },
  },
  {
    title: "权益包产品类型编码",
    dataIndex: "equityPackageCode",
    filterable: {
      alignLeft: true,
      filter: (value, record) => record?.equityPackageCode?.includes(value),
      icon: () => h(IconSearch),
      renderContent: ({
        filterValue,
        setFilterValue,
        handleFilterConfirm,
        handleFilterReset,
      }) =>
        h(Filterable, {
          value: filterValue[0],
          onSetFilterValue: setFilterValue,
          onHandleFilterConfirm: handleFilterConfirm as unknown as any,
          onHandleFilterReset: handleFilterReset as unknown as any,
        }),
    },
  },
  {
    title: "外部服务包编码",
    dataIndex: "extServicePackageCode",
    filterable: {
      alignLeft: true,
      filter: (value, record) => record?.equityPackageCode?.includes(value),
      icon: () => h(IconSearch),
      renderContent: ({
        filterValue,
        setFilterValue,
        handleFilterConfirm,
        handleFilterReset,
      }) =>
        h(Filterable, {
          value: filterValue[0],
          onSetFilterValue: setFilterValue,
          onHandleFilterConfirm: handleFilterConfirm as unknown as any,
          onHandleFilterReset: handleFilterReset as unknown as any,
        }),
    },
  },
  {
    title: "团队ID",
    dataIndex: "teamId",
  },
  {
    title: "团队名称",
    dataIndex: "teamName",
    filterable: {
      alignLeft: true,
      filter: (value, record) => record?.teamName?.includes(value),
      icon: () => h(IconSearch),
      renderContent: ({
        filterValue,
        setFilterValue,
        handleFilterConfirm,
        handleFilterReset,
      }) =>
        h(Filterable, {
          value: filterValue[0],
          onSetFilterValue: setFilterValue,
          onHandleFilterConfirm: handleFilterConfirm as unknown as any,
          onHandleFilterReset: handleFilterReset as unknown as any,
        }),
    },
  },
  {
    title: "同步时间",
    dataIndex: "xcreateTime",
  },
  {
    title: "操作",
    dataIndex: "name",
    slotName: "operate",
  },
];
