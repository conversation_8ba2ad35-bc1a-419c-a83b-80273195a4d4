<!--
  * @description 搜索条件
  * @fileName table-filter.vue
  * <AUTHOR>
  * @date 2025/07/02 10:19:19
!-->
<script setup lang="ts">
import { reactive } from "vue";
import bdInput from "@/components/bd-input/index.vue";

const emits = defineEmits(["search", "handleReset"]);
defineProps<{ placeholder?: string }>();

const form = reactive<{ keyWord: string | undefined }>({ keyWord: undefined });

const handleSearch = (_searchValue: any) => {
  emits("search", _searchValue);
};

const handleReset = () => {
  form.keyWord = undefined;
  emits("handleReset", { ...form });
};
</script>

<template>
  <div class="filter-wrapper">
    <a-form :model="form" layout="inline">
      <div class="search-container">
        <a-space>
          <bd-input
            v-model="form.keyWord"
            width="253px"
            field="keyWord"
            :placeholder="placeholder ?? '请输入'"
            @search="handleSearch"
          />
          <a-button @click="handleReset">重置</a-button>
        </a-space>
      </div>
    </a-form>
    <a-space>
      <slot name="operate" />
    </a-space>
  </div>
</template>

<style lang="scss" scoped>
.filter-wrapper {
  background: #fff;
  padding: 16px 20px;
  margin-bottom: 16px;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  > .search-container {
    display: flex;
    column-gap: var(--spacing-7);
  }
}
</style>
