<!--
* @description 服务人员新增
* @fileName add.vue
* <AUTHOR>
* @date 2025/07/08 15:52:27
!-->
<script setup lang="ts">
import { computed, onUpdated, reactive, ref, watch } from "vue";
import { getImageSrc } from "@/utils";
import { FormInstance, Message } from "@arco-design/web-vue";
import * as apis from "@/batchApi";
import { getToken } from "@/utils/auth";
import { uniqueId, isEmpty } from "lodash";
import to from "await-to-js";
import DoctorSelect from "./doctorSelect.vue";
import { IModalType, ModalType } from "../../config";
import { IDictionaryState } from "../index.vue";

const token = getToken();
const uploadUrl = `${import.meta.env.VITE_API_BASE_URL}/base/upload/uploadImgMin`;
const props = defineProps<Props>();

const modalFormRef = ref<FormInstance>();
const emits = defineEmits(["fetchApi"]);
const visible = defineModel<boolean>("visible", { required: true });
const pageState = reactive<IState>({
  loading: false,
  form: { customerType: "3" } as unknown as IState["form"],
  file: {},
  doctorInfo: {},
  disabled: false,
  uploadLoading: false, // 添加上传loading状态
});

const title = computed(() => {
  return props.type === ModalType.edit ? "编辑" : "新增";
});
const handleOnOk = async (): Promise<boolean> => {
  const modalError = await modalFormRef.value?.validate();
  return new Promise(resolve => {
    if (modalError) {
      resolve(false);
      return;
    }
    const params = {
      ...pageState.form,
      tagList: pageState.form.tagList?.map(item => ({ id: item })),
      avatar: pageState.file?.url,
      userId: props.userId,
    } as apis.postBdManageApiServicePersonnelManagementEditManagementUserAdminRequest;
    const fetchApi = {
      [ModalType.add]:
        apis.postBdManageApiServicePersonnelManagementAddInternetDoctor,
      [ModalType.edit]:
        apis.postBdManageApiServicePersonnelManagementEditManagementUserAdmin,
    };
    fetchApi?.[props.type as Extract<IModalType, "add" | "edit">](params)
      .then(res => {
        if (res.code === "1") {
          Message.success("成功");
          cancel();
          resolve(true);
        }
      })
      .catch(() => {
        resolve(false);
      });
  });
};

/* 字典 */
const handleDictData = async (_dictType: string) => {
  const [err, res] = await to(
    apis.getBdManageApiSystemDictDataTypeByDictType({
      dictType: _dictType,
    }),
  );
  if (err) {
    throw err;
  }
  if (res.data) {
    return res.data;
  }
  return [];
};

const cancel = () => {
  visible.value = false;
  modalFormRef.value?.resetFields();
  pageState.file = {};
  pageState.doctorInfo = {};
  emits("fetchApi");
};

const handleClear = () => {
  pageState.doctorInfo = {};
  modalFormRef.value?.resetFields();
  pageState.file = {};
  modalFormRef.value?.setFields({
    customerType: { value: "3" }, // 类型
  });
};

const handleUploadChange = (_: any, currentFile: any) => {
  // 上传开始时设置loading
  if (currentFile.status === "uploading") {
    pageState.uploadLoading = true;
  }

  // 上传完成时处理结果
  if (currentFile.status === "done") {
    const { data } = currentFile?.response || {};
    pageState.file = { ...currentFile, url: data?.uri };
    pageState.uploadLoading = false;
  }

  // 上传失败时关闭loading
  if (currentFile.status === "error") {
    pageState.uploadLoading = false;
    Message.error("上传失败，请重试");
  }
};

// 添加上传前的文件大小检查
const beforeUpload = (file: File) => {
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    Message.error("上传头像大小不能超过10MB");
    return false;
  }

  const isJpgOrPng =
    file.type === "image/jpeg" ||
    file.type === "image/png" ||
    file.type === "image/jpg";
  if (!isJpgOrPng) {
    Message.error("只能上传JPG/PNG格式的图片");
    return false;
  }

  return true;
};

onUpdated(async () => {
  if (visible.value && props.type === ModalType.edit) {
    getManagementUserAdminDetail();
  }
});

const getManagementUserAdminDetail = async () => {
  pageState.loading = true;
  const [err, res] = await to(
    apis.getBdManageApiServicePersonnelManagementQueryManagementUserAdminDetail(
      { userId: props?.userId as unknown as string },
    ),
  );
  pageState.loading = false;
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    const { data } = res || {};
    const transformedData: any = {};
    if (data) {
      Object.keys(data).forEach(key => {
        transformedData[key] = { value: data[key as keyof IData] };
        if (["customerType", "userTitle"]?.includes(key)) {
          transformedData[key] = { value: `${data[key as keyof IData]}` };
        }
      });
      pageState.file = { url: data?.avatar };
    }
    const tagIds = data?.tagList?.map(item => item?.id);
    modalFormRef.value?.setFields(
      Object.assign(transformedData, { tagList: { value: tagIds } }),
    );
  }
};

watch(
  () => pageState.doctorInfo,
  newDoctorInfo => {
    pageState.file = { uid: uniqueId(), url: newDoctorInfo?.picture };
    const tagIds = newDoctorInfo?.tag_list?.map(item => item?.value);
    if (!isEmpty(newDoctorInfo)) {
      pageState.disabled = true;
      modalFormRef.value?.setFields({
        name: { value: newDoctorInfo?.name },
        customerType: { value: "0" }, // 类型
        userTitle: {}, // 头衔
        certNo: { value: newDoctorInfo?.idCard }, // 身份证号码
        professionalTitleId: {}, // 职称
        organCode: { value: newDoctorInfo?.org_code }, // 所属医院
        departmentId: { value: newDoctorInfo?.major_code }, // 科室
        phone: { value: newDoctorInfo?.phone }, // 电话
        tag_list: { value: tagIds },
        speciality: { value: newDoctorInfo?.specialty }, // 擅长
        socialCognition: { value: "" }, // 社会认知
        brief: { value: newDoctorInfo?.introduce }, // 简介
      });
    } else {
      pageState.disabled = false;
    }
  },
);
</script>

<script lang="ts">
export type IForm =
  Partial<apis.postBdManageApiServicePersonnelManagementEditManagementUserAdminRequest>;
type IData =
  apis.QueryManagementUserAdminDetailUsingGETManagementUserAdminDetailVo;
type IState = {
  loading: boolean;
  form: Partial<apis.postBdManageApiServicePersonnelManagementEditManagementUserAdminRequest>;
  file: any;
  doctorInfo: apis.GetInternetDoctorListUsingPOSTDoctorInfo;
  disabled: boolean;
  uploadLoading: boolean; // 添加上传loading类型
};

type Props = {
  type: IModalType;
  userId: number | undefined;
  dictionaryState: IDictionaryState<apis.DictTypeUsingGETSysDictData[]>;
};
</script>

<template>
  <a-drawer
    unmount-on-close
    :width="620"
    :visible="visible"
    :mask-closable="true"
    :on-before-ok="handleOnOk"
    :title="title"
    @cancel="cancel"
  >
    <template #footer>
      <a-space>
        <a-button v-if="type !== ModalType.edit" @click="handleClear"
          >清除数据</a-button
        >
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="handleOnOk">确定</a-button>
      </a-space>
    </template>
    <a-spin
      :loading="pageState.loading"
      :style="{
        width: '100%',
      }"
    >
      <DoctorSelect
        v-if="type === ModalType.add"
        v-model="pageState.doctorInfo"
      />
      <a-upload
        :action="uploadUrl"
        accept="image/jpeg,image/png,image/jpg"
        :file-list="pageState.file ? [pageState.file] : []"
        :show-file-list="false"
        :before-upload="beforeUpload"
        :headers="{
          authorization: `Bearer ${token}`,
          accesstoken: token as string,
        }"
        @change="handleUploadChange"
      >
        <template #upload-button>
          <div class="upload-avatar-container">
            <a-spin :loading="pageState.uploadLoading">
              <a-avatar
                :trigger-icon-style="{ color: '#3491FA' }"
                :auto-fix-font-size="false"
                :size="72"
                :style="{ marginBottom: '8px', marginTop: '20px' }"
              >
                <img :src="pageState.file?.url || getImageSrc('avatar.png')" />
                <template #trigger-icon>
                  <IconCamera />
                </template>
              </a-avatar>
            </a-spin>
            <div class="upload-tips">
              <div class="tip-text">支持JPG、PNG格式</div>
              <div class="tip-text">文件大小不超过10MB</div>
            </div>
          </div>
        </template>
      </a-upload>
      <a-form
        ref="modalFormRef"
        :model="pageState.form"
        layout="vertical"
        label-align="left"
        class="serviceAppointmentForm"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              field="name"
              label="姓名"
              :disabled="pageState.disabled"
              :rules="[
                {
                  required: true,
                  message: '请输入姓名',
                },
              ]"
            >
              <a-input v-model="pageState.form.name" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              field="customerType"
              label="类型"
              disabled
              :rules="[
                {
                  required: true,
                  message: '请选择类型',
                },
              ]"
            >
              <a-select
                v-model="pageState.form.customerType"
                :loading="dictionaryState.typeLoading"
                :fallback-option="false"
              >
                <a-option
                  v-for="item in dictionaryState.typeList"
                  :key="item?.dictCode"
                  :value="item?.dictValue"
                  >{{ item?.dictLabel }}</a-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              field="userTitle"
              label="头衔"
              :rules="[
                {
                  required: true,
                  message: '请选择头衔',
                },
              ]"
            >
              <a-select
                v-model="pageState.form.userTitle"
                :fallback-option="false"
                allow-search
              >
                <a-option
                  v-for="item in dictionaryState.titleTypeList"
                  :key="item?.dictValue"
                  :value="item?.dictValue"
                  >{{ item?.dictLabel }}</a-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              field="professionalTitleId"
              :label="`职称${pageState.doctorInfo?.title ? `(${pageState.doctorInfo?.title})` : ''}`"
              :rules="[
                {
                  required: false,
                  message: '请选择职称',
                },
              ]"
            >
              <a-select
                v-model="pageState.form.professionalTitleId"
                :fallback-option="false"
                allow-search
              >
                <a-option
                  v-for="item in dictionaryState.professionalTitleList"
                  :key="item?.professionalTitleId"
                  :value="item?.professionalTitleId"
                  >{{ item?.name }}</a-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              field="organCode"
              label="所属医院"
              :rules="[
                {
                  required: false,
                  message: '请选择所属医院',
                },
              ]"
            >
              <a-select
                v-model="pageState.form.organCode"
                :fallback-option="false"
                allow-search
              >
                <a-option
                  v-for="item in dictionaryState.organList"
                  :key="item?.organCode"
                  :value="item?.organCode"
                  >{{ item?.name }}</a-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              field="departmentId"
              :label="`所属科室${pageState.doctorInfo?.clinic_name ? `(${pageState.doctorInfo?.clinic_name})` : ''}`"
              :rules="[
                {
                  required: false,
                  message: '请选择所属科室',
                },
              ]"
            >
              <a-select
                v-model="pageState.form.departmentId"
                :fallback-option="false"
                allow-search
              >
                <a-option
                  v-for="item in dictionaryState.departmentList"
                  :key="item?.xid"
                  :value="`${item?.xid}`"
                  >{{ item?.secondName }}</a-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              field="certNo"
              label="身份证号码"
              :rules="[
                {
                  required: true,
                  message: '请输入身份证号码',
                },
              ]"
            >
              <a-input v-model="pageState.form.certNo" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              field="phone"
              label="联系电话"
              :rules="[
                {
                  required: true,
                  message: '请输入联系电话',
                },
              ]"
            >
              <a-input v-model="pageState.form.phone" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              field="practicingLicenseNumber"
              label="执业许可证编号"
              :rules="[
                {
                  required: false,
                  message: '请输入执业许可证编号',
                },
              ]"
            >
              <a-input v-model="pageState.form.practicingLicenseNumber" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              field="tagList"
              label="标签"
              :rules="[
                {
                  required: false,
                  message: '请选择标签',
                },
              ]"
            >
              <a-select
                v-model="pageState.form.tagList"
                :fallback-option="false"
                multiple
                allow-search
              >
                <a-option
                  v-for="item in dictionaryState.tagList"
                  :key="item?.id"
                  :value="item?.id"
                  >{{ item?.name }}</a-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              field="speciality"
              label="擅长领域"
              :rules="[
                {
                  required: false,
                  message: '请输入擅长领域',
                },
              ]"
            >
              <a-textarea
                v-model="pageState.form.speciality"
                placeholder="请输入...."
                :auto-size="{ minRows: 5, maxRows: 10 }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              field="socialCognition"
              label="社会任职"
              :rules="[
                {
                  required: false,
                  message: '请输入社会任职',
                },
              ]"
            >
              <a-textarea
                v-model="pageState.form.socialCognition"
                placeholder="请输入...."
                :auto-size="{ minRows: 5, maxRows: 10 }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              field="brief"
              label="简介"
              :rules="[
                {
                  required: false,
                  message: '请输入简介',
                },
              ]"
            >
              <a-textarea
                v-model="pageState.form.brief"
                placeholder="请输入...."
                :auto-size="{ minRows: 5, maxRows: 10 }"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>
.upload-avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;

  .upload-tips {
    text-align: center;
    margin-bottom: 20px;

    .tip-text {
      font-size: 12px;
      color: #86909c;
      line-height: 16px;
    }
  }
}
</style>
