<!--
* @description 服务人员详情
* @fileName details.vue
* <AUTHOR>
* @date 2025/07/08 15:51:48
!-->
<script setup lang="ts">
import { ref, onUpdated } from "vue";
import to from "await-to-js";
import * as apis from "@/batchApi";
import { renderCell } from "@/utils";
import { infoColumn } from "../../config";

type IData =
  apis.QueryManagementUserAdminDetailUsingGETManagementUserAdminDetailVo;
const visibleLook = defineModel<boolean>("visibleLook", { required: true });

const props = defineProps<{ userId: number | undefined }>();
const data = ref<IData>({});
const loading = ref<boolean>(false);

const getManagementUserAdminDetail = async () => {
  loading.value = true;
  const [err, res] = await to(
    apis.getBdManageApiServicePersonnelManagementQueryManagementUserAdminDetail(
      { userId: props?.userId as unknown as string },
    ),
  );
  loading.value = false;
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    data.value = res?.data as IData;
  }
};

onUpdated(() => {
  if (visibleLook.value) {
    getManagementUserAdminDetail();
  }
});
</script>

<template>
  <a-drawer
    unmount-on-close
    :width="520"
    :visible="visibleLook"
    :mask-closable="true"
    body-class="appointmentView"
    :footer="false"
    title="详细信息"
    @cancel="visibleLook = false"
  >
    <a-spin
      :loading="loading"
      :style="{
        width: '100%',
      }"
    >
      <div class="contentView">
        <!-- baseInfo -->
        <div class="cardView baseInfo">
          <a-avatar :size="72">
            <img alt="avatar" :src="data?.avatar" />
          </a-avatar>
          <div>
            <span>{{ data?.name ?? "-" }}</span>
            <span
              >{{ data?.organName ?? "-"
              }}{{ data?.departmentName ?? "-" }}</span
            >
            <span>{{ data?.phone }}</span>
          </div>
        </div>
        <!-- info -->
        <div class="cardView">
          <a-row :gutter="[0, 10]">
            <template v-for="item in infoColumn" :key="item.attrName">
              <a-col :span="item.span?.[0]" style="color: #86909c">
                {{ item?.label }}
              </a-col>
              <a-col :span="item.span?.[1]">
                <template v-if="item?.render">
                  <component :is="renderCell(item.render(data))" />
                </template>
                <template v-else>
                  {{ data?.[item.attrName as keyof IData] ?? "-" }}
                </template>
              </a-col>
            </template>
          </a-row>
        </div>
        <div class="cardView">
          <div class="title">擅长领域</div>
          <div>
            {{ data?.speciality ?? "-" }}
          </div>
        </div>
        <div class="cardView">
          <div class="title">社会任职</div>
          <div>
            {{ data?.socialCognition ?? "-" }}
          </div>
        </div>
        <div class="cardView">
          <div class="title">简介</div>
          <div>
            {{ data?.brief ?? "-" }}
          </div>
        </div>
      </div>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>
.contentView {
  font-size: 14px;
  font-weight: normal;

  .cardView {
    background: white;
    padding: 20px;
    margin-bottom: 12px;
    .title {
      color: #86909c;
      margin-bottom: 8px;
    }
  }

  .baseInfo {
    display: flex;
    gap: 16px;
    color: rgba(0, 0, 0, 0.7);
    > div {
      display: flex;
      flex-direction: column;
      gap: 2px;
      > span:first-child {
        color: #000000;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
}
</style>
