<!--
 * @Description: 选中用户进行展示的组件
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-12-18 09:40:05
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-04-27 15:42:36
-->
<template>
  <div class="bd-user-select-container" :class="{ disabled: disabled }">
    <a-trigger
      v-if="!disabled"
      :popup-visible="resultVisible"
      :click-outside-to-close="true"
      :click-to-close="false"
      auto-fit-popup-width
      @popup-visible-change="visible => (resultVisible = visible)"
    >
      <a-input-search
        v-model="searchValue"
        :style="{ width: '100%' }"
        placeholder="请输入医生名称进行搜索"
        search-button
        @search="handleSearch"
        @press-enter="handleSearch"
      />
      <template #content>
        <!-- 用于展示获取到的表格数据 -->
        <a-spin :loading="associationLoading" tip="正在拉取医生，请稍后...">
          <a-table
            :loading
            :columns="columns"
            :data="tableData"
            :pagination="false"
            :scroll="{ y: 200 }"
            @row-dblclick="handleTableDbClick"
          />
        </a-spin>
      </template>
    </a-trigger>
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js";
import { useFormItem } from "@arco-design/web-vue";
import { ref, watch } from "vue";
import * as apis from "@/batchApi";
import { getImageSrc } from "@/utils";
import { useQuickAccessStore } from "@/store";

type IRecord = apis.GetInternetDoctorListUsingPOSTDoctorInfo;

const searchValue = ref();
const resultVisible = ref(false);
const patientInfo = ref<IRecord>();
const tableData = ref<IRecord[]>([]);
const loading = ref(false);
const associationLoading = ref(false);

const columns = [
  {
    title: "姓名",
    dataIndex: "name",
  },
  {
    width: 125,
    title: "电话",
    dataIndex: "phone",
  },
  {
    title: "职称",
    dataIndex: "title",
    width: 130,
  },
  {
    title: "医院",
    dataIndex: "org_name",
    width: 230,
  },
];

const { mergedDisabled, eventHandlers } = useFormItem();

const disabled = mergedDisabled.value;

const { modelValue } = defineProps(["modelValue"]);

const emits = defineEmits(["update:modelValue"]);

// 双击表格行的时候 触发的方法
const handleTableDbClick = (record: IRecord, ev: Event) => {
  // 然后设置新的数据
  handleInput(record);
  resultVisible.value = false;
  patientInfo.value = record;
  searchValue.value = undefined;
  // 设置当前选中的数据
};

// 点击搜索的时候 获取对应的数据
const handleSearch = () => {
  if (typeof searchValue.value === "string" && searchValue.value !== "") {
    resultVisible.value = true;
    getDoctorList(searchValue.value);
  }
};

// 设置当前选中的医生数据
const handleInput = (record: IRecord) => {
  const ev = {
    target: {
      value: record,
    },
  };
  emits("update:modelValue", ev.target.value);
  eventHandlers.value?.onChange?.(ev as unknown as InputEvent);
};

// 初始获取医生的数据
const getDoctorList = async (_keyword: string | undefined) => {
  loading.value = true;
  const [err, res] = await to(
    apis.postBdManageApiServiceReservationGetInternetDoctorList({
      name: _keyword,
    }),
  );
  loading.value = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    tableData.value = res.data;
  }
  return res.data;
};

watch(
  () => modelValue,
  () => {
    // 如果modelValue 发生了变化 根据这个数据来更新本地的数据
    if (typeof modelValue?.idCard === "string" && modelValue?.idCard !== "") {
      patientInfo.value = modelValue;
    }
  },
);
</script>

<style lang="scss" scoped>
.bd-user-select-container {
  width: 100%;
  &.disabled {
    pointer-events: none;
  }
}
</style>
