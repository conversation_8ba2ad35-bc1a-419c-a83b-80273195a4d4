<!--
* @description 服务人员管理
* @fileName index.vue
* <AUTHOR>
* @date 2025/07/02 09:51:18
!-->
<script setup lang="ts">
import { reactive, onMounted } from "vue";
import {
  Message,
  PaginationProps,
  TableChangeExtra,
  TableData,
} from "@arco-design/web-vue";
import { to } from "await-to-js";
import * as apis from "@/batchApi";
import { waitstaffColumn, ModalType, IModalType } from "../config";
import tableFilter from "../components/table-filter.vue";
import Details from "./components/details.vue";
import AddAndEditForm from "./components/addAndEditForm.vue";

const pageState = reactive<IState>({
  loading: false,
  synchronizeLoading: false,
  visible: false,
  visibleLook: false,
  isShowPagination: false,
  data: [],
  type: ModalType.add,
  keyWord: undefined,
  editID: undefined,
  userId: undefined,
});

const dictionaryState = reactive<
  IDictionaryState<apis.DictTypeUsingGETSysDictData[]>
>({
  loading: false,
  // 类型
  typeLoading: false,
  typeList: [],
  // 头衔
  titleTypeLoading: false,
  titleTypeList: [],
  // 标签
  tagLoading: false,
  tagList: [],
  // 机构
  organLoading: false,
  organList: [],
  // 科室
  departmentLoading: false,
  departmentList: [],
  // 职称
  professionalTitleList: [],
});

const pagination = reactive<PaginationProps>({
  size: "small",
  showTotal: true,
  showJumper: true,
  showPageSize: true,
  total: 0,
  current: 1,
  pageSize: 10,
});

const getList = async () => {
  pageState.loading = true;
  const [err, res] = await to(
    apis.postBdManageApiServicePersonnelManagementQueryManagementUserAdminPage({
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      userName: pageState.keyWord,
    }),
  );
  pageState.loading = false;
  if (err) {
    throw err;
  }
  if (res.code === "1") {
    pageState.data = res.data?.records as Record[];
    pagination.total = res?.data?.total;
  }
};

const handleEdit = (record: Record) => {
  pageState.editID = record?.xid;
  pageState.userId = record?.userId;
  pageState.type = ModalType.edit;
  pageState.visible = true;
};

const handleSynchronize = async () => {
  pageState.synchronizeLoading = true;
  const [err, res] = await to(
    apis.getBdManageApiServicePersonnelManagementSyncCrmUser(),
  );
  pageState.synchronizeLoading = false;
  if (err) {
    pageState.synchronizeLoading = false;
    throw err;
  }
  if (res?.code === "1") {
    Message.success("同步成功");
  }
};

const handleTableChange = (_data: TableData, extra: TableChangeExtra) => {
  const { page, pageSize, type, filters } = extra || {};
  // 如果有filter条件隐藏分页
  if (type === "filter" && filters) {
    const hasValidArray = Object.values(filters)?.some(
      v => Array.isArray(v) && v.length > 0,
    );
    pageState.isShowPagination = hasValidArray;
  }
  if (type === "pagination") {
    pagination.current = page;
    pagination.pageSize = pageSize;
    getList();
  }
};

const handleDictData = async (_dictType: string) => {
  const [err, res] = await to(
    apis.getBdManageApiSystemDictDataTypeByDictType({
      dictType: _dictType,
    }),
  );
  if (err) {
    throw err;
  }
  if (res.data) {
    return res.data;
  }
  return [];
};

const handleSearch = ({ keyWord }: { keyWord: string }) => {
  pageState.keyWord = keyWord;
  getList();
};

const getEnumAll = async () => {
  try {
    dictionaryState.loading = true;
    const params = { pageNum: 1, pageSize: 999 };
    const fetchList = [
      apis.postBdManageApiTagPage(params),
      apis.postBdManageApiOrganPage(params),
      apis.postBdManageApiDepartmentPage({ ...params, query: { floor: 2 } }),
      apis.getBdManageApiSystemDictDataTypeByDictType({
        dictType: "service_type",
      }),
      apis.getBdManageApiSystemDictDataTypeByDictType({
        dictType: "user_title_type",
      }),
      apis.postBdManageApiProfessionalTitleQueryProfessionalTitleByName({}),
    ];
    const results = await Promise.allSettled(fetchList);
    dictionaryState.loading = false;
    // 处理每一项
    const tagData =
      results[0].status === "fulfilled"
        ? (results[0].value?.data as apis.PageUsingPOST_2HxPagePatientTag)
            ?.content
        : [];
    const organData =
      results[1].status === "fulfilled"
        ? (results[1].value?.data as apis.PageUsingPOSTHxPageOrganOrgan)
            ?.content
        : [];
    const departmentData =
      results[2].status === "fulfilled"
        ? (
            results[2].value
              .data as apis.PageUsingPOST_1HxPagePatientDepartmentVo
          )?.content
        : [];
    const serviceTypeData =
      results[3].status === "fulfilled"
        ? (results[3].value?.data as apis.DictTypeUsingGETSysDictData[])
        : [];
    const titleTypeData =
      results[4].status === "fulfilled"
        ? (results[4].value?.data as apis.DictTypeUsingGETSysDictData[])
        : [];
    const professionalTitleData =
      results[5].status === "fulfilled"
        ? (results[5].value
            ?.data as apis.postBdManageApiProfessionalTitleAddProfessionalTitleResponse["data"])
        : [];
    dictionaryState.tagList = tagData;
    dictionaryState.organList = organData;
    dictionaryState.departmentList = departmentData;
    dictionaryState.typeList = serviceTypeData;
    dictionaryState.titleTypeList = titleTypeData;
    dictionaryState.professionalTitleList = professionalTitleData ?? [];
  } catch (error) {
    dictionaryState.loading = false;
  }
};

onMounted(async () => {
  // pageState.organLoading = true;
  // pageState.organList = await handleDictData("org_type");
  // pageState.organLoading = false;
});

getList();
getEnumAll();
</script>

<script lang="ts">
type Record =
  apis.QueryManagementUserAdminPageUsingPOSTFuWuRenYuanGuanLiFanHuiXinXi;

interface IState {
  loading: boolean;
  synchronizeLoading: boolean;
  visible: boolean;
  visibleLook: boolean;
  isShowPagination: boolean;
  data: Record[];
  type: Extract<IModalType, "add" | "edit">;
  keyWord: string | undefined;
  editID: number | undefined;
  userId: number | undefined;
}
export type IDictionaryState<T> = {
  loading: boolean;
  // 类型
  typeLoading: boolean;
  typeList: T | undefined;
  // 头衔
  titleTypeLoading: boolean;
  titleTypeList: T | undefined;
  // 职称
  professionalTitleList: apis.postBdManageApiProfessionalTitleAddProfessionalTitleResponse["data"];
  // 标签
  tagLoading: boolean;
  tagList: apis.PageUsingPOST_2PatientTag[] | undefined;
  // 机构
  organLoading: boolean;
  organList: apis.PageUsingPOSTOrganOrgan[] | undefined;
  // 科室
  departmentLoading: boolean;
  departmentList: apis.PageUsingPOST_1PatientDepartmentVo[] | undefined;
};
</script>

<template>
  <div class="table-wrapper">
    <tableFilter @search="handleSearch" @handle-reset="handleSearch">
      <template #operate>
        <a-button
          type="primary"
          @click="
            pageState.visible = true;
            pageState.editID = undefined;
            pageState.type = ModalType.add;
          "
        >
          <template #icon> <icon-plus /> </template>新增</a-button
        >
        <a-button
          :loading="pageState.synchronizeLoading"
          type="primary"
          @click="handleSynchronize"
        >
          <template #icon> <icon-sync /> </template>同步信息</a-button
        >
      </template>
    </tableFilter>
    <div class="table-container">
      <a-table
        :loading="pageState.loading"
        row-key="xid"
        :columns="
          waitstaffColumn({
            typeList: dictionaryState.typeList,
            titleTypeList: dictionaryState.titleTypeList,
            professionalTitleList: dictionaryState.professionalTitleList,
            tagList: dictionaryState.tagList,
            organList: dictionaryState.organList,
            departmentList: dictionaryState.departmentList,
          })
        "
        :data="pageState.data"
        :scroll="{ x: 1600, y: '100%' }"
        scrollbar
        :pagination="pageState.isShowPagination || pagination"
        @page-change="(current: number) => (pagination.current = current)"
        @page-size-change="
          (pageSize: number) => (pagination.pageSize = pageSize)
        "
        @change="handleTableChange"
      >
        <template #operate="{ record }">
          <a-space>
            <a-button
              size="mini"
              type="outline"
              @click="
                pageState.visibleLook = true;
                pageState.userId = record?.userId;
              "
              >查看</a-button
            >
            <a-button size="mini" type="outline" @click="handleEdit(record)">
              编辑
            </a-button>
          </a-space>
        </template>
      </a-table>
    </div>
    <!-- 新增编辑 -->
    <AddAndEditForm
      v-model:visible="pageState.visible"
      :type="pageState.type"
      :user-id="pageState.userId"
      :dictionary-state="dictionaryState"
      @fetch-api="getList"
    />
    <!-- 详情 -->
    <Details
      v-model:visible-look="pageState.visibleLook"
      :user-id="pageState.userId"
    />
  </div>
</template>

<style lang="scss" scoped>
.table-wrapper {
  background-color: white;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  .table-operate {
    display: flex;
    gap: 8px;
  }
  .table-container {
    overflow-y: scroll;
  }
}
</style>
