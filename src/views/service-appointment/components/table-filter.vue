<!--
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2024-12-12 18:11:20
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-04-24 10:09:12
-->
<template>
  <div class="table-filter">
    <a-form :model="form" layout="inline">
      <!-- <div class="form-view">
        <a-form-item field="reservationType" label="预约类型">
          <a-select
            v-model="form.reservationType"
            :style="{ width: '180px' }"
            placeholder="请选择"
          >
            <a-option :value="0">全部</a-option>
            <a-option :value="1">已下单</a-option>
            <a-option :value="2">下单成功</a-option>
            <a-option :value="3">已取消</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="serviceId" label="服务项目">
          <a-select
            v-model="form.serviceId"
            :style="{ width: '180px' }"
            placeholder="请选择"
            allow-clear
          >
            <a-option
              v-for="item in projectList"
              :key="item?.serviceId"
              :value="item?.serviceId"
            >
              {{ item.serviceName }}
            </a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="expirationDate" label="权益到期日期">
          <a-date-picker
            v-model="form.expirationDate"
            style="width: 180px"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </a-form-item>
      </div> -->
      <a-form-item class="keyword-search" field="nameOrPhone">
        <bd-input
          width="253px"
          field="keyWord"
          placeholder="输入姓名/电话"
          @search="handleSearch"
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch, ref, onMounted } from "vue";
import bdInput from "@/components/bd-input/index.vue";
import * as apis from "@/batchApi";
import to from "await-to-js";

const projectList = ref<any[]>([]);

const form = reactive({
  // 权益到期日期
  expirationDate: undefined,
  // 服务分类
  serviceId: undefined,
  // status
  reservationType: undefined,
});

const emits = defineEmits(["search"]);

watch(
  () => form,
  () => {
    emits("search", form);
  },
  {
    deep: true,
  },
);

// 点击确定的时候 调用对应的方法
const handleSearch = (_searchValue: any) => {
  emits("search", { ...form, ..._searchValue });
};

/* 获取服务项目 */
const getServiceItems = async () => {
  const [err, res] = await to(
    apis.postBdManageApiServiceReservationQueryServiceItems(),
  );
  if (err) {
    throw err;
  }
  if (res?.data) {
    projectList.value = res?.data;
  }
};

onMounted(async () => {
  getServiceItems();
});
</script>

<style lang="scss" scoped>
.table-filter {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  :deep(.arco-form-layout-inline .arco-form-item) {
    margin-bottom: 0px !important;
  }
  > .arco-form {
    .form-view {
      display: flex;
    }
    justify-content: space-between;
    div.arco-form-item {
      margin-bottom: 0px;
      margin-right: var(--spacing-7);
      &.keyword-search {
        margin-right: 0px;
        :deep(.arco-form-item-label-col) {
          padding-right: 0px;
        }
      }
    }
  }
}
</style>
