<!--
* @description 服务预约记录
* @fileName details.vue
* <AUTHOR>
* @date 2025/06/06 09:05:50
!-->
<script setup lang="ts">
import { ref, watch, onBeforeUnmount } from "vue";
import { getImageSrc, renderCell } from "@/utils";
import * as apis from "@/batchApi";
import to from "await-to-js";
import { isEmpty } from "lodash";
import VuePdfEmbed from "vue-pdf-embed";
import cardView from "@/components/global-component/formContent/cardView.vue";
import { imageExtensions } from "@/constant";
import pdfPng from "@/assets/images/<EMAIL>";
import InfoRow from "@/components/InfoRow/index.vue";
import {
  serviceInformation,
  OrderInfo,
  BaseHealthInfo,
  meetingInfo,
} from "../config";

type IData = apis.GetServiceReservationByIdUsingPOSTServiceReservationDto;

const visible = defineModel<boolean>("visible", { required: true });
const props = defineProps<{
  detailsId: string | undefined;
  consultationId: string | undefined;
}>();

const loading = ref<boolean>(false);
const okLoading = ref<boolean>(false);
const data = ref<IData | any>();
const meetingInfoData =
  ref<
    apis.getBdManageApiServiceReservationGetInternetOrderDetailResponse["data"]
  >();
const pdfVisible = ref(false);
const pdfSrc = ref();

const format = (item: any) => {
  const { attributeName, enumMap } = item || {};
  if (!attributeName) return "-";
  const result = data.value?.[attributeName];
  if (enumMap) {
    return enumMap[result] ?? "-";
  }
  return result ?? "-";
};

const getDetails = async () => {
  loading.value = true;
  const res: any = await Promise.allSettled([
    apis.postBdManageApiServiceReservationGetServiceReservationById({
      id: props?.detailsId,
    }),
    apis.getBdManageApiServiceReservationGetInternetOrderDetail({
      consultationId: props?.consultationId as string,
    }),
  ]);
  loading.value = false;
  data.value = res[0].value?.data as IData;
  meetingInfoData.value = res[1].value?.data
    ?.data as apis.getBdManageApiServiceReservationGetInternetOrderDetailResponse["data"];
};

watch(visible, newVal => {
  if (newVal) {
    getDetails();
  }
});
const handleOk = async () => {
  // okLoading.value = true;
  // const [err, res] = await to(
  //   apis.postBdClientServiceOperationUpdateServicePackageStatus({
  //     id: props?.detailsId,
  //   }),
  // );
  // okLoading.value = false;
  // if (err) {
  //   Message.error("更新失败");
  //   throw err;
  // }
  // if (res.code === "1") {
  //   Message.success("更新成功");
  // }
};

const differentTypes = (list: any[]) => {
  if (!list?.length) return;
  // 1 报告 2 知情同意书
  const reportList = list?.filter(item => item?.type === "1");
  const letterList = list?.filter(item => item?.type === "2");
  return { reportList, letterList };
};

const handlePerviewPdf = (url: string) => {
  pdfSrc.value = url;
  pdfVisible.value = true;
};

onBeforeUnmount(() => {
  data.value = {};
});
</script>

<template>
  <a-drawer
    :width="630"
    :visible="visible"
    :footer="isEmpty(data)"
    unmount-on-close
    title="服务预约详情"
    body-class="appointmentDetailsView"
    ok-text="确定"
    :ok-loading="okLoading"
    @ok="handleOk"
    @cancel="visible = false"
  >
    <a-spin class="conainer" tip="加载中..." :loading="loading">
      <div v-if="isEmpty(data)" class="emptyView">
        <a-empty />
      </div>
      <div v-else class="details-view">
        <!-- 服务信息 -->
        <cardView title="服务信息" :card-style="{ marginBottom: '20px' }">
          <a-row :gutter="[0, 14]">
            <template
              v-for="item in serviceInformation"
              :key="item.attributeName"
            >
              <template v-if="data?.firstVisitType">
                <a-col :span="item.col?.name" style="color: #86909c">{{
                  item.title
                }}</a-col>
                <a-col :span="item?.col.value">
                  {{ format(item) }}
                </a-col>
              </template>
              <template v-else>
                <a-col :span="item.col?.name" style="color: #86909c">{{
                  item.title
                }}</a-col>
                <a-col :span="item?.col.value">
                  {{ format(item) }}
                </a-col>
              </template>
            </template>
          </a-row>
        </cardView>

        <!-- 预约信息 -->
        <cardView title="预约信息">
          <a-row :gutter="[0, 14]">
            <a-col :span="4" style="color: #86909c">已选权益人</a-col>
            <a-col :span="20">
              <div class="userView">
                <div
                  v-for="item in data?.detailList"
                  :key="item?.id"
                  class="userInfo"
                >
                  <div class="content">
                    <img :src="getImageSrc('woman.png')" />
                    <div class="patindUserInfo">
                      <span class="title">{{ item?.patientName ?? "-" }}</span>
                      <span
                        >{{ item?.patientAge ?? "-" }}岁
                        {{
                          item?.gender === 1
                            ? "男"
                            : item?.gender === 2
                              ? "女"
                              : "未知"
                        }}</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </a-col>
            <a-col v-if="data?.firstVisitType" :span="4" style="color: #86909c"
              >首诊类型</a-col
            >
            <a-col v-if="data?.firstVisitType" :span="20">{{
              data?.firstVisitType === "0"
                ? "线上首诊"
                : data?.firstVisitType === "1"
                  ? "线下首诊"
                  : "-"
            }}</a-col>
            <a-col :span="4" style="color: #86909c">预约时间</a-col>
            <a-col :span="20">{{ data?.appointTime ?? "-" }}</a-col>
            <template v-if="data?.firstVisitType === '1'">
              <a-col :span="4" style="color: #86909c">是否预留车位</a-col>
              <a-col :span="20">{{
                data?.isReservedParking ? "是" : "否"
              }}</a-col>
            </template>
            <template v-if="data?.isReservedParking">
              <a-col :span="4" style="color: #86909c">预留车位时间</a-col>
              <a-col :span="20">{{ data?.reservedParkingTime ?? "-" }}</a-col>
            </template>
          </a-row>
        </cardView>

        <div v-for="item in data?.detailList" :key="item">
          <div class="iconView">
            <img :src="getImageSrc('quanyi.png')" class="" />
            <span>权益人：{{ item?.patientName }}</span>
          </div>
          <!-- 基本健康信息 -->
          <cardView
            v-if="
              item?.hasCurrentHistory !== null ||
              item?.hasPastDisease !== null ||
              item?.hasAllergyHistory !== null
            "
            title="基本健康信息"
            :card-style="{ marginBottom: '20px' }"
          >
            <a-row
              v-for="health in BaseHealthInfo"
              :key="health.attributeName"
              :gutter="[0, 14]"
            >
              <a-col :span="health.col?.name" style="color: #86909c">{{
                health.title
              }}</a-col>
              <a-col :span="health?.col.value">
                {{ item?.[health.attributeName1] === true ? "有" : "无" }}&emsp;
                {{ item?.[health.attributeName] }}
              </a-col>
            </a-row>
          </cardView>
          <!-- 首诊描述 -->
          <cardView title="首诊描述" :card-style="{ paddingBottom: '20px' }">
            <div>
              {{ item?.description ?? "-" }}
            </div>
          </cardView>

          <!-- 上传报告 -->
          <cardView
            v-if="differentTypes(item?.fileList2)?.reportList?.length"
            title="报告"
            :card-style="{ marginBottom: '20px' }"
          >
            <div class="reportView">
              <template
                v-for="file in differentTypes(item?.fileList2)?.reportList"
                :key="file?.id"
              >
                <a-image
                  v-if="imageExtensions?.includes(file?.fileType)"
                  width="64px"
                  height="64px"
                  :src="file?.threeFilePath"
                />
                <img
                  v-if="file?.fileType === 'pdf'"
                  :src="pdfPng"
                  @click="handlePerviewPdf(file.threeFilePath)"
                />
              </template>
            </div>
          </cardView>
          <cardView
            v-if="differentTypes(item?.fileList2)?.letterList?.length"
            title="知情同意书"
            :card-style="{ marginBottom: '20px' }"
          >
            <div class="reportView">
              <template
                v-for="file in differentTypes(item?.fileList2)?.letterList"
                :key="file?.id"
              >
                <a-image
                  v-if="imageExtensions?.includes(file?.fileType)"
                  width="64px"
                  height="64px"
                  :src="file?.threeFilePath"
                />
                <img
                  v-if="file?.fileType === 'pdf'"
                  :src="pdfPng"
                  @click="handlePerviewPdf(file.threeFilePath)"
                />
              </template>
            </div>
          </cardView>
        </div>

        <!-- 订单信息 -->
        <cardView title="订单信息">
          <a-row
            v-for="item in OrderInfo"
            :key="item.attributeName"
            :gutter="[0, 14]"
          >
            <a-col :span="item.col?.name" style="color: #86909c">{{
              item.title
            }}</a-col>
            <a-col :span="item?.col.value">
              {{ format(item) }}
            </a-col>
          </a-row>
        </cardView>
        <cardView title="腾讯会议信息" :card-style="{ marginBottom: '20px' }">
          <InfoRow :data="meetingInfoData" :items="meetingInfo" />
        </cardView>
      </div>
    </a-spin>
    <!-- 单独pdf预览的组件 -->
    <a-modal
      v-model:visible="pdfVisible"
      title="pdf预览"
      width="800px"
      :mask-closable="false"
      :footer="false"
      unmount-on-close
      @cancel="pdfVisible = false"
    >
      <div style="height: 80vh; overflow: auto">
        <vue-pdf-embed ref="pdfRef" :source="pdfSrc" />
      </div>
    </a-modal>
  </a-drawer>
</template>

<style lang="scss" scoped>
.conainer {
  height: 100%;
  width: 100%;
}

.name {
  color: #86909c;
  font-size: 14px;
}
.value {
  color: #000000;
}
.details-view {
  padding-bottom: 20px;
}
.emptyView {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.userView {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;

  .userInfo {
    display: flex;

    .content {
      width: 176px;
      padding: 16px;
      border-radius: 8px;
      padding-top: 8px;
      display: flex;
      align-items: center;
      background: rgba(0, 82, 217, 0.06);
      // margin-right: 10px;
      > img {
        width: 48px;
        height: 48px;
        margin-right: 12px;
      }

      .patindUserInfo {
        display: flex;
        flex-direction: column;
        font-weight: normal;
        line-height: 14px;
        color: #86909c;

        .title {
          font-size: 16px;
          font-weight: bold;
          color: #000000;
          margin-bottom: 8px;
        }
      }
    }
  }
}

.iconView {
  height: 30px;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #0052d9;
  background: #f9fbff;
  padding: 0 20px;
  margin-bottom: 8px;
  margin-top: 20px;
  > img {
    width: 12px;
    height: 15px;
    margin-right: 10px;
  }
}

.reportView {
  display: flex;
  > img {
    width: 64px;
    height: 64px;
    margin-right: 16px;
  }
}
</style>
