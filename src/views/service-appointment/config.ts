import { RenderInfo } from "@/types/global";
import * as apis from '@/batchApi'
import { h } from "vue";

/* 服务信息 */
export const serviceInformation = [
  // { title: "已选渠道", attributeName: "channelName", col: { name: 4, value: 20 } },
  { title: "支付类型", attributeName: "payType", enumMap: { '1': '权益核销', '2': '患者支付' }, col: { name: 4, value: 20 } },
  { title: "服务项目", attributeName: "serviceName", col: { name: 4, value: 20 } },
  { title: "首诊机构", attributeName: "firstVisitOrgId",extend: true, col: { name: 4, value: 20 } },
];

/* 订单信息 */
export const OrderInfo = [
  { title: "操作人", attributeName: "createUser", col: { name: 4, value: 20 } },
  { title: "订单号", attributeName: "consultationId", col: { name: 4, value: 20 } },
  { title: "提交时间", attributeName: "xCreateTime", col: { name: 4, value: 20 } },
];
/* 基本健康信息 */
export const BaseHealthInfo = [
  { title: "现病史", attributeName1: "hasCurrentHistory",attributeName: 'currentHistory', col: { name: 4, value: 20 } },
  { title: "过往病史", attributeName1: "hasPastDisease",attributeName: 'pastDisease', col: { name: 4, value: 20 } },
  { title: "过敏史", attributeName1: "hasAllergyHistory",attributeName: 'allergyHistory', col: { name: 4, value: 20 } },
]

/* 腾讯会议信息 */
export const meetingInfo: RenderInfo<any>[] = [  
  { label: "会议号", attrName: "meeting_code", span: [4, 20]},
  { label: "会议密码", attrName: "meeting_password", span: [4, 20] },
  { label: "会议时间", attrName: "meeting_start_date", span: [4, 20] },
  // { label: "会议链接", attrName: "meetingPassword", span: [4, 20],render: () => {
  //   return h('a', {  href: 'https://www.baidu.com/',
  //     target: '_blank', },'点击参加会议')
  // } },
  { label: "会议主持人", attrName: "doctor_info", span: [4, 20],render: (data) => {
    return data?.doctor_info?.name
  } },
]