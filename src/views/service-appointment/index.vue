<template>
  <div class="table-wrapper">
    <table-filter @search="handleSearch" />
    <div class="table-container">
      <a-table
        :data="tableData"
        :pagination="false"
        :loading="loading"
        :scroll="scroll"
        scrollbar
      >
        <template #columns>
          <a-table-column title="序号" :width="65">
            <template #cell="{ rowIndex }">
              <span>{{ rowIndex + 1 }}</span>
            </template>
          </a-table-column>
          <a-table-column title="订单号" data-index="consultationId">
          </a-table-column>
          <a-table-column title="主权益人" data-index="patientName" />
          <!-- <a-table-column title="下单类型" data-index="sex">
            <template #cell="{ record }">
              todo 现在没有，后续在加
              <span>服务人员下单</span>
            </template>
          </a-table-column> -->
          <a-table-column title="服务项目" data-index="serviceName">
          </a-table-column>
          <a-table-column title="状态">
            <template #cell="{ record }">
              <span
                :style="{
                  color:
                    ServiceAppointmentStatusMap.get(record?.status)?.color ??
                    '#333',
                }"
                >{{ record?.statusName }}</span
              >
            </template>
          </a-table-column>
          <a-table-column
            title="下单时间"
            data-index="xCreateTime"
          ></a-table-column>
          <a-table-column title="支付方式">
            <template #cell="{ record }">
              <span v-if="record.payType === '1'">权益核销</span>
              <span v-else-if="record.payType === '2'">患者支付</span>
            </template>
          </a-table-column>
          <a-table-column title="操作" :width="250">
            <template #cell="{ record }">
              <div class="table-operate">
                <a-button
                  size="mini"
                  type="outline"
                  @click="handleView(record)"
                >
                  查看
                </a-button>
                <a-popconfirm
                  content="确认要更新此条服务预约记录的状态？"
                  type="info"
                  :on-before-ok="e => handleUpdate(e, record)"
                >
                  <a-button size="mini" type="outline"> 更新状态 </a-button>
                </a-popconfirm>
                <!-- 01:待支付，02:支付失败，03:待执行，04：执行中 -->
                <a-popconfirm
                  v-if="['01', '02', '03'].includes(record?.status)"
                  content="确认要删除此条服务预约记录？"
                  type="warning"
                  :on-before-ok="e => handleDelete(e, record)"
                >
                  <a-button size="mini" type="outline"> 取消 </a-button>
                </a-popconfirm>
              </div>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
    <div class="paginationView">
      <a-pagination
        :total="pagination.total"
        :page-size="pagination.pageSize"
        :current="pagination.current"
        size="medium"
        show-total
        show-jumper
        show-page-size
        @change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      />
    </div>
    <!-- 详情 -->
    <Details
      v-model:visible="visile"
      :details-id="detailsId"
      :consultation-id="consultationId"
    />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js";
import * as apis from "@/batchApi";
import { onMounted, reactive, ref } from "vue";
import { ServiceAppointmentStatusMap } from "@/types/enums";
import tableFilter from "./components/table-filter.vue";
import Details from "./components/details.vue";

const scroll = ref({
  y: "100%",
});

type IRecord = apis.QueryServiceReservationListUsingPOST_1ServiceReservationDto;

const loading = ref(false);
const visile = ref<boolean>(false);
const detailsId = ref<string | undefined>(undefined);
const consultationId = ref<string | undefined>(undefined);
const tableData = ref<IRecord[]>([]);
const params = reactive({
  expirationDate: undefined,
  keyWord: undefined,
  reservationType: undefined,
  serviceId: undefined,
});

// 存储表格分页信息
const pagination = reactive({
  current: 1,
  total: 0,
  pageSize: 10,
});

// 根据传入的参数 进行筛选
const handleSearch = async (_values: any) => {
  const { expirationDate, keyWord, serviceId } = _values || {};
  params.expirationDate = expirationDate;
  params.keyWord = keyWord;
  params.serviceId = serviceId;
  pagination.current = 1;
  await init();
};

const handlePageChange = (current: number) => {
  pagination.current = current;
  init();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  init();
};

const handleUpdate: (
  done: (closed: boolean) => void,
  record: IRecord,
) => void = (done, record) => {
  apis
    .postBdManageApiServiceReservationGetOrderStatus({
      orderId: record?.id,
    })
    .then(() => {
      done(true);
      init();
    })
    .catch(() => {
      done(true);
    });
};

const handleDelete: (
  done: (closed: boolean) => void,
  record: IRecord,
) => void = (done, record) => {
  apis
    .postBdManageApiServiceReservationCancelServiceReservation({
      id: record?.id as unknown as string,
    })
    .then(() => {
      done(true);
      init();
    })
    .catch(() => {
      done(true);
    });
};

const handleView = (_record: IRecord) => {
  visile.value = true;
  detailsId.value = _record?.id as unknown as string;
  consultationId.value = _record?.consultationId as unknown as string;
};

const init = async () => {
  loading.value = true;
  const [err, res] = await to(
    apis.postBdManageApiServiceReservationQueryServiceReservationList({
      ...params,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    }),
  );
  loading.value = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    tableData.value = res.data?.records as IRecord[];
    pagination.total = res.data.total as number;
  }
};

onMounted(async () => {
  await init();
});
</script>

<style lang="scss" scoped>
.table-wrapper {
  background-color: white;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  .table-operate {
    display: flex;
    gap: 8px;
  }
  .table-container {
    overflow-y: scroll;
  }

  .paginationView {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}

.name-item {
  display: flex;
  align-items: center;
  > img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: var(--spacing-4);
  }
  // 展示具体的名称
  > span {
    cursor: pointer;
    text-decoration: underline;
    color: var(--primary-color);
    display: inline-block;
    width: 80px;
    white-space: nowrap; /* 确保文本不换行 */
    overflow: hidden; /* 超出部分隐藏 */
    text-overflow: ellipsis;
  }
}
.name-item-desc {
  padding: 8px;
  border-radius: 4px;
  background-color: white;
  box-shadow: 2px 2px 5px #ddd;
}
</style>
