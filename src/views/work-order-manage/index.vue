<!--
* @description 工单记录
* @fileName index.vue
* <AUTHOR>
* @date 2025/07/10 21:33:06
!-->
<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import {
  FormInstance,
  Message,
  SelectOptionData,
  TableChangeExtra,
  TableColumnData,
  TableData,
} from "@arco-design/web-vue";
import { to } from "await-to-js";
import * as apis from "@/batchApi";
import tableFilter from "@/views/system/components/table-filter.vue";
import useQuickAccessStore from "@/store/modules/quick-access";
import { useUserStore, useWorkOrder } from "@/store";
import { storeToRefs } from "pinia";
import { Filters } from "@arco-design/web-vue/es/table/interface";
import { isEmpty } from "lodash";
import {
  workOrderMangeColumn,
  ModalType,
  IModalType,
  WorkOrderStatusEnum,
  WorkOrderStatusMap,
} from "./config";
import Details from "./components/details.vue";

type Record = apis.MyTicketsUsingPOST_1TicketQueryResVo;

interface IState {
  loading: boolean;
  visible: boolean;
  transferLoading: boolean;
  transferVisible: boolean;
  visibleLook: boolean;
  isShowPagination: boolean;
  doctorList: apis.GetTeamDoctorUsingPOSTDoctorInfoDto[];
  form: { toUserId?: string; id?: number };
  type: IModalType;
  keyWord: string | undefined;
  editID: number | undefined;
  executorList: apis.MyTicketsUsingPOST_1AssigneeVo[];
  workOrderTypeList: apis.DictTypeUsingGETSysDictData[];
}

const workOrderStore = useWorkOrder();
const userStore = useUserStore();
const { list, pagination } = storeToRefs(workOrderStore);
const quickAccessStore = useQuickAccessStore();
const modalFormRef = ref<FormInstance>();
const pageState = reactive<IState>({
  loading: false,
  visible: false,
  transferLoading: false,
  transferVisible: false,
  visibleLook: false,
  isShowPagination: false,
  doctorList: [],
  form: {},
  type: ModalType.add,
  keyWord: undefined,
  editID: undefined,
  executorList: [],
  workOrderTypeList: [],
});

const getList = (params?: apis.postBdManageApiTicketMyRequest) => {
  workOrderStore.getTicketMyList({
    ticketNumber: pageState.keyWord,
    ...params,
  });
};

const handleLookUp = (record: Record) => {
  pageState.type = ModalType.look;
  pageState.editID = record?.xid;
  pageState.visibleLook = true;
};

/* 更新状态 */
const handleUpdateStatus = async (
  record: Record,
  status: keyof typeof WorkOrderStatusEnum,
) => {
  const [err, res] = await to(
    apis.postBdManageApiTicketStatusById(
      { id: record?.xid as number },
      { status },
      {},
    ),
  );
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    Message.success("成功");
    getList();
  }
};

const generateResultObject = (
  columns: Array<TableColumnData>,
  valueObject: Filters | undefined,
) => {
  if (isEmpty(valueObject) || !columns?.length) return {};
  return columns
    .filter(column => column.filterable) // 只保留包含 filterable 属性的列
    .reduce((acc: any, column) => {
      const key = column.dataIndex;
      const isArray = !column.filterable?.renderContent;
      acc[key as string] = isArray
        ? valueObject?.[key as string]
        : valueObject?.[key as string]
          ? valueObject?.[key as string]?.[0]
          : undefined;

      return acc;
    }, {});
};

const handleTableChange = (_data: TableData, extra: TableChangeExtra) => {
  const { page, pageSize, type, filters } = extra || {};
  // 如果有filter条件隐藏分页
  if (type === "filter" && filters) {
    const params = generateResultObject(
      workOrderMangeColumn({}),
      extra?.filters,
    );
    const hasValidArray = Object.values(filters)?.some(
      v => Array.isArray(v) && v.length > 0,
    );
    getList(params);
    // pageState.isShowPagination = hasValidArray;
  }
  if (type === "pagination") {
    workOrderStore.setPagination({ current: page, pageSize });
    getList();
  }
};

const handleOk = async (): Promise<boolean> => {
  const modalError = await modalFormRef.value?.validate();
  return new Promise(resolve => {
    if (modalError) {
      resolve(false);
      return;
    }
    apis
      .postBdManageApiTicketAssigneeTransferById(
        { id: pageState.form?.id as number },
        { toUserId: pageState.form?.toUserId as unknown as number },
        {},
      )
      .then(res => {
        if (res?.code === "1") {
          Message.success("成功");
          modalFormRef.value?.resetFields();
          resolve(true);
          getList();
        }
      })
      .catch(() => {
        resolve(false);
      });
  });
};
const handleDictData = async (_dictType: string) => {
  const [err, res] = await to(
    apis.getBdManageApiSystemDictDataTypeByDictType({
      dictType: _dictType,
    }),
  );
  if (err) {
    throw err;
  }
  if (res.data) {
    return res.data;
  }
  return [];
};

// onUpdated(async () => {
//   if (pageState.visible) {
//     pageState.organLoading = true;
//     pageState.organList = await handleDictData("org_type");
//     pageState.organLoading = false;
//   }
// });

/* 转移他人 */
const handleTransfer = async (record: Record) => {
  pageState.transferVisible = true;
  pageState.executorList = record?.assignees as IState["executorList"];
  getSignDoctorList();
};

const getSignDoctorList = async () => {
  pageState.transferLoading = true;
  const [err, res] = await to(
    apis.postBdManageApiServiceReservationGetTeamDoctor({}),
  );
  pageState.transferLoading = false;
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    const result = res?.data?.filter(
      item => item?.doctorUserId !== userStore?.accountInfo?.userId,
    ); // 筛出自己
    pageState.doctorList = result as IState["doctorList"];
  }
};

const cancel = () => {
  pageState.transferVisible = false;
  pageState.executorList = [];
  modalFormRef.value?.resetFields();
};

const handleSearch = ({ keyWord }: { keyWord: string }) => {
  pageState.keyWord = keyWord;
  getList();
};

onMounted(async () => {
  pageState.workOrderTypeList = await handleDictData("ticket_type");
});

/* 删除 */
const handleDel = async (done: (closed: boolean) => void, id: number) => {
  apis
    .deleteBdManageApiTicketById({ id }, {})
    .then(res => {
      if (res?.code === "1") {
        Message.success("成功");
        done(true);
        getList();
      }
    })
    .catch(_err => {
      done(true);
    });
};

const formatLabel = (data: SelectOptionData | any) => {
  const record = pageState.executorList?.find(
    item => item?.xid === data?.value,
  );
  return record?.execUserName;
};

getList();
</script>

<template>
  <div class="table-wrapper">
    <tableFilter
      placeholder="请输入工单ID"
      @search="handleSearch"
      @handle-reset="handleSearch"
    >
      <template #operate>
        <a-button
          type="primary"
          @click="
            pageState.visible = true;
            pageState.type = ModalType.add;
            quickAccessStore.toggleWorkOrderVisibleStatus(true);
          "
        >
          <template #icon> <icon-plus /> </template>新增</a-button
        >
      </template>
    </tableFilter>
    <div class="table-container">
      <a-table
        :loading="pageState.loading"
        row-key="xid"
        :columns="
          workOrderMangeColumn({
            workOrderTypeList: pageState.workOrderTypeList,
          })
        "
        :data="list"
        :scroll="{ x: 1500, y: '100%' }"
        :pagination="pageState.isShowPagination || pagination"
        @page-change="(current: number) => (pagination.current = current)"
        @page-size-change="
          (pageSize: number) => (pagination.pageSize = pageSize)
        "
        @change="handleTableChange"
      >
        <template #operate="{ record }">
          <a-space>
            <a-button size="mini" type="outline" @click="handleLookUp(record)"
              >查看</a-button
            >
            <a-button
              v-if="record?.status === WorkOrderStatusEnum.DRAFT"
              size="mini"
              type="outline"
              @click="
                quickAccessStore.toggleWorkOrderVisibleStatus(true, record?.xid)
              "
            >
              编辑
            </a-button>
            <a-button
              v-if="
                [
                  WorkOrderStatusEnum.PENDING,
                  WorkOrderStatusEnum.SUSPENDED,
                ].includes(record?.status)
              "
              size="mini"
              type="outline"
              @click="handleTransfer(record)"
            >
              转移他人
            </a-button>
            <a-button
              v-if="[WorkOrderStatusEnum.PENDING].includes(record?.status)"
              size="mini"
              type="outline"
              @click="handleUpdateStatus(record, WorkOrderStatusEnum.SUSPENDED)"
            >
              挂起
            </a-button>
            <a-button
              v-if="
                [
                  WorkOrderStatusEnum.PENDING,
                  WorkOrderStatusEnum.SUSPENDED,
                ].includes(record?.status)
              "
              size="mini"
              type="outline"
              @click="
                handleUpdateStatus(record, WorkOrderStatusEnum.TERMINATED)
              "
            >
              终止
            </a-button>
            <a-button
              v-if="
                [
                  WorkOrderStatusEnum.PENDING,
                  WorkOrderStatusEnum.SUSPENDED,
                ].includes(record?.status)
              "
              size="mini"
              type="outline"
              @click="handleUpdateStatus(record, WorkOrderStatusEnum.COMPLETED)"
            >
              完成
            </a-button>
            <a-popconfirm
              v-if="record?.status === WorkOrderStatusEnum.DRAFT"
              content="确认要删除选中的记录?"
              type="warning"
              :on-before-ok="done => handleDel(done, record?.xid)"
            >
              <a-button type="outline" size="mini"> 删除 </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </div>
    <!-- 转移他人 -->
    <a-modal
      v-model:visible="pageState.transferVisible"
      title="工单变更"
      @before-ok="handleOk"
      @cancel="cancel"
    >
      <a-form ref="modalFormRef" :model="pageState.form" layout="vertical">
        <a-form-item
          field="id"
          label="当前执行人"
          :rules="[
            {
              required: true,
              message: '请选择当前执行人',
            },
          ]"
        >
          <a-select
            v-model="pageState.form.id"
            placeholder="请选择"
            :fallback-option="false"
            :format-label="formatLabel"
          >
            <a-option
              v-for="item in pageState.executorList"
              :key="item.xid"
              :value="item?.xid"
              :disabled="
                [
                  WorkOrderStatusEnum.COMPLETED,
                  WorkOrderStatusEnum.TERMINATED,
                ].includes(item?.status as WorkOrderStatusEnum)
              "
            >
              <span class="optionStyle">
                <label>{{ item?.execUserName }}</label>
                <label
                  :style="{
                    color: WorkOrderStatusMap.get(
                      item?.status as WorkOrderStatusEnum,
                    )?.color,
                  }"
                  >{{
                    WorkOrderStatusMap.get(item?.status as WorkOrderStatusEnum)
                      ?.title
                  }}</label
                >
              </span>
            </a-option>
          </a-select>
        </a-form-item>
        <a-form-item
          field="toUserId"
          label="转移给他"
          :rules="[
            {
              required: true,
              message: '请选择转移给他',
            },
          ]"
        >
          <a-select
            v-model="pageState.form.toUserId"
            placeholder="请选择"
            :loading="pageState.transferLoading"
            :fallback-option="false"
            allow-search
          >
            <a-option
              v-for="item in pageState.doctorList"
              :key="item.doctorUserId"
              :value="item?.doctorUserId"
              >{{ item?.doctorName }}</a-option
            >
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
    <!-- Details -->
    <Details
      v-model:visible-look="pageState.visibleLook"
      :ticket-id="pageState.editID"
    />
  </div>
</template>

<style lang="scss" scoped>
.table-wrapper {
  background-color: white;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  .table-operate {
    display: flex;
    gap: 8px;
  }
  .table-container {
    overflow-y: scroll;
  }
}

:deep(.arco-select-option-content) {
  width: 100%;
}
.optionStyle {
  display: flex;
  justify-content: space-between;

  > label {
    display: inline-block;
  }
}
</style>
