<!--
* @description 工单详情
* @fileName details.vue
* <AUTHOR>
* @date 2025/07/15 16:39:39
!-->
<script setup lang="ts">
import { ref, onUpdated, computed } from "vue";
import to from "await-to-js";
import * as apis from "@/batchApi";
import { renderCell } from "@/utils";
import {
  renderBaseInfo,
  renderWorkOrderDetails,
  WorkOrderStatusMap,
  WorkOrderStatusEnum,
} from "../config";

type IData = apis.GetTicketUsingGETTicketDetailResVo;
const visibleLook = defineModel<boolean>("visibleLook", { required: true });

const props = defineProps<{ ticketId: number | undefined }>();
const data = ref<IData>({});
const loading = ref<boolean>(false);

const getManagementUserAdminDetail = async () => {
  loading.value = true;
  const [err, res] = await to(
    apis.getBdManageApiTicketById({ id: props?.ticketId as unknown as number }),
  );
  loading.value = false;
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    data.value = res?.data as IData;
  }
};

const imgUrl = computed(() => {
  if (data?.value?.status) {
    return (
      WorkOrderStatusMap.get(
        data?.value?.status as keyof typeof WorkOrderStatusEnum,
      )?.icon ?? undefined
    );
  }

  return undefined;
});

onUpdated(() => {
  if (visibleLook.value) {
    getManagementUserAdminDetail();
  }
});
</script>

<template>
  <a-drawer
    unmount-on-close
    :width="680"
    :visible="visibleLook"
    :mask-closable="true"
    :footer="false"
    title="工单详情"
    @cancel="visibleLook = false"
  >
    <a-spin
      :loading="loading"
      :style="{
        width: '100%',
      }"
    >
      <div class="contentView">
        <img v-if="imgUrl" class="tagImg" :src="imgUrl" />
        <div class="cardView">
          <div class="title">基础信息</div>
          <a-row :gutter="[0, 10]">
            <template v-for="item in renderBaseInfo" :key="item.attrName">
              <a-col :span="item.span?.[0]" style="color: #86909c">
                {{ item?.label }}
              </a-col>
              <a-col :span="item.span?.[1]">
                <template v-if="item?.render">
                  <component :is="renderCell(item.render(data))" />
                </template>
                <template v-else>
                  {{ data?.[item.attrName as keyof IData] ?? "-" }}
                </template>
              </a-col>
            </template>
          </a-row>
        </div>
        <div class="cardView">
          <div class="title">工单详情</div>
          <a-row :gutter="[0, 10]">
            <template
              v-for="item in renderWorkOrderDetails"
              :key="item.attrName"
            >
              <a-col :span="item.span?.[0]" style="color: #86909c">
                {{ item?.label }}
              </a-col>
              <a-col :span="item.span?.[1]">
                <template v-if="item?.render">
                  <component :is="renderCell(item.render(data))" />
                </template>
                <template v-else>
                  {{ data?.[item.attrName as keyof IData] ?? "-" }}
                </template>
              </a-col>
            </template>
            <template v-if="data?.assignees?.length">
              <a-col :span="4" style="color: #86909c"> 处理人 </a-col>
              <a-col :span="20" style="color: #86909c">
                <a-timeline>
                  <a-timeline-item
                    v-for="item in data?.assignees"
                    :key="item?.xid"
                  >
                    <template #label>
                      <div class="tipText">
                        <span class="text">{{
                          item?.execUserName ?? "-"
                        }}</span>
                        <span>执行状态:</span>
                        <span
                          :style="{
                            color: WorkOrderStatusMap.get(
                              item?.status as WorkOrderStatusEnum,
                            )?.color,
                            marginLeft: '6px',
                          }"
                          >{{
                            item?.status
                              ? WorkOrderStatusMap.get(
                                  item?.status as WorkOrderStatusEnum,
                                )?.title
                              : "-"
                          }}</span
                        >
                        &emsp;{{ item?.xupdateTime }}
                      </div>
                    </template>
                  </a-timeline-item>
                </a-timeline>
              </a-col>
            </template>
          </a-row>
        </div>
        <div v-if="data?.logs?.length" class="cardView">
          <div class="title">处理记录</div>
          <a-timeline :style="{ marginRight: '40px', marginLeft: '8px' }">
            <a-timeline-item
              v-for="item in data?.logs"
              :key="item?.xid"
              :label="item?.action"
            >
              <template #dot>
                <icon-check-circle-fill />
              </template>
              {{ item?.comment ?? "-" }}
              <template #label>
                <div class="tipText">
                  <span class="text">{{ item?.userName ?? "-" }}</span>
                  <span class="time">{{ item?.xcreateTime }}</span>
                </div>
              </template>
            </a-timeline-item>
          </a-timeline>
        </div>
      </div>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>
.contentView {
  position: relative;
  font-size: 14px;
  font-weight: normal;

  .tagImg {
    width: 112px;
    height: 97px;
    position: absolute;
    right: 0;
  }

  .cardView {
    background: white;
    margin-bottom: 12px;
    .title {
      color: #000;
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 8px;
    }

    .tipText {
      padding: 12px;
      border-radius: 4px;
      background: #f9fbff;
      > span:first-child {
        color: #1d2129;
        font-size: 12px;
        margin-right: 12px;
      }
    }
  }

  .baseInfo {
    display: flex;
    gap: 16px;
    color: rgba(0, 0, 0, 0.7);
    > div {
      display: flex;
      flex-direction: column;
      gap: 2px;
      > span:first-child {
        color: #000000;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
}
</style>
