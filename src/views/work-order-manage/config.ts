import {
  TableColumnData,
  TableData,
  TableFilterData,
} from "@arco-design/web-vue";
import { IconSearch } from "@arco-design/web-vue/es/icon";
import Filterable from "@/components/filterable/index.vue";
import { h } from "vue";
import dayjs from "dayjs";
import * as apis from "@/batchApi";
import { getImageSrc } from "@/utils";
import { RenderInfo } from "@/types/global";

/* 页面弹窗状态 */
export enum ModalType {
  add = "add",
  look = "look",
  edit = "edit",
}

export type IModalType = keyof typeof ModalType;

type IMapValue = {
  title: string;
  color?: string;
  icon?: string;
  bg?: string;
};

/* 优先级 */
export enum PriorityEnum {
  LOW = "LOW",
  NORMAL = "NORMAL",
  HIGH = "HIGH",
}

export const PriorityMap = new Map<keyof typeof PriorityEnum, IMapValue>([
  [PriorityEnum.LOW, { title: "低", color: "#00B42A", bg: "#E8FFEA" }],
  [PriorityEnum.NORMAL, { title: "中", color: "#0052D9", bg: "#E8F5FF" }],
  [PriorityEnum.HIGH, { title: "高", color: "#F53F3F", bg: "#FFECE8" }],
]);

/* 工单状态 */
export enum WorkOrderStatusEnum {
  COMPLETED = "COMPLETED",
  DRAFT = "DRAFT",
  PENDING = "PENDING",
  SUSPENDED = "SUSPENDED",
  TERMINATED = "TERMINATED",
}

export const WorkOrderStatusMap = new Map<
  keyof typeof WorkOrderStatusEnum,
  IMapValue
>([
  [
    WorkOrderStatusEnum.COMPLETED,
    { title: "完成", color: "#00B42A", icon: getImageSrc("wc.png") },
  ],
  [WorkOrderStatusEnum.DRAFT, { title: "草稿", color: "#FF7F0B",icon: getImageSrc('cg.png') }],
  [
    WorkOrderStatusEnum.PENDING,
    { title: "待执行", color: "#F53F3F", icon: getImageSrc("dzx.png") },
  ],
  [
    WorkOrderStatusEnum.SUSPENDED,
    { title: "挂起", color: "#86909C", icon: getImageSrc("gq.png") },
  ],
  [
    WorkOrderStatusEnum.TERMINATED,
    { title: "终止", color: "#86909C", icon: getImageSrc("zc.png") },
  ],
]);

/* 履约方式 */
export enum UlfillmentModeEnum {
  JOINT = "JOINT",
  SEPARATE = "SEPARATE",
}

export const UlfillmentModeMap = new Map<
  keyof typeof UlfillmentModeEnum,
  IMapValue
>([
  [UlfillmentModeEnum.JOINT, { title: "共同履约" }],
  [UlfillmentModeEnum.SEPARATE, { title: "分别履约" }],
]);

export const renderBaseInfo: RenderInfo<apis.GetTicketUsingGETTicketDetailResVo>[] =
  [
    { label: "客户信息", attrName: "customerName", span: [4, 20] },
    { label: "联系方式", attrName: "contactMobile", span: [4, 20] },
    { label: "工单标题", attrName: "title", span: [4, 20] },
    { label: "工单类型", attrName: "typeName", span: [4, 20] },
    {
      label: "权益包",
      attrName: "servicePackageName",
      span: [4, 20],
    },
    {
      label: "服务内容",
      attrName: "rightsPackageName",
      span: [4, 20],
    },
    {
      label: "优先级",
      attrName: "priority",
      span: [4, 20],
      render: data => {
        const result: any = data?.priority
          ? PriorityMap.get(data?.priority)
          : {};
        return h(
          "span",
          {
            style: {
              color: result?.color,
              background: result?.bg,
              padding: "4px 16px",
              borderRadius: "4px",
            },
          },
          result?.title,
        );
      },
    },
    {
      label: "状态",
      attrName: "status",
      span: [4, 20],
      render: data => {
        const result: any = data?.status
          ? WorkOrderStatusMap.get(data?.status)
          : {};
        return h(
          "span",
          {
            style: {
              color: result?.color,
            },
          },
          result?.title,
        );
      },
    },
    { label: "执行时间", attrName: "plannedExecuteTime", span: [4, 20] },
  ];

export const renderWorkOrderDetails: RenderInfo<apis.GetTicketUsingGETTicketDetailResVo>[] =
  [
    { label: "问题描述", attrName: "description", span: [4, 20] },
    // {
    //   label: "处理人",
    //   attrName: "assignees",
    //   span: [4, 18],
    //   render: data => {
    //     const users = data?.assignees
    //       ?.map(item => item?.execUserName)
    //       ?.filter(Boolean);
    //     return users?.length ? users?.join("、") : "-";
    //   },
    // },
    {
      label: "处理方式",
      attrName: "fulfillmentMode",
      span: [4, 20],
      render: data => {
        return data?.fulfillmentMode
          ? UlfillmentModeMap?.get(data?.fulfillmentMode)?.title
          : "-";
      },
    },
  ];
/* 工单管理column */
type OrganMangeColumn = {
  workOrderTypeList?: apis.DictTypeUsingGETSysDictData[];
};
export const workOrderMangeColumn = ({
  workOrderTypeList,
}: OrganMangeColumn): Array<TableColumnData> => [
  {
    title: "序号",
    dataIndex: "xid",
    align: "center",
    width: 60,
    render: ({ rowIndex }) => {
      return rowIndex + 1;
    },
  },
  {
    title: "工单ID",
    dataIndex: "ticketNumber",
    width: 180,
    filterable: {
      alignLeft: true,
      // filter: (value, record) => record?.ticketId?.includes(value),
      filter: () => true,
      icon: () => h(IconSearch),
      renderContent: ({
        filterValue,
        setFilterValue,
        handleFilterConfirm,
        handleFilterReset,
      }) =>
        h(Filterable, {
          value: filterValue[0],
          onSetFilterValue: setFilterValue,
          onHandleFilterConfirm: handleFilterConfirm as unknown as any,
          onHandleFilterReset: handleFilterReset as unknown as any,
        }),
    },
  },
  {
    title: "工单标题",
    dataIndex: "title",
    ellipsis: true,
    tooltip: {position: 'left'},
    width: 120,
    filterable: {
      alignLeft: true,
      // filter: (value, record) => record?.title?.includes(value),
      filter: () => true,
      icon: () => h(IconSearch),
      triggerProps: {
        showArrow: true,
        defaultPopupVisible: true
      },
      renderContent: ({
        filterValue,
        setFilterValue,
        handleFilterConfirm,
        handleFilterReset,
      }) =>
        h(Filterable, {
          value: filterValue[0],
          onSetFilterValue: setFilterValue,
          onHandleFilterConfirm: handleFilterConfirm as unknown as any,
          onHandleFilterReset: handleFilterReset as unknown as any,
        }),
    },
  },
  {
    title: "工单类型",
    dataIndex: "type",
    width: 120,
    filterable: {
      filters: workOrderTypeList?.map(item => ({
         value: item?.dictValue,
        text: item?.dictLabel,
      }))  as Array<TableFilterData>,
      alignLeft: true,
      filter: () => true,
      // filter: (value, record) => value?.includes(`${record?.type}`),
      multiple: true,
    },
    render: ({ record }: { record: TableData }) => record?.typeName
  },
  {
    title: "优先级",
    dataIndex: "priority",
    width: 100,
    filterable: {
      filters: Array.from(PriorityMap, ([key, { title }]) => ({
        value: key,
        text: title,
      })) as Array<TableFilterData>,
      alignLeft: true,
      filter: () => true,
      // filter: (value, record) => value?.includes(`${record?.priority}`),
      multiple: true,
    },
    render: ({ record }: { record: TableData }) => {
      const result: any = record?.priority
        ? PriorityMap.get(record?.priority)
        : {};
      return h(
        "span",
        {
          style: {
            color: result?.color,
            background: result?.bg,
            padding: "4px 16px",
            borderRadius: "4px",
          },
        },
        result?.title,
      );
    },
  },
  {
    title: "执行方式",
    dataIndex: "fulfillmentMode",
    width: 120,
    filterable: {
      filters: Array.from(UlfillmentModeMap, ([key, { title }]) => ({
        value: key,
        text: title,
      })) as Array<TableFilterData>,
      alignLeft: true,
      filter: () => true,
      // filter: (value, record) => value?.includes(`${record?.fulfillmentMode}`),
      multiple: true,
    },
    render: ({ record }: { record: TableData }) => {
      const result = record?.fulfillmentMode
        ? UlfillmentModeMap.get(record?.fulfillmentMode)?.title
        : "-";
      return result;
    },
  },
  {
    title: "执行人",
    dataIndex: "assignees",
    ellipsis: true,
    tooltip: {position: 'left'},
    width: 150,
    render: ({ record }: { record: TableData }) => {
      const result = record?.assignees
        ?.map((item: any) => item?.execUserName)
        .filter(Boolean);
      return result?.length ? result?.join("、") : "-";
    },
  },
  {
    title: "状态",
    dataIndex: "status",
    width: 80,
    filterable: {
      filters: Array.from(WorkOrderStatusMap, ([key, { title }]) => ({
        value: key,
        text: title,
      })) as Array<TableFilterData>,
      alignLeft: true,
      filter: () => true,
      // filter: (value, record) => value?.includes(`${record?.status}`),
      multiple: true,
    },
    render: ({ record }: { record: TableData }) => {
      const result: any = record?.status
        ? WorkOrderStatusMap.get(record?.status)
        : {};
      return h(
        "span",
        {
          style: {
            color: result?.color,
          },
        },
        result?.title,
      );
    },
  },
  {
    title: "要求执行时间",
    dataIndex: "plannedExecuteTime",
    width: 180,
    render: ({ record }: { record: TableData }) => {
      return record?.plannedExecuteTime
        ? dayjs(record?.plannedExecuteTime).format("YYYY-MM-DD HH:mm:ss")
        : "-";
    },
    sortable: {
      sortDirections: ["ascend", "descend"],
    },
  },
  {
    title: "实际执行时间",
    width: 180,
    dataIndex: "completedTime",
    render: ({ record }: { record: TableData }) => {
      return record?.completedTime
        ? dayjs(record?.completedTime).format("YYYY-MM-DD HH:mm:ss")
        : "-";
    },
    sortable: {
      sortDirections: ["ascend", "descend"],
    },
  },
  {
    title: "操作",
    dataIndex: "xid",
    width: 360,
    slotName: "operate",
    fixed: "right",
    align: "left",
  },
];
