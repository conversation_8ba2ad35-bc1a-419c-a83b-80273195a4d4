<!--
* @description 保险服务购买记录
* @fileName details.vue
* <AUTHOR>
* @date 2025/06/06 09:05:50
!-->
<script setup lang="ts">
import { ref, watch, onBeforeUnmount } from "vue";
import { getImageSrc } from "@/utils";
import * as apis from "@/bdclientApi";
import to from "await-to-js";
import { isEmpty } from "lodash";
import { Message } from "@arco-design/web-vue";
import { OrderStatus } from "@/types/enums";
import { UserInfo, OrderInfo } from "../config";

type Data = apis.GetServicePackageByIdUsingPOSTPatientServicePackageDto;

const visible = defineModel<boolean>("visible", { required: true });
const props = defineProps<{ detailsId: string | number | undefined }>();

const loading = ref<boolean>(false);
const okLoading = ref<boolean>(false);
const data = ref<Data>();
const emit = defineEmits(["getList"]);

const getDetails = async () => {
  loading.value = true;
  const [err, res] = await to(
    apis.postBdClientServiceOperationGetServicePackageById({
      id: props?.detailsId as string,
    }),
  );
  loading.value = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    data.value = res.data;
  }
};

watch(visible, newVal => {
  if (newVal) {
    getDetails();
  }
});
const handleOk = async () => {
  okLoading.value = true;
  const [err, res] = await to(
    apis.postBdClientServiceOperationUpdateServicePackageStatus({
      id: props?.detailsId as string,
    }),
  );
  okLoading.value = false;
  if (err) {
    Message.error("更新失败");
    throw err;
  }
  if (res.code === "1") {
    emit("getList");
    visible.value = false;
    Message.success("更新成功");
  }
};

const formatAmount = (amount: string) => {
  if (!amount) return "0.00";
  const number = parseFloat(amount);
  const result = number / 100;
  return result.toFixed(2);
};

onBeforeUnmount(() => {
  data.value = {} as any;
});
</script>

<template>
  <a-drawer
    :width="750"
    :visible="visible"
    :footer="!isEmpty(data)"
    unmount-on-close
    title="订单详情"
    body-class="ssf"
    ok-text="更新状态"
    :ok-loading="okLoading"
    @ok="handleOk"
    @cancel="visible = false"
  >
    <a-spin class="conainer" tip="加载中..." :loading="loading">
      <div v-if="isEmpty(data)" class="emptyView">
        <a-empty />
      </div>
      <div v-else class="details-view">
        <!-- header -->
        <div class="header">
          <div class="title">用户信息</div>
          <div class="infoview">
            <div class="user">
              <img :src="getImageSrc('woman.png')" />
              <div>
                <span class="name">{{ data?.name }}</span>
                <span>{{ data?.age ?? "-" }}岁 {{ data?.sex }}</span>
              </div>
            </div>
            <div class="baseInfo">
              <a-row :gutter="[0, 10]">
                <a-col
                  v-for="item in UserInfo"
                  :key="item.attributeName"
                  :span="item.span"
                >
                  <a-row>
                    <a-col :span="item.col.name">{{ item?.title }}</a-col>
                    <a-col :span="item.col.value" class="value">{{
                      data?.[item?.attributeName as keyof Data] ?? "-"
                    }}</a-col>
                  </a-row>
                </a-col>
              </a-row>
            </div>
          </div>
        </div>
        <!-- 订单 -->
        <div class="orderView">
          <div class="title">订单信息</div>
          <a-row
            v-for="item in OrderInfo"
            :key="item.attributeName"
            :gutter="[0, 14]"
          >
            <a-col :span="5" class="name">{{ item.title }}</a-col>
            <a-col :span="19" class="value">
              <div v-if="item.isEnum">
                <span
                  v-if="data?.[item?.attributeName] === OrderStatus.Ordered"
                  :style="{ color: '#333333' }"
                  >已下单</span
                >
                <span
                  v-else-if="
                    data?.[item?.attributeName as keyof Data] ===
                    OrderStatus.Confirmation
                  "
                  :style="{ color: '#77DC89' }"
                  >下单成功</span
                >
                <span
                  v-else-if="
                    data?.[item?.attributeName as keyof Data] ===
                    OrderStatus.Cancelled
                  "
                  :style="{ color: '#0052D9' }"
                  >已取消</span
                >
              </div>
              <span v-else-if="item.mark"
                >{{ item.mark }}
                {{ data?.[item?.attributeName as keyof Data] ?? "-" }}
              </span>
              <span v-else>{{
                data?.[item?.attributeName as keyof Data] ?? "-"
              }}</span>
            </a-col>
          </a-row>
        </div>
        <!-- 内容 -->
        <div class="contentView">
          <div class="title">保险服务项目</div>
          <div
            v-for="item in data?.contract?.infos"
            :key="item?.id"
            class="cardItemView"
          >
            <div class="nameInfo">
              <span>{{ item?.serviceName }}</span>
              <span class="money">¥{{ item?.serviceAmount ?? 0 }}</span>
            </div>
            <div class="otherInfo">
              <span>产品编码：{{ item?.serviceCode ?? "-" }}</span>
              <span>数量：x{{ item?.quantity ?? 0 }}</span>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </a-drawer>
</template>

<style lang="scss" scoped>
.conainer {
  height: 100%;
  width: 100%;
}
.emptyView {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.details-view {
  overflow-y: auto;
  .header {
    margin-bottom: 20px;

    .infoview {
      display: grid;
      grid-template-columns: 2fr 5fr;
      .user {
        display: flex;
        align-items: center;
        > img {
          width: 48px;
          height: 48px;
        }
        > div {
          flex: 1;
          display: flex;
          flex-direction: column;
          color: #86909c;
          font-size: 14px;
          margin-left: 16px;
          border-right: 1px solid #eeeeee;
          margin-right: 20px;

          > .name {
            color: #000000;
            font-size: 16px;
            margin-bottom: 8px;
          }
        }
      }

      .baseInfo {
        color: #86909c;
        font-size: 14px;
        .value {
          color: #000000;
        }
      }
    }
  }

  .orderView {
    padding: 20px 4px;
    margin-bottom: 20px;
    font-size: 14px;
    .name {
      color: #86909c;
    }
    .value {
      color: #000000;
    }
  }

  .contentView {
    padding: 20px 4px;
    .cardItemView {
      border-radius: 8px;
      padding: 16px;
      background: rgba(0, 82, 217, 0.06);
      margin-bottom: 12px;

      .nameInfo {
        display: flex;
        justify-content: space-between;
        color: #000000;
        font-weight: bold;
        font-size: 16px;

        .money {
          font-size: 16px;
          font-weight: 400;
          margin-bottom: 8px;
        }
      }

      .otherInfo {
        display: flex;
        justify-content: space-between;
        color: #86909c;
        font-size: 14px;
      }
    }
  }
}
.title {
  font-size: 16px;
  font-weight: bold;
  line-height: 16px;
  color: #000000;
  margin-bottom: 16px;
}
</style>
