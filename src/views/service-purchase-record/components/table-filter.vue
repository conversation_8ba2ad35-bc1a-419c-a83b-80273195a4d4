<!--
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2024-12-12 18:11:20
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-04-24 10:09:12
-->
<template>
  <div class="table-filter">
    <a-form :model="form" layout="inline">
      <div class="form-view">
        <!-- <a-form-item field="serviceParentItem" label="服务项目">
          <a-select 
            v-model="form.serviceParentItem" :style="{width:'180px'}" placeholder="请选择"
            allow-clear
          >
            <a-option
              v-for="item, index in projectList"
              :key="index"
              :value="item.dictValue"
            >
              {{ item.dictLabel }}
            </a-option>
          </a-select>
        </a-form-item> -->
        <a-form-item field="status" label="状态筛选">
          <a-select
            v-model="form.status"
            :style="{ width: '180px' }"
            placeholder="请选择"
            allow-clear
          >
            <a-option :value="1">已下单</a-option>
            <a-option :value="2">下单成功</a-option>
            <a-option :value="3">已取消</a-option>
          </a-select>
        </a-form-item>
        <!-- <a-form-item field="hospital" label="下单时间">
          <a-select v-model="form.hospital" :style="{width:'180px'}" placeholder="请选择">
            <a-option :value="1">启用</a-option>
            <a-option :value="0">停用</a-option>
            <a-option :value="3">暂存</a-option>
          </a-select>
        </a-form-item> -->
        <a-form-item field="createTime" label="下单时间">
          <a-date-picker
            v-model="form.startTime"
            style="width: 180px"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </a-form-item>
      </div>
      <a-form-item class="keyword-search" field="nameOrPhone">
        <bd-input
          width="253px"
          field="keyword"
          placeholder="搜索患者姓名/电话"
          @search="handleSearch"
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from "vue";
import bdInput from "@/components/bd-input/index.vue";
// import to from "await-to-js";

// const projectList = ref<
//   getBdManageApiSystemDictDataTypeByDictTypeResponse["data"]
// >([]);

const form = reactive({
  // 预约时间
  startTime: undefined,
  // 服务分类
  // serviceParentItem: undefined,
  // status
  status: undefined,
});

const emits = defineEmits(["search"]);

watch(
  () => form,
  () => {
    console.log("new_vale", form);
    emits("search", form);
  },
  {
    deep: true,
  },
);

// 点击确定的时候 调用对应的方法
const handleSearch = (_searchValue: any) => {
  emits("search", { ...form, ..._searchValue });
};

// const handleDictData = async (_dictType: string) => {
//   const [err, res] = await to(
//     getBdManageApiSystemDictDataTypeByDictType({
//       dictType: _dictType,
//     }),
//   );
//   if (err) {
//     throw err;
//   }
//   if (res.data) {
//     return res.data;
//   }
//   return [];
// };

// onMounted(async () => {
//   projectList.value = await handleDictData("service_parent_item");
// });
</script>

<style lang="scss" scoped>
.table-filter {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  :deep(.arco-form-layout-inline .arco-form-item) {
    margin-bottom: 0px !important;
  }
  > .arco-form {
    .form-view {
      display: flex;
    }
    justify-content: space-between;
    div.arco-form-item {
      margin-bottom: 0px;
      margin-right: var(--spacing-7);
      &.keyword-search {
        margin-right: 0px;
        :deep(.arco-form-item-label-col) {
          padding-right: 0px;
        }
      }
    }
  }
}
</style>
