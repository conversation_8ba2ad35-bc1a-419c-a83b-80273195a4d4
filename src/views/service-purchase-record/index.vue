<template>
  <div class="table-wrapper">
    <table-filter @search="handleSearch" />
    <div class="table-container">
      <a-table
        :pagination="false"
        :data="tableData"
        :loading="loading"
        :scroll="scroll"
        scrollbar
      >
        <template #columns>
          <a-table-column title="序号" :width="65">
            <template #cell="{ rowIndex }">
              <span>{{ rowIndex + 1 }}</span>
            </template>
          </a-table-column>
          <!-- <a-table-column title="下单渠道" data-index="sex">
            <template #cell="{ record }">
              todo 现在没有，后续在加
              <span>健康险</span>
            </template>
          </a-table-column> -->
          <a-table-column
            title="家医订单号"
            data-index="outChannelOrderId"
            :width="200"
          ></a-table-column>
          <!-- <a-table-column title="下单类型" data-index="sex">
            <template #cell="{ record }">
              todo 现在没有，后续在加
              <span>服务人员下单</span>
            </template>
          </a-table-column> -->
          <a-table-column title="患者姓名" data-index="name"></a-table-column>
          <a-table-column title="服务编码" data-index="age">
            <template #cell="{ record }">
              <span>{{ format(record?.contract?.infos)?.serviceCode }}</span>
            </template>
          </a-table-column>
          <a-table-column title="服务项目" data-index="reservationChannelName">
            <template #cell="{ record }">
              <span>{{ format(record?.contract?.infos)?.serviceName }}</span>
            </template>
          </a-table-column>
          <a-table-column title="状态">
            <template #cell="{ record }">
              <span
                v-if="record.status === OrderStatus.Ordered"
                :style="{ color: '#333333' }"
                >已下单</span
              >
              <span
                v-else-if="record.status === OrderStatus.Confirmation"
                :style="{ color: '#77DC89' }"
                >下单成功</span
              >
              <span
                v-else-if="record.status === OrderStatus.Cancelled"
                :style="{ color: '#0052D9' }"
                >已取消</span
              >
            </template>
          </a-table-column>
          <a-table-column
            title="下单时间"
            :width="160"
            data-index="outChannelCommitTime"
          ></a-table-column>
          <a-table-column title="操作" :width="250">
            <template #cell="{ record }">
              <div class="table-operate">
                <a-button
                  size="mini"
                  type="outline"
                  @click="handleView(record)"
                >
                  查看
                </a-button>
                <a-popconfirm
                  content="确认要更新此条服务预约记录的状态？"
                  type="info"
                  :on-before-ok="e => handleUpdate(e, record)"
                >
                  <a-button size="mini" type="outline"> 更新状态 </a-button>
                </a-popconfirm>
                <a-popconfirm
                  content="确认要删除此条服务预约记录？"
                  type="warning"
                  :on-before-ok="e => handleDelete(e, record)"
                >
                  <a-button size="mini" type="outline"> 取消 </a-button>
                </a-popconfirm>
              </div>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
    <div class="paginationView">
      <a-pagination
        :total="pagination.total"
        :page-size="pagination.pageSize"
        :current="pagination.current"
        size="medium"
        show-total
        show-jumper
        show-page-size
        @change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      />
    </div>
    <!-- 详情 -->
    <Details
      v-model:visible="visile"
      :details-id="detailsId"
      @get-list="init"
    />
  </div>
</template>

<script setup lang="ts">
import to from "await-to-js";
import * as apis from "@/bdclientApi";
import { onMounted, reactive, ref } from "vue";
import { OrderStatus } from "@/types/enums";
import tableFilter from "./components/table-filter.vue";
import Details from "./components/details.vue";

const scroll = ref({
  y: "100%",
});

type IRecord = apis.QueryServicePackageUsingPOST_1PatientServicePackageDto;

const loading = ref(false);
const visile = ref<boolean>(false);
const detailsId = ref<string | number | undefined>(undefined);
const tableData = ref<Array<IRecord>>([]);

// 存储表格分页信息
const pagination = reactive({
  current: 1,
  total: 0,
  pageSize: 10,
});

const params = reactive({
  startTime: undefined,
  keyword: undefined,
  status: undefined,
});

const format = (infoList: any[]) => {
  const Obj = {
    serviceCode: "未知",
    serviceName: "未知",
  };
  if (!infoList?.length) return Obj;
  const serviceCodeList = infoList?.map(item => item?.serviceCode);
  const serviceNameList = infoList?.map(item => item?.serviceName);
  return {
    serviceCode: serviceCodeList?.join(","),
    serviceName: serviceNameList?.join(","),
  };
};

// 根据传入的参数 进行筛选
const handleSearch = async (_values: any) => {
  const { startTime, status, keyword } = _values || {};
  params.startTime = startTime;
  params.keyword = keyword;
  params.status = status;
  pagination.current = 1;
  await init();
};

const handlePageChange = (current: number) => {
  pagination.current = current;
  init();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  init();
};

const handleUpdate: (done: (closed: boolean) => void, record: any) => void = (
  done,
  record,
) => {
  apis
    .postBdClientServiceOperationUpdateServicePackageStatus({ id: record?.id })
    .then(() => {
      done(true);
      init();
    });
};

const handleDelete: (done: (closed: boolean) => void, record: any) => void = (
  done,
  record,
) => {
  apis
    .postBdClientServiceOperationDeleteServicePackage({
      id: record?.id,
    })
    .then(() => {
      done(true);
      init();
    });
};

const handleView = (_record: IRecord) => {
  visile.value = true;
  detailsId.value = _record?.id;
};

const init = async () => {
  loading.value = true;
  const [err, res] = await to(
    apis.postBdClientServiceOperationQueryServicePackage({
      ...params,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    }),
  );
  loading.value = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    tableData.value = res.data?.records as IRecord[];
    pagination.total = res.data.total as number;
  }
};

onMounted(async () => {
  await init();
});
</script>

<style lang="scss" scoped>
.table-wrapper {
  background-color: white;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  .table-operate {
    display: flex;
    gap: 8px;
  }
  .table-container {
    overflow-y: scroll;
  }

  .paginationView {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}

.name-item {
  display: flex;
  align-items: center;
  > img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: var(--spacing-4);
  }
  // 展示具体的名称
  > span {
    cursor: pointer;
    text-decoration: underline;
    color: var(--primary-color);
    display: inline-block;
    width: 80px;
    white-space: nowrap; /* 确保文本不换行 */
    overflow: hidden; /* 超出部分隐藏 */
    text-overflow: ellipsis;
  }
}
.name-item-desc {
  padding: 8px;
  border-radius: 4px;
  background-color: white;
  box-shadow: 2px 2px 5px #ddd;
}
</style>
