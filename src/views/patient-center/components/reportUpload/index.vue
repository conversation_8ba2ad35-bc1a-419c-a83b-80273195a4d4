<template>
  <a-spin :loading="pageState.infoSaveLoading">
    <a-modal
      v-model:visible="pageState.visible" 
      width="600px" unmount-on-close
      :mask-closable="false" 
      :on-before-ok="onBeforeOk"
      :on-before-cancel="handleCancel"
      @ok="handleOk"
    >
      <template #title>
        报告上传
      </template>
      <div>
        <a-form ref="formRef" :model="form" @submit="handleSubmit">
          <a-form-item field="type" label="选择报告类型" :rules="[{required:true,}]" >
            <a-radio-group v-model="form.type" @change="onRadioChange">
              <a-radio value="1">检验报告</a-radio>
              <a-radio value="2">检查报告</a-radio>
              <a-radio value="3">病历</a-radio>
              <a-radio value="4">体检报告</a-radio>
              <a-radio value="5">健康建议书</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item
            field="name" 
            label="报告名称"
            :rules="[{required:true, message: '请填写报告名称'}]"
          >
            <a-input v-model="form.name" placeholder="请输入报告名称" :max-length="100" allow-clear show-word-limit />
          </a-form-item>
          <a-form-item
            field="date"
            label="检查日期"
            :rules="[{required:true, message: '请选择检查日期'}]">
            <a-date-picker v-model="form.date" placeholder="请选择日期" :disabled-date="(current) => dayjs(current).isAfter(dayjs()) "/>
          </a-form-item>
          <a-form-item
            field="organ" 
            label="诊疗机构"
            :rules="[{required:true, message: '请填写诊疗机构'}]"
          >
            <a-input v-model="form.organ" placeholder="请输入诊疗机构" :max-length="100"  allow-clear  show-word-limit />
          </a-form-item>
          <a-form-item field="upload" label="选择报告类型" :rules="[{required:true,}]">
            <a-radio-group v-model="pageState.uploadImageType" type="button" @change="onUploadImageTypeChange">
              <a-radio value="1" :disabled="form.type === '5'">图片</a-radio>
              <a-radio value="2">PDF</a-radio>
            </a-radio-group>
          </a-form-item>
          <!-- <a-form-item
            field=""
            label=""
            extra="上传文件的大小不能超过20M"
          >
            <div class="uploadBox" style="height: 102px;">
              <div v-for="(item, i) in pageState.uploadImageList" :key="item.uri" class="uploadItemBox">
                <a-image
                  v-if="pageState.uploadImageType === '1'"
                  :preview="false"
                  width="80px"
                  height="80px"
                  :src="item.uri"
                >
                  <template #extra>
                    <div class="actions">
                      <span class="action iconStyle" @click="onPreview(i)"><icon-eye /></span>
                      <span class="action iconStyle" @click="onDeleteImage(i)"><icon-delete /></span>
                    </div>
                  </template>
                </a-image>
                <div v-if="pageState.uploadImageType === '2'" class="PDFBox" >
                  <img :src="pdf" alt="" />
                  <div class="actionsWrap">
                    <div class="actions">
                      <span class="action iconStyle" @click="onPDFPreview(item)"><icon-eye /></span>
                      <span class="action iconStyle" @click="onDeleteImage(i)"><icon-delete /></span>
                    </div>
                  </div>
                </div>
                <div class="uploadBoxItemText">{{ item.name }}</div>
              </div>
              <a-image-preview-group
                v-model:visible="pageState.previewGroupVisible"
                v-model:current="pageState.previewGroupCurrent"
                infinite
                :src-list="pageState.uploadImageList.map(item => item.uri)"
              />
              <div style="height: 102px;">
                <a-spin :loading="pageState.uploadLoading">
                  <a-upload
                    v-if="pageState.uploadImageType === '1' && pageState.uploadImageList.length < 9"
                    accept="image/jpeg,image/png,image/jpg"
                    image-preview
                    :show-file-list="false"
                    :custom-request="customRequest"
                  >
                    <template #upload-button>
                      <div>
                        <div class="arco-upload-picture-card">
                          <div class="arco-upload-picture-card-text">
                            <IconPlus />
                            <div style="margin-top: 10px; font-weight: 600">上传</div>
                          </div>
                        </div>
                      </div>
                    </template>
                  </a-upload>

                  <a-upload
                    v-if="pageState.uploadImageType === '2' && pageState.uploadImageList.length < 1"
                    accept="application/pdf"
                    image-preview
                    :show-file-list="false"
                    :custom-request="customRequest"
                  >
                    <template #upload-button>
                      <div>
                        <div class="arco-upload-picture-card">
                          <div class="arco-upload-picture-card-text">
                            <IconPlus />
                            <div style="margin-top: 10px; font-weight: 600">上传</div>
                          </div>
                        </div>
                      </div>
                    </template>
                  </a-upload>
                </a-spin>
                <div style="width: 10px; height: 22px; line-height: 22px; font-size: 12px;">{{ `` }}</div>
              </div>
            </div>
          </a-form-item> -->
          <a-form-item
            label=""
            field=""
            extra="上传文件的大小不能超过10M"
          >
            <bd-upload
              v-if="pageState.uploadImageType === '1'"
              ref="uploadRef"
              :file-type="EFileType.Img"
            />
            <bd-upload
              v-if="pageState.uploadImageType === '2'"
              ref="uploadRef"
              :file-type="EFileType.File"
              :limit="1"
            />
          </a-form-item>
        </a-form>
      </div>
      <a-modal 
        v-model:visible="pageState.pdfVisible" 
        title="pdf预览" 
        width="800px" 
        :mask-closable="false" 
        :footer="false" 
        unmount-on-close
        @cancel="handlePdfCancel"
      >
        <div style="height: 80vh; overflow: auto;">
          <vue-pdf-embed ref="pdfRef" :source="pageState.pdfSrc" />
        </div>
      </a-modal>
    </a-modal>
  </a-spin>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import dayjs from 'dayjs';
import { getToken } from '@/utils/auth';
import { usePatientStoreWidthOut } from '@/store/modules/patient'
import { Message, Modal } from '@arco-design/web-vue';
import useAppStore from '@/store/modules/app';
import VuePdfEmbed from 'vue-pdf-embed';
import _ from 'lodash';
import bdUpload, {
  EFileType
} from '@/components/bd-upload/index.vue'
import { ERefreshType, setPatientInfoChangeEmitter } from '@/utils/patient-listener';

interface Istate {
  visible: boolean;
  uploadImageType: string;
  uploadImageList: { uri: string, name: string }[];
  previewGroupVisible: boolean;
  previewGroupCurrent: number;
  pdfVisible: boolean;
  pdfSrc: string;
  uploadLoading: boolean;
  infoSaveLoading: boolean;
}

const useStore = usePatientStoreWidthOut()
const appStore = useAppStore()

const token = getToken();

const formRef = ref();
const form = ref<any>({
  type: '1',
  upload: true,
})
// 获取当前的上传数据
const uploadRef = ref();

const pageState = ref<Istate>({
  visible: false,
  uploadImageType: '1',
  uploadImageList: [],
  previewGroupVisible: false,
  previewGroupCurrent: 1,
  pdfVisible: false,
  pdfSrc: '',
  uploadLoading: false,
  infoSaveLoading: false,
})

const handleSubmit = async ({values, errors}: {values: Record<string, any>; errors: any}) => {
  useStore.setUpdateReport();
  console.log('uploadRef', uploadRef.value)
  // if (!pageState.value.uploadImageList.length) {
  if (
    uploadRef.value.fileList.length === 0
  ) {
    Message.warning({
      content:'请添加报告附件',
    });
    return;
  }
  if(errors === undefined) {
    pageState.value.infoSaveLoading = true;
    const payload = {
      ...values,
      // images: pageState.value.uploadImageList.map(item => item.uri),
      images: uploadRef.value.fileList.map((item: any) => item.response.data.uri),
      patientId: (appStore.patientInfo as any).patientId
    };

    delete (payload as any).upload;

    const res = await useStore.patientDiagnoseAdd(payload)
    if(res) {
      Message.success({
        content:'报告上传成功',
      });
      form.value = {type: '1', upload: true,}
      pageState.value.uploadImageList = []
      pageState.value.uploadImageType = '1'
      useStore.setUpdateReport();
      pageState.value.visible = false
    }
    pageState.value.infoSaveLoading = false;
    // 更新诊疗信息数据
    setPatientInfoChangeEmitter(ERefreshType.MedicalTreatmentInformation, true);
  }
}

const openModal = () => {
  pageState.value.visible = true
}

const onBeforeOk = () => {
  formRef.value.handleSubmit()
  return false;
}

const handleOk = () => {
  pageState.value.visible = false
}

const handleCancel = () => {
  if(pageState.value.uploadImageList.length || !_.isEqual(form.value, {type: '1', upload: true,} )) {
    Modal.warning({
      title: '取消确认',
      content: '目前内容未上传，请确认是否离开？',
      bodyStyle: {
        textAlign: 'center',
      },
      hideCancel: false,
      onOk: () => {
        form.value = {type: '1', upload: true,}
        pageState.value.uploadImageList = []
        pageState.value.uploadImageType = '1'
        pageState.value.visible = false
      },
      onCancel: () => {
        console.log('取消')
      },
    })
  } else {
    form.value = {type: '1', upload: true,}
    pageState.value.uploadImageList = []
    pageState.value.uploadImageType = '1'
    pageState.value.visible = false
  }
  
  return false;
}

const handlePdfCancel = () => {
  pageState.value.pdfVisible = false
}

const onUploadImageTypeChange = () => {
  pageState.value.uploadImageList = []
}

const onRadioChange = (v: string | number | boolean) => {
  console.log(v);
  if(+v === 5) {
    pageState.value.uploadImageType = '2'
  }
}

const onDeleteImage = (index: number) => {
  pageState.value.uploadImageList = pageState.value.uploadImageList.filter((item, i) => i !== index)
}

const onPreview = (index: number) => {
  pageState.value.previewGroupCurrent = index;
  pageState.value.previewGroupVisible = true;
}

const onPDFPreview = (item: any) => {
  pageState.value.pdfSrc = item.uri;
  pageState.value.pdfVisible = true;
}

const customRequest = (option: any) => {
  pageState.value.uploadLoading = true;
  const {onError, onSuccess, fileItem, name} = option
  const xhr = new XMLHttpRequest();
  xhr.onerror = function error(e) {
    onError(e);
  };
  xhr.onload = function onload() {
    if (xhr.status < 200 || xhr.status >= 300) {
      onError(xhr.responseText);
    } else {
      const res = JSON.parse(xhr.response)
      if(res.code === '1') {
        pageState.value.uploadImageList = pageState.value.uploadImageList.concat(res.data)
        onSuccess(xhr.response);
      } else {
        onError(xhr.responseText);
      }

    }
    pageState.value.uploadLoading = false;
  };

  const formData = new FormData();
  formData.append(name || 'file', fileItem.file);
  xhr.open('post', `${import.meta.env.VITE_API_BASE_URL}/base/upload/uploadImgMin`, true);
  xhr.setRequestHeader('authorization', `Bearer ${token}`);
  xhr.send(formData);

  return {
    abort() {
      xhr.abort()
    }
  }
};

defineExpose({
	openModal,
});

</script>

<style lang="scss" scoped>
.uploadBox {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  > *:not(:last-child) {
    margin-right: 16px;
  }

  > * {
    margin-bottom: 10px;
  }

  .uploadItemBox {

    &:hover {
      .actions {
        visibility: visible;
        opacity: 1;
      }
    }

    .uploadBoxItemText {
    width: 80px;
    overflow: hidden;
    white-space: nowrap; 
    text-overflow: ellipsis; 
    }

    :global(.arco-image-footer) {
      padding: 0 !important;
      top: 0;
    }

    :global(.arco-image-footer-extra) {
      width: 100%;
      height: 100%;
      padding-left: 0;
    }

    .PDFBox {
      position: relative;
      width: 80px;
      height: 80px;
      background-color: #E5E6EB;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;

      .img {
        width: 32px;
        height: 32px;
      }
    }

    .actionsWrap {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
    }

    .actions {
      width: 100%;
      height: 100%;
      display: flex;
      padding: 9px 16px;
      background: rgba(29, 33, 41, 0.6);
      justify-content: space-between;
      align-items: center;
      visibility: hidden;
      opacity: 0;
    }
  }

  .iconStyle {
    font-size: 20px; 
    cursor: pointer;
  }
}
</style>