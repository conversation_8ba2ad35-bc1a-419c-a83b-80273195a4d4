<template>
  <div class="label-manage-container">
    <div class="title">标签管理</div>
    <!-- 最外层的label元素 -->
    <div class="labelBox">
      <div ref="labelListRef" class="labelList">
        <div
          v-for="(tag) of showElements"
          :key="tag.labelId"
        >
          <a-tooltip v-if="tag.name.length > 4" :content="tag.name">
            <a-tag
              :key="tag.labelId"
              color="#ffffff"
              bordered
              style="color: #1D2129; user-select: none;"
              class="tagsStyle"
            >
              {{ tag.name.slice(0, 4) + '…' }}
            </a-tag>
          </a-tooltip>
          <a-tag
            v-else
            :key="tag.labelId"
            color="#ffffff"
            bordered
            style="color: #1D2129; user-select: none;"
            class="tagsStyle"
          >
            {{ tag.name }}
          </a-tag>
        </div>
        <!-- 展示一个更多元素的标签 -->
        <a-trigger 
          position="bottom"
          auto-fit-position
          :unmount-on-close="false"
          :popup-translate="[20, 20]"
        >
          <a-tag
            v-if="typeof props.showNumbers === 'number' && props.showNumbers !== 0 && pageState.boundTags.length > props.showNumbers"
            color="#ffffff"
            bordered
            style="color: #1D2129; user-select: none;"
            class="tagsStyle"
          >
            ...
          </a-tag>
          <template #content>
            <!-- 超出的部分展示一个三个点 -->
            <div class="label-content">
              <span
                v-for="_label, _index in pageState.boundTags.slice(props.showNumbers)"
                :key="_index"
                class="label-item"
              >
                {{ _label.name }}
              </span>
            </div>
          </template>
        </a-trigger>
      </div>
      <div class="label-manage-btn" @click="onOpenModal">
        <img :src="labelManagePng" />
        <div>管理标签</div>
      </div>
    </div>
    <a-modal 
        v-model:visible="pageState.visible" 
        title="添加/删除标签" 
        width="800px" 
        :mask-closable="false" 
        unmount-on-close
        :on-before-ok="handleOk"
        @cancel="handleCancel"
      >
        <div class="modalContainer">
          <div class="itemContainer">
            <div class="label">已贴标签</div>
            <div class="tagsBox">
              <a-space v-if="pageState.addTags.length" wrap>
                <a-tag
                  v-for="(tag) of pageState.addTags"
                  :key="tag.labelId"
                  color="#ffffff"
                  bordered
                  style="color: #1D2129; cursor: pointer;"
                >
                  {{ tag.name }}
                  <!-- 增加逻辑 如果当前是固定标签里面的标签 不支持进行删除 -->
                  <div
                    v-if="pageState.innateTags.findIndex((_tag: any) => _tag.labelId === tag.labelId) === -1"
                    class="closeIcon"
                    @click.stop="addTagsDelete(tag)"
                  >
                    <icon-close />
                  </div>
                </a-tag>
              </a-space>
            </div>
          </div>
          <!-- 固定标签对应的服务包 由签约的时候 写入数据 前端不再支持进行配置 -->
          <!-- <div class="itemContainer">
            <div class="label">固定标签</div>
            <div class="tagsBox">
              <a-space v-if="pageState.innateTags.length" wrap>
                <a-tag
                  v-for="(tag) of pageState.innateTags"
                  :key="tag.labelId"
                  color="#ffffff"
                  bordered
                  style="color: #1D2129; cursor: pointer;"
                  :class="{ 'disabled-label': pageState.addTags.some(_fi => _fi.labelId === tag.labelId) }"
                  @click.stop="onAddTags(tag)"
                >
                  {{ tag.name }}
                </a-tag>
                <div></div>
              </a-space>
            </div>
          </div> -->
          <div class="itemContainer">
            <div class="label">自定义标签</div>
            <div class="tagsBox">
              <a-space wrap>
                <template v-if="pageState.customTags.length">
                  <a-tag
                    v-for="(tag) of pageState.customTags"
                    :key="tag.labelId"
                    color="#ffffff"
                    bordered
                    style="color: #1D2129; cursor: pointer;"
                    :class="{ 'disabled-label': pageState.addTags.some(_fi => _fi.labelId === tag.labelId) }"
                    @click.stop="onAddTags(tag)"
                  >
                    {{ tag.name }}

                    <div class="closeIcon" @click.stop="customTagsDelete(tag)">
                      <icon-close />
                    </div>
                  </a-tag>
                </template>
                <a-input
                  v-if="pageState.showInput"
                  ref="inputRef"
                  v-model.trim="pageState.inputVal"
                  :style="{ minWidth: '90px', height: '24px'}"
                  size="mini"
                  :max-length="20" 
                  allow-clear 
                  show-word-limit
                  @keyup.enter="handleAdd"
                  @blur="handleAdd"
                />
                <div v-else class="label-manage-btn"  @click="handleEdit">
                  <icon-plus />
                  <div style="margin-left: 6px;">新增标签</div>
                </div>
              </a-space>
            </div>
          </div>
        </div>
      </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, computed } from 'vue';
import { Message } from '@arco-design/web-vue';
import useAppStore from '@/store/modules/app';
import { usePatientStoreWidthOut } from '@/store/modules/patient'
import labelManagePng from '@/assets/images/<EMAIL>';

interface tagType {
  name: string;
  labelId: string;
}

interface Istate {
  visible: boolean;
  boundTags: tagType[];
  addTags: tagType[];
  innateTags: tagType[];
  customTags: tagType[];
  showInput: boolean;
  inputVal: any;
}

const appStore = useAppStore()
const useStore = usePatientStoreWidthOut()

const labelListRef = ref()
const inputRef = ref()
const pageState = ref<Istate>({
  visible: false,
  showInput: false,
  boundTags: [],
  addTags: [],
  innateTags: [],
  customTags: [],
  inputVal: undefined,
})
const props = defineProps([
  'showNumbers'
])

// 展示的标签元素
const showElements = computed(() => {
  if (
    typeof props.showNumbers === 'number' &&
    props.showNumbers !== 0
  ) {
    return pageState.value.boundTags.slice(0, props.showNumbers)
  }
  return pageState.value.boundTags
})

const queryPatientLabelList = async () => {
  const payload = {
      patientId: (appStore.patientInfo as any).patientId
    };
  const res = await useStore.patientLabelList(payload)
  if(res) {
    console.log(res,9999)
    const list = res?.content ?? [];
    pageState.value.boundTags = list;
    pageState.value.addTags = list;
  }
};

const queryPatientLabelGroupList = async (isOpen?: boolean) => {
  const res = await useStore.patientLabelGroupList({});
  if(res) {
    const list = res?.content ?? [];
    list.forEach((item: any) => {
      if(item.type === 0) {
        pageState.value.innateTags = item.labels;
      }

      if(item.type === 1) {
        pageState.value.customTags = item.labels;
      }
    })
    if(isOpen) {
      pageState.value.visible = true
    }
  }
}

const handleOk = async () => {
  const payload = {
    patientIds: [
      (appStore.patientInfo as any).patientId,
    ],
    labelIds: pageState.value.addTags.map(item => item.labelId)
  }
  const res = await useStore.patientLabelAdd(payload);
  if(res) {
    Message.success({
        content:'保存成功',
      });
    pageState.value.visible = false
    queryPatientLabelList()
  }
  return false;
}

const handleCancel = () => {
  pageState.value.addTags = pageState.value.boundTags
  pageState.value.visible = false
}

const onOpenModal = async () => {
  // const userInfo = localStorage.getItem('user_info')
  // const userInfoData = userInfo ? JSON.parse(userInfo) : {}
  // const payload = {
  //   // teamId: (userInfoData as any)?.accountInfo?.teamId ?? ''
  // }
  // const res = await useStore.patientLabelGroupList(payload);
  // if(res) {
  //   const list = res?.content ?? [];
  //   list.forEach((item: any) => {
  //     if(item.type === 0) {
  //       pageState.value.innateTags = item.labels;
  //     }

  //     if(item.type === 1) {
  //       pageState.value.customTags = item.labels;
  //     }
  //   });
  //   pageState.value.visible = true
  // }
  queryPatientLabelGroupList(true)
}

const handleAdd = async () => {
  if (pageState.value.inputVal) {
    const res = await useStore.patientUserDefinedLabelAdd({ name: pageState.value.inputVal })
    if(res) {
      pageState.value.customTags.push({ name: pageState.value.inputVal, labelId: res.labelId});
      pageState.value.inputVal = '';
      pageState.value.showInput = false;
    }
  } else {
    pageState.value.showInput = false;
  }
};

const handleEdit = () => {
  pageState.value.showInput = true;

  nextTick(() => {
    if (inputRef.value) {
      inputRef.value.focus();
    }
  });
};

const onAddTags = (record: any) => {
  const index = pageState.value.addTags.findIndex(ite => ite.labelId === record.labelId)
  if(index === -1) {
    pageState.value.addTags = ([] as any).concat(pageState.value.addTags, [record])
  }
}

const addTagsDelete = (record : any) => {
  pageState.value.addTags = pageState.value.addTags.filter(ite => ite.labelId !== record.labelId)
}

const customTagsDelete = async (record: any) => {
  const res = await useStore.patientUserDefinedLabelDelete({ labelId: record.labelId })
  if(res) {
    console.log(res)
    queryPatientLabelGroupList()
    queryPatientLabelList()
  }
}

const horizontalScrolling = () => {
  const container = labelListRef.value;
  container.addEventListener("wheel", (e: { preventDefault: () => void; deltaY: any; }) => {
    e.preventDefault();
    container.scrollLeft += e.deltaY;
  });
};

onMounted(() => {
  horizontalScrolling()
  queryPatientLabelList()
})
</script>

<style lang="scss" scoped>
.label-manage-container {
  display: flex;
  align-items: center;

  >.title {
    margin-right: 8px;
    min-width: 56px;
  }

  >.labelBox {
    display: flex;
    align-items: center;
    border-radius: 4px;
    background: #F7F7F7;
    padding: 9px 8px;

    >.labelList {
      display: flex;
      align-items: center;
      gap: 8px;
      max-width: 400px;
      height: 100%;
      overflow: auto;

      &::-webkit-scrollbar {
        height: 8px;
      }

      &::-webkit-scrollbar-thumb {
        cursor: pointer;
        border-radius: 8px;
        background-color: #abaaaa;
      }

      &::-webkit-scrollbar-track {
        background-color: #e7e7e7;
      }
    }
  }

  .tagsStyle {
    color: #1D2129;
    font-size: 14px;
    padding: 1px 8px;
  }
}

.label-content {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-4);
  padding: 8px;
  max-width: 400px;
  border-radius: 4px;
  background-color: white;
  box-shadow: 2px 2px 5px #ddd;
  .label-item {
    font-size: 12px;
    font-weight: 500;
    color: #1D2129;
    padding: 3px 8px;
    border: 1px solid rgb(var(--gray-3));
    border-radius: 2px;
    margin-right: 8px;
    &:last-child {
      margin-right: 0px;
    }
  }
}

.label-manage-btn {
  padding: 1px 8px;
  border-radius: 2px;
  background: #0052D9;
  display: flex;
  align-items: center;
  font-size: 14px;
  // height: 22px;
  // line-height: 14px;
  font-weight: normal;
  color: #FFFFFF;
  cursor: pointer;
  margin-left: 7px;

  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: rgb(var(--primary-5));;
  }

  >img {
    width: 14px;
    height: 14px;
    margin-right: 6px;
  }
}

.modalContainer {
  width: 100%;

  > *:not(:last-child) {
    margin-bottom: 16px;
  }

  >.itemContainer {
    width: 100%;
    display: flex;
    
    >.label {
      width: 86px;
      height: 41px;
      line-height: 41px;
      margin-right: 8px;
      text-align: right;
    }

    >.tagsBox {
      flex: 1;
      min-height: 41px;
      border-radius: 4px;
      background: #F2F3F5;
      padding: 8px 8px 0px 8px;
      .disabled-label {
        pointer-events: none;
        color: var(--label-disabled-color) !important;
      }
    }
  }
}

.closeIcon {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: var(--color-fill-3);
  }
}
</style>