<template>
  <div class="followup-list">
    <!-- 评估列表 -->
    <div class="list">
      <h2>随访列表</h2>
      <!-- 时间的搜索条件 -->
      <!-- <div class="list-time-select">
        <a-range-picker
          @change="handleDateChange"
        />
      </div> -->
      <div class="list-item-container">
        <!-- 显示待办的信息 -->
        <followup-item
          v-for="_item, _index in packageList"        
          :key="_index"
          :item="_item"
          :active="activeKeyInfo?.followId === _item.followId"
          @click="handleTodo"
        />
      </div>
    </div>
    <!-- 评估的内容展示 -->
    <div class="content">
      <div class="main-content">
        <!-- <div class="result-title">
          {{ followTitle }}
        </div> -->
        <div
          v-if="activeInfo?.formId"
          class="result-content"
        >
          <div
            v-if="!activeKeyInfo?.nextFollowTime && !activeInfo?.contentId"
            class="result-content-empty"
          >
            暂无需要随访的内容
          </div>
          <!-- form-id、schema-id、content-id 是查看表单数据的最小数据 -->
          <evaluate-item
            v-else
            ref="evaluateItemRef"
            :name="activeInfo?.taskName"
            :action-code="activeInfo?.actionCode"
            :uuid="activeInfo?.formId + activeInfo?.schemaId"
            :action-record-id="activeInfo?.actionCode"
            :event-flow-id="activeInfo?.eventFlowId"
            :patient-id="appStore.patientInfo.patientId"
            :extend-id="activeInfo?.eventFlowId"
            :task-id="activeInfo?.taskId"
            :task-record-id="activeInfo?.taskRecordId"
            
            
            :form-id="activeInfo?.formId"
            :schema-id="activeInfo?.schemaId"
            :content-id="activeInfo?.contentId ?? undefined"

            :custom-submit="true"
            :init-data="initData"
          />
        </div>
        <div
          v-if="!activeInfo?.contentId"
          class="result-footer"
        >
          <a-button
            @click="handleTemplateSave"
          >
            <template #icon>
              <icon-clock-circle />
            </template>
            <template #default>暂存</template>
          </a-button>
          <a-button
            type="primary"
            @click="handleSubmit"
          >
            <template #icon>
              <icon-upload />
            </template>
            <template #default>提交</template>
          </a-button>
        </div>
      </div>
      <!-- 对应类型的评估历史记录展示 -->
      <history
        ref="historyRef"
        :active-info="activeKeyInfo"
        :task-name="diseaseName"
        :task-code="activeInfo?.taskCode"
        @handle-detail="handleHistory"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { addFormContent, addPatientFollowUp, getCompleteTask, ICompleteTaskRes } from '@/api/patients'
import { GetPatientFollowupHisUsingPOSTPatientFollowHisResVo, GetPatientPackageUsingPOSTBdFollowTypeCountResVo, postBdManageApiFollowupGetFollowInfoById, postBdManageApiFollowupGetPatientFollowupHis, postBdManageApiFollowupGetPatientFollowupNow, postBdManageApiFollowupGetPatientFollowupNowResponse, postBdManageApiFollowupGetPatientPackage, postBdManageApiFollowupGetPatientPackageResponse, postBdManageApiFollowupSaveFollowResult } from '@/batchApi';
import to from 'await-to-js';
import dayjs from 'dayjs';
import { computed, onMounted, ref, watch } from 'vue';
import useAppStore from '@/store/modules/app';
import { useUserStore } from '@/store';
import message from '@arco-design/web-vue/es/message';
import { CalendarValue } from '@arco-design/web-vue/es/date-picker/interface';
import { makeAsRead } from '@/api/evaluation';
import { EPackageCode } from '@/types/enums';
import history from './components/history.vue';
import evaluateItem from '../evaluate-item/index.vue';
import followupItem from './components/followup-item.vue';

const appStore = useAppStore();
const userStore = useUserStore();
const evaluateItemRef = ref();

const packageList = ref<postBdManageApiFollowupGetPatientPackageResponse['data']>([]);
const activeKeyInfo = ref<GetPatientPackageUsingPOSTBdFollowTypeCountResVo>();
// 当前需要去执行的任务的数据
const activeInfo = ref<ICompleteTaskRes>({
  // 测试数据
  // formId: '748111506368892833',
  // schemaId: '750161193284406845',
});
const historyRef = ref();

const initData = computed(() => {
  return {
    visitingDoctorSignature: userStore.accountInfo.name
  }
})

// 获取指定的时间 是在第几个季度
const getQuarterDesc = (date: string): string => {
  const dateObj = dayjs(date);
  const year = dateObj.year();
  const month = dateObj.month() + 1;
  
  // 计算季度
  const quarter = Math.ceil(month / 3);
  
  // 中文数字映射
  const chineseNumbers = ['一', '二', '三', '四'];
  
  return `${year ?? ''}年第${chineseNumbers[quarter - 1] ?? ''}季度`;
};

const getDiseaseName = (serviceCode: string) => {
  let diseaseName = '';
  switch(serviceCode) {
    case EPackageCode.GXY: diseaseName = '高血压';break;
    case EPackageCode.JZXJJ: diseaseName = '甲状腺结节';break;
    case EPackageCode.GZXZ: diseaseName = '混合性高脂血症';break;
    case EPackageCode.GNSXZ: diseaseName = '高尿酸血症';break;
    case EPackageCode.TNB: diseaseName = '糖尿病';break;
    case EPackageCode.FP: diseaseName = '肥胖';break;
    case EPackageCode.CH: diseaseName = '冠心病';break;
    case EPackageCode.HF: diseaseName = '心力衰竭';break;
    case EPackageCode.HA: diseaseName = '心律失常';break;
    default: break;
  }
  return diseaseName ?? ''
}

const diseaseName = computed(() => {
  const serviceCode = activeKeyInfo.value?.serviceCode ?? ''
  return getDiseaseName(serviceCode)
})

const followTitle = computed(() => {
  const nextFollowTime = activeKeyInfo.value?.nextFollowTime
  // 2025年第一季度【高血压】随访
  return `${nextFollowTime ? getQuarterDesc(nextFollowTime ?? '') : ''}【${diseaseName.value}】随访`
})

// 点击提交表单的数据
const handleSubmit = () => {
  evaluateItemRef.value?.submitForm((
    // 提交的表单数据 每个表单中的数据结构不一致
    _values: any
  ) => {
    addFormContent({
      formId: activeInfo.value.formId,
      schemaId: activeInfo.value.schemaId,
      content: JSON.stringify(_values)
    }).then(_res => {
      if (
        typeof _res.data.contentId === 'string'
      ) {
        postBdManageApiFollowupSaveFollowResult({
          followId: activeKeyInfo.value?.followId,
          // followResults: _values.followupClassify,
          followResults: _values.followupClassify === '控制满意' ? '1' : '2',
          formContentId: _res.data.contentId
        }).then(_saveRes => {
          message.success('随访保存成功！')
          if (
            typeof _res.data.contentId === 'string' &&
            _res.data.contentId !== '' &&
            activeKeyInfo.value?.followId
          ) {
            getFollowDetailById(activeKeyInfo.value?.followId);
            // 保存成功之后 当前展示历史的数据
            // activeInfo.value.contentId = _res.data.contentId
          }
          getFollowPackages()
          console.log('_res', _res, _saveRes)
          // 更新历史数据的列表
          if (
            typeof historyRef.value?.reload === 'function'
          ) {
            historyRef.value?.reload()
          }
        })
      }
    })
  });
}

// 暂存当前的数据
const handleTemplateSave = () => {
  evaluateItemRef.value?.handleTs()
  message.success('暂存成功！');
}

// 点击展示todo去做
const handleTodo = (_record) => {
  console.log('todo_record', _record)
  activeKeyInfo.value = JSON.parse(JSON.stringify(_record))
  getFollowDetailById(_record?.followId)
}

// 根据随访id 获取表单的详情
const getFollowDetailById = (_followId: number) => {
  postBdManageApiFollowupGetFollowInfoById({
    followId: _followId
  }).then(_res => {
    if (
      _res.data
    ) {
      if (
        activeKeyInfo.value
      ) {
        activeKeyInfo.value.followId = _res.data.followId as unknown as number;
      }
      activeInfo.value.formId = _res.data.formId as unknown as string;
      activeInfo.value.schemaId = _res.data.formSchemaId as unknown as string;
      activeInfo.value.contentId = _res.data.formContentId as unknown as string;
    }
  })
}

// 点击展示对应的历史记录
const handleHistory = (
  _info: GetPatientFollowupHisUsingPOSTPatientFollowHisResVo
) => {
  if (
    _info.followId
  ) {
    getFollowDetailById(_info.followId);
  }
}

// 获取当前正在进行随访的服务包的数据
const getFollowPackages = () => {
  postBdManageApiFollowupGetPatientPackage({
    patientId: appStore.patientInfo.patientId as unknown as number
  }).then(_res => {
    if (
      Array.isArray(_res.data)
    ) {
      packageList.value = _res.data
      if (
        _res.data?.[0] && !activeKeyInfo.value
      ) {
        handleTodo(_res.data?.[0])
      }
      // activeKeyInfo.value = _res.data?.[0] || undefined
    }
    console.log('follow_packages', _res)
  })
}

const init = async () => {
  getFollowPackages();
}

onMounted(async () => {
  await init()
  console.log('historyRef', historyRef.value)
})


</script>

<style lang="scss" scoped>
.followup-list {
  display: flex;
  align-items: start;
  justify-content: space-between;
  gap: var(--spacing-4);
  height: 100%;
  >.list {
    display: flex;
    flex-direction: column;
    width: 246px;
    height: 100%;
    border-radius: var(--border-radius-medium);
    background-color: white;
    >h2 {
      padding: var(--spacing-7) var(--spacing-7) 0 var(--spacing-7);
      color: var(--text-span-color);
      font-size: var(--font-size-body-3);
      margin: 0;
    }
    // 展示时间搜索框的样式
    >.list-time-select {
      margin-top: var(--spacing-4);
      padding: 0 var(--spacing-7);
    }
    >.list-item-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      row-gap: 16px;
      overflow-y: auto;
      margin-top: var(--spacing-6);
      padding: 0 var(--spacing-7) var(--spacing-7) var(--spacing-7);
    }
  }
  >.content {
    flex: 1;
    height: 100%;
    display: flex;
    >.main-content {
      display: flex;
      flex-direction: column;
      width: 100%;
      padding: var(--spacing-7);
      border-radius: var(--border-radius-medium);
      background-color: white;
      border-right: 1px solid var(--border-color);
      >.result-title {
        font-size: 28px;
        font-weight: bold;
        text-align: center;
      }
      >.result-content {
        flex: 1;
        // padding-top: var(--spacing-7);
        >.result-content-empty {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 18px;
          font-weight: bold;
          text-align: center;
        }
      }
      >.result-footer {
        text-align: right;
        margin-top: 18px;
        >button {
          &:nth-of-type(1) {
            color: white;
            margin-right: 24px;
            background-color: #3EC3CF;
          }
        }
      }
    }
  }
}
</style>