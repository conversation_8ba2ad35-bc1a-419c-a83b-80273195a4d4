<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2025-04-01 15:57:14
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 22:53:00
-->
<template>
  <div class="followup-history-container">
    <div class="header">
      <h2>随访</h2>
      <div
        v-if="itemList?.length !== 0"
        class="more cursor-pointer"
        @click="handleMore()"
      >
        <span>更多</span>
        <icon-right />
      </div>
    </div>
    <div class="content">
      <followup-item
        v-for="item, index in itemList"
        :key="index"
        :item="{ ...item }"
        :active="false"
        card
        @click="handleItem"
      />
    </div>
    <div
      v-if="itemList?.length === 0"
      class="content-empty"
    >
      <a-empty
        description="暂无随访历史"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore, usePatientCenterStore } from '@/store';
import { GetPatientPackageUsingPOSTBdFollowTypeCountResVo, postBdManageApiFollowupGetPatientPackage, postBdManageApiFollowupGetPatientPackageResponse } from '@/batchApi';
import { markRaw, onMounted, ref } from 'vue';
import followupList from './list.vue'
import followupItem from './components/followup-item.vue'

const patientCenterStore = usePatientCenterStore()
const appStore = useAppStore()

const itemList = ref<postBdManageApiFollowupGetPatientPackageResponse['data']>([]);

const getFollowPackage = () => {
  postBdManageApiFollowupGetPatientPackage({
    patientId: appStore.patientInfo.patientId as unknown as number
  }).then(_res => {
    if (
      Array.isArray(_res.data)
    ) {
      itemList.value = _res.data;
    }
    console.log('getFollowPackage_res', _res)
  })
}

const handleItem = (_item: GetPatientPackageUsingPOSTBdFollowTypeCountResVo)  => {
  console.log('handle_item', _item);
  // patientCenterStore.changeSecondaryMenu(true, '随访', [
  //   {
  //     name: '',
  //     component: markRaw(followupList),
  //     componentParams: {
  //       patientId: appStore.patientInfo.patientId,
  //       followId: _item.
  //     }
  //   }
  // ]);
}

// 查看当前患者的具体随访信息
const handleMore = () => {
  patientCenterStore.changeSecondaryMenu(true, '随访', [
    {
      name: '',
      component: markRaw(followupList),
      componentParams: {
        patientId: appStore.patientInfo.patientId,
      }
    }
  ]);
}

onMounted(() => {
  getFollowPackage()
})


</script>

<style lang="scss" scoped>
.followup-history-container {
  flex: 1;
  // flex: unset !important;
  display: flex;
  flex-direction: column;
  width: 25vw;
  padding: var(--spacing-7) 0;
  border-radius: var(--border-radius-medium);
  background-color: white;
  >.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-7);
    >h2 {
      color: var(--text-span-color);
      margin: 0 !important;
      font-size: var(--font-size-body-3);
    }
    >.more {
      color: var(--text-gray-color);
      >span {
        margin-right: var(--spacing-2);
      }
    }
  }
  >.content {
    // flex: 1;
    overflow-y: auto;
    padding: var(--spacing-7);
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    column-gap: 16px;
    row-gap: 16px;
  }
  >.content-empty {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>