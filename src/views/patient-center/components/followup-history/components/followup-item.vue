<template>
  <div
    class="content-item"
    :class="{
      active: active === true,
      disActive: active === false,
      card: card === true
    }"
    @click="handleClick"
  >
    <div class="title">
      <span>{{ item?.name }}</span>
      <span>{{ item?.completeTotal }} / {{ item?.total }}</span>
    </div>
    <div
      v-if="item?.nextFollowTime"
      class="info"
    >
      <span>下次随访</span>
      <span
        :class="{
          outDate: isOutDate
        }"
      >
        {{ dayjs(item?.nextFollowTime).format('YYYY-MM-DD') }}
      </span>
    </div>
    <div
      class="result"
    >
      <span>上次结果</span>
      <span
        :class="{
          'dissatisfy': item?.lastResults === '2',
          'satisfy': item?.lastResults === '1'
        }"
      >
        {{ item?.lastResults ? item?.lastResults === '1' ? '满意' : '不满意' : '-' }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  GetPatientPackageUsingPOSTBdFollowTypeCountResVo
} from '@/batchApi'
import dayjs from 'dayjs';
import { computed } from 'vue';

const {
  item,
  active,
  card = false
} = defineProps<{
  item: GetPatientPackageUsingPOSTBdFollowTypeCountResVo,
  active: boolean,
  card?: boolean
}>()

const isOutDate = computed(() => {
  if (!item?.nextFollowTime) return false;
  return dayjs(item.nextFollowTime).isBefore(dayjs(), 'day');
});

const emit = defineEmits(['click'])

const handleClick = () => {
  emit('click', item);
}

</script>

<style lang="scss" scoped>
.content-item {
  padding: var(--spacing-6) var(--spacing-6) var(--spacing-5) var(--spacing-6);
  border-radius: var(--border-radius-medium);
  background-color: var(--backlog-item-color);
  height: max-content;
  // 列表展示的时候使用的样式
  &.active {
    border: 1px solid var(--primary-color);
  }
  // 在列表进行选择的时候展示的样式
  &.disActive {
    border: 1px solid var(--evaluate-border-color);
    background-color: white;
  }
  // 最外层用卡片展示的时候的样式
  &.card {
    border-color: var(--backlog-item-color);
    background-color: var(--backlog-item-color);
  }
  >.title, >.info, >.result {
    display: flex;
    justify-content: space-between;
  }
  >.title {
    margin-bottom: var(--spacing-4);
    >span {
      color: var(--text-h1-color);
      font-size: var(--font-size-body-3);
      font-weight: 700;
    }
  }
  >.info {
    margin-bottom: var(--spacing-4);
    >span {
      font-size: var(--font-size-body-1);
      &:nth-of-type(1) {
        color: var(--text-gray-deep-color);
      }
      &:nth-of-type(2) {
        color: var(--primary-color);
      }
      &.outDate {
        color: var(--text-outdate-color);
      }
    }
  }
  >.result {
    >span {
      font-size: var(--font-size-body-1);
      &:nth-of-type(1) {
        color: var(--text-gray-deep-color);
      }
      &:nth-of-type(2) {
        display: inline-block;
        font-weight: bold;
        padding: var(--spacing-2) var(--spacing-4);
        border-radius: var(--border-radius-small);
      }
      &.dissatisfy {
        color: var(--text-dissatisfy-color);
        background-color: var(--dissatisfy-bg-color);
      }
      &.satisfy {
        color: var(--text-satisfaction-color);
        background-color: var(--satisfaction-bg-color);
      }
    }
  }
}
</style>