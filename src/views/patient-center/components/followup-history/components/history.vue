<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-10-24 09:43:02
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-04-28 17:12:34
-->
<template>
  <div class="content-record">
    <h2>{{ taskName }}随访历史记录</h2>
    <div class="content-record-container">
      <div
        v-for="_item, _index in historyList"
        :key="_index"
        :class="{
          'content-record-wrapper': true,
          'active': _item.followId === activeInfo?.followId
        }"
        @click="handleShowDetail(_item)"
      >
        <div class="content-record-item">
          <div class="item-circle">
            <div class="circle"></div>
            <div v-if="_index !== (Array.isArray(historyList) ? historyList.length - 1 : 0)" class="line"></div>
          </div>
          <div class="item-content">
            <div class="info">
              <span>{{ dayjs(_item?.completeTime).format('YYYY.MM.DD') }}</span>
              <span>{{ _item?.doctorName }}</span>
            </div>
            <div class="result">
              <span>随访结果</span>
              <span
                :class="{
                  dissatisfy: _item?.followResults === '2'
                }"
              >
                {{ _item?.followResults === '1' ? '满意' : '不满意' }}
              </span>
            </div>
          </div>
          <!-- <div class="time-info">
            <span>{{ _item.date }}</span>
            <span>{{ _item.executeUserName }}</span>
          </div>
          <div
            :class="{ 'content-wrapper': true, 'last': _index === historyList.length - 1 }"
          >
            <div :class="{ content: true, active: _item.taskRecordId === props.activeKey }">
              {{ _item.resultDesc || '-' }}
            </div>
          </div> -->
        </div>
      </div>
      <!-- 没有数据的时候 展示的提示 -->
      <a-empty
        v-if="historyList?.length === 0"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import to from 'await-to-js';
import { onMounted, ref, watch } from 'vue';
import useAppStore from '@/store/modules/app';
import { GetPatientFollowupHisUsingPOSTPatientFollowHisResVo, GetPatientPackageUsingPOSTBdFollowTypeCountResVo, postBdManageApiFollowupGetPatientFollowupHis, postBdManageApiFollowupGetPatientFollowupHisResponse } from '@/batchApi';
import dayjs from 'dayjs'

const appStore = useAppStore();

const {
  activeInfo,
  taskCode,
  taskName,
} = defineProps<{
  activeInfo: GetPatientPackageUsingPOSTBdFollowTypeCountResVo | undefined
  taskCode: string
  taskName: string
}>()

const emits = defineEmits([
  'handleDetail'
])

const historyList = ref<postBdManageApiFollowupGetPatientFollowupHisResponse['data']>([])

const init = async () => {
  const [err, res] = await to(postBdManageApiFollowupGetPatientFollowupHis({
    patientId: appStore.patientInfo.patientId as unknown as number,
    serviceCode: activeInfo?.serviceCode
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    Array.isArray(res.data)
  ) {
    historyList.value = res.data
  }
  console.log('history_res', res)
}

// 点击展示历史数据
const handleShowDetail = (_item: GetPatientFollowupHisUsingPOSTPatientFollowHisResVo) => {
  emits('handleDetail', _item)
}

onMounted(async () => {
  await init()
})

watch([
  () => activeInfo?.serviceCode,
  () => activeInfo?.followId
], async () => {
  await init();
})

defineExpose({
  reload: init
})


</script>

<style scoped>
.content-record {
  width: 278px;
  border-radius: var(--border-radius-medium);
  background-color: white;
  display: flex;
  flex-direction: column;
  >h2 {
    padding: var(--spacing-7);
    color: var(--text-span-color);
    font-size: var(--font-size-body-3);
    margin: 0;
  }
  >.content-record-container {
    flex: 1;
    overflow-y: auto;
    padding: 0 var(--spacing-7) var(--spacing-7) var(--spacing-7);
    >.content-record-wrapper {
      &:first-child {
        margin-top: 12px;
      }
      &.active {
        >.content-record-item {
          >.item-content {
            border-color: var(--primary-color);
          }
        }
      }
      >.content-record-item {
        display: flex;
        column-gap: 16px;
        >.item-circle {
          display: flex;
          flex-direction: column;
          align-items: center;
          >.circle {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: var(--border-gray-color);
          }
          >.line {
            flex: 1;
            width: 1px;
            margin-top: 3px;
            margin-bottom: 3px;
            background-color: var(--border-gray-color);
          }
        }
        /* >.time-info {
          &::before {
            content: '';
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: var(--backlog-item-sub-color);
            margin-right: var(--spacing-4);
          }
        }
        >.content-wrapper {
          padding: 20px;
          margin-left: 4px;
          border-left: 1px solid var(--backlog-item-sub-color);
          &.last {
            border-left: none;
          }
          >.content {
            padding: 14px var(--spacing-7);
            border-radius: var(--border-radius-medium);
            border: 1px solid var(--gray-bg-color);
            background-color: var(--gray-bg-color);
            &.active {
              border: 1px solid var(--evaluation-bg-color) !important;
              background-color: var(--backlog-item-color);
            }
          }
        } */
        >.item-content {
          border: 1px solid transparent;
          flex: 1;
          padding: var(--spacing-6);
          margin-bottom: 30px;
          border-radius: var(--border-radius-medium);
          background-color: var(--backlog-item-color);
          >.info, >.result {
            display: flex;
            align-items: center;
            justify-content: space-between;
            >span {
              font-size: var(--font-size-body-3);
            }
          }
          >.info {
            >span {
              font-weight: bold;
              &:nth-of-type(1) {
                color: black;
              }
              &:nth-of-type(2) {
                color: var(--text-span-color);
              }
            }
          }
          >.result {
            margin-top: var(--spacing-2);
            >span {
              &:nth-of-type(1) {
                color: var(--text-h1-color);
              }
              &:nth-of-type(2) {
                color: var(--text-satisfaction-color);
              }
              &.dissatisfy {
                color: var(--text-dissatisfy-color);
              }
            }
          }
        }
      }
    }
  }
}
</style>