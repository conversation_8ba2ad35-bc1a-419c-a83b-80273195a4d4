<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-10-22 15:15:21
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-05-21 16:54:23
-->
<template>
  <div class="tcm-container">
    <h2>中医体质辨识</h2>
    <div class="info">
      <span>当前评估</span>
      <div v-if="lastRecord">
        <a-tooltip :content="lastRecord.resultDesc">
          <span>{{ lastRecord.resultDesc || '-' }}</span>
        </a-tooltip>
        <span>{{ lastRecord.date ?? '-' }}</span>
      </div>
      <div v-else>
        <span>-</span>
      </div>
    </div>
    <!-- 展示图片 -->
    <img :src="HealthPeoplePng" alt="">
  </div>
</template>

<script setup lang="ts">
import { getTaskList } from '@/api/patients';
import HealthPeoplePng from '@/assets/images/360-health-people.png';
import useAppStore from '@/store/modules/app';
import to from 'await-to-js';
import { onMounted, ref } from 'vue';

const appStore = useAppStore()
const lastRecord = ref()

const init = async () => {
  const [err, res] = await to(getTaskList({
    patientId: appStore.patientInfo.patientId,
    taskCode: 'TCM_constitution'
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    Array.isArray(res.data.items) && 
    res.data.items.length !== 0
  ) {
    lastRecord.value = res.data.items?.[0]
  }
  console.log('res', res)
}

onMounted(async () => {
  await init()
})

</script>

<style lang="scss" scoped>
.tcm-container {
  position: relative;
  width: 21vw;
  // min-width: 380px;
  padding: var(--spacing-7);
  border-radius: var(--border-radius-medium);
  background-color: white;
  >h2 {
    color: var(--text-span-color);
    margin: 0 !important;
    font-size: var(--font-size-body-3);
  }
  // 展示中医体质评估的内容
  >.info {
    top: 24px;
    right: 16px;
    position: absolute;
    padding: 14px;
    text-align: left;
    // display: flex;
    // align-items: start;
    // justify-content: space-between;
    // gap: 8px;
    width: 120px;
    background-color: var(--tcm-info-bg-color);
    border-radius: var(--border-radius-medium);
    margin-top: var(--spacing-7);
    // 当前评估
    >span {
      color: var(--text-span-color);
      font-size: var(--font-size-body-3);
    }
    // 具体的评估内容
    >div {
      // flex: 1;
      // display: flex;
      // align-items: start;
      // max-width: 200px;
      >span {
        // 本次评估的体质内容
        &:first-child {
          margin: var(--spacing-2) 0;
          display: block;
          color: var(--text-span-color);
          font-size: var(--font-size-body-3);
          font-weight: var(--font-weight-700);
          width: 100px;
          white-space: nowrap; /* 确保文本不换行 */
          overflow: hidden; /* 超出部分隐藏 */
          text-overflow: ellipsis;
        }
        // 本次评估的日期
        &:last-child {
          color: var(--text-gray-color);
          font-size: var(--font-size-body-1);
        }
      }
    }
  }
  >img {
    display: block;
    margin: var(--spacing-8) auto 0 auto;
    // width: calc(100% - 90px);
    // max-width: 100%;
    height: calc(100% - 40px);
  }
}
</style>