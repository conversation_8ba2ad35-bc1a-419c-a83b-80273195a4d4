/* 服务信息 */
export const serviceInformation = [
  { title: "支付类型", attributeName: "payType",enumMap: {'1': '权益核销','2': '患者支付'},col: {name: 2,value: 20} },
  { title: "服务项目", attributeName: "serviceName",col: {name: 2,value: 20} },
  { title: "预约时间", attributeName: "appointTime",col: {name: 2,value: 20} },
  { title: "首诊类型", attributeName: "firstVisitType",extend: true,enumMap: {'0': '线上首诊','1': '线下首诊'},col: {name: 2,value: 20} },
  { title: "首诊机构", attributeName: "firstVisitOrgId",extend: true, col: {name: 2,value: 20} },
];

/* 订单信息 */
export const OrderInfo = [
  { title: "操作人", attributeName: "createUser",col: {name: 2,value: 20} },
  { title: "订单号", attributeName: "consultationId",col: {name: 2,value: 20} },
  { title: "提交时间", attributeName: "xCreateTime",col: {name: 2,value: 20} },
];
/* 基本健康信息 */
export const BaseHealthInfo = [
  { title: "现病史", attributeName1: "hasCurrentHistory",attributeName: 'currentHistory', col: { name: 4, value: 20 } },
  { title: "过往病史", attributeName1: "hasPastDisease",attributeName: 'pastDisease', col: { name: 4, value: 20 } },
  { title: "过敏史", attributeName1: "hasAllergyHistory",attributeName: 'allergyHistory', col: { name: 4, value: 20 } },
]