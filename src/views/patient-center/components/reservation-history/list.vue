<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-12-18 13:46:38
 * @LastEditors: yangrongxin
 * @LastEditTime: 2024-12-18 14:27:30
-->
<template>
  <a-spin style="width: 100%; height: 100%" :loading="pageState.loading">
    <div class="reservation-list">
      <!-- 评估列表 -->
      <div class="list">
        <h2>服务预约记录</h2>
        <div v-if="!pageState.historyList?.length" class="listEmpty">
          <a-empty />
        </div>
        <div v-else class="list-item-container">
          <div
            v-for="(_item, _index) in pageState.historyList"
            :key="_index"
            :class="{
              'list-item': true,
              active: pageState.activeKey === _item.id,
            }"
            @click="handleToggleRecord(_item)"
          >
            <div class="title">
              <h2>{{ _item.serviceName }}</h2>
              <span>{{ _item?.statusName }}</span>
              <!-- 评估如果是未读 展示一下红点 -->
              <!-- <div v-if="!_item.read" class="red-circle"></div> -->
            </div>
            <a-row :gutter="[0, 4]">
              <a-col :span="8">预约时间：</a-col>
              <a-col :span="16">{{ _item.appointTime ?? "-" }}</a-col>
              <!-- <a-col :span="8">渠道：</a-col>
              <a-col :span="16">{{ _item.channelName || "-" }}</a-col> -->
              <template v-if="_item?.firstVisitOrgId">
                <a-col :span="8">就诊医院：</a-col>
                <!-- 这里没有维护专门的表，传递时候写死，后续增家表后再修改 -->
                <a-col :span="16">{{ _item?.firstVisitOrgId || "-" }}</a-col>
              </template>
            </a-row>
          </div>
        </div>
      </div>
      <!-- 评估的内容展示 -->
      <div class="content">
        <h2>预约详情</h2>
        <div v-if="false" class="emptyView">
          <a-empty />
        </div>
        <div v-else class="all-contnent">
          <div class="main-content">
            <CardView class="card" title="已选权益人">
              <div class="userList">
                <div
                  v-for="item in pageState.data?.detailList"
                  :key="item?.id"
                  class="userInfo"
                >
                  <div class="content">
                    <img :src="getImageSrc('woman.png')" />
                    <div class="patindUserInfo">
                      <span class="title">{{ item?.patientName }}</span>
                      <span
                        >{{ item?.patientAge ?? "-" }}岁
                        {{
                          item?.gender === 1
                            ? "男"
                            : item?.gender === 2
                              ? "女"
                              : "未知"
                        }}</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </CardView>
            <CardView class="card" title="预约信息">
              <template
                v-for="item in serviceInformation"
                :key="item.attributeName"
              >
                <a-row v-if="item?.extend" :gutter="[0, 14]">
                  <a-col
                    v-if="pageState.data?.firstVisitType"
                    :span="item.col?.name"
                    style="color: #86909c"
                    >{{ item.title }}</a-col
                  >
                  <a-col
                    v-if="pageState.data?.firstVisitType"
                    :span="item?.col.value"
                  >
                    {{ format(item) }}
                  </a-col>
                </a-row>

                <a-row v-else :gutter="[0, 14]">
                  <a-col :span="item.col?.name" style="color: #86909c">{{
                    item.title
                  }}</a-col>
                  <a-col :span="item?.col.value">
                    {{ format(item) }}
                  </a-col>
                </a-row>
              </template>
            </CardView>
            <div v-for="item in pageState.data?.detailList" :key="item">
              <div class="iconView">
                <img :src="getImageSrc('quanyi.png')" class="" />
                <span>权益人：{{ item?.patientName }}</span>
              </div>
              <!-- 基本健康信息 -->
              <cardView
                v-if="
                  item?.hasCurrentHistory !== null ||
                  item?.hasPastDisease !== null ||
                  item?.hasAllergyHistory !== null
                "
                title="基本健康信息"
                :card-style="{ paddingBottom: '20px' }"
              >
                <a-row
                  v-for="health in BaseHealthInfo"
                  :key="health.attributeName"
                  :gutter="[0, 14]"
                >
                  <a-col :span="health.col?.name" style="color: #86909c">{{
                    health.title
                  }}</a-col>
                  <a-col :span="health?.col.value">
                    {{
                      item?.[health.attributeName1] === true ? "有" : "无"
                    }}&emsp;
                    {{ item?.[health.attributeName] ?? "-" }}
                  </a-col>
                </a-row>
              </cardView>
              <CardView
                class="card"
                title="首诊描述"
                :card-style="{ paddingBottom: '20px' }"
              >
                <div>
                  {{ pageState.data?.detailList?.at(0)?.description ?? "-" }}
                </div>
              </CardView>
              <CardView
                v-if="differentTypes(item?.fileList2)?.reportList?.length"
                class="card"
                title="报告"
                :card-style="{ paddingBottom: '20px' }"
              >
                <div class="imgList">
                  <template
                    v-for="file in differentTypes(item?.fileList2)?.reportList"
                    :key="file?.id"
                  >
                    <a-image
                      v-if="imageExtensions?.includes(file?.fileType)"
                      width="64px"
                      height="64px"
                      :src="file?.threeFilePath"
                    />
                    <img
                      v-if="file?.fileType === 'pdf'"
                      :src="pdfPng"
                      @click="handlePerviewPdf(file.threeFilePath)"
                    />
                  </template>
                </div>
              </CardView>
              <CardView
                v-if="differentTypes(item?.fileList2)?.letterList?.length"
                class="card"
                title="知情同意书"
                :card-style="{ paddingBottom: '20px' }"
              >
                <div class="imgList">
                  <template
                    v-for="file in differentTypes(item?.fileList2)?.letterList"
                    :key="file?.id"
                  >
                    <a-image
                      v-if="imageExtensions?.includes(file?.fileType)"
                      width="64px"
                      height="64px"
                      :src="file?.threeFilePath"
                    />
                    <img
                      v-if="file?.fileType === 'pdf'"
                      :src="pdfPng"
                      @click="handlePerviewPdf(file.threeFilePath)"
                    />
                  </template>
                </div>
              </CardView>
            </div>
            <CardView title="订单信息">
              <a-row
                v-for="item in OrderInfo"
                :key="item.attributeName"
                :gutter="[0, 14]"
              >
                <a-col :span="item.col?.name" style="color: #86909c">{{
                  item.title
                }}</a-col>
                <a-col :span="item?.col.value">
                  {{ format(item) }}
                </a-col>
              </a-row>
            </CardView>
          </div>
          <!-- <div class="statusView">
            <div class="title">状态</div>
            <a-timeline :style="{ marginRight: '40px' }">
              <a-timeline-item label="申请">
                <template #dot>
                  <IconCheck
                    :style="{
                      fontSize: '12px',
                      padding: '2px',
                      boxSizing: 'border-box',
                      borderRadius: '50%',
                      backgroundColor: 'var(--color-primary-light-1)',
                    }"
                  />
                </template>
                申请
                <template #label>
                  <div class="tipText">
                    <div class="text">已接收您的申请</div>
                    <div class="time">2024-01-23 14:00:12</div>
                  </div>
                </template>
              </a-timeline-item>
              <a-timeline-item>
                <template #dot>
                  <IconCheck
                    :style="{
                      fontSize: '12px',
                      padding: '2px',
                      boxSizing: 'border-box',
                      borderRadius: '50%',
                      backgroundColor: 'var(--color-primary-light-1)',
                    }"
                  />
                </template>
                受理
                <template #label>
                  <div class="tipText">
                    <div class="text">
                      已为您的申请安排服务人员：黄丹颜 19821921922
                    </div>
                    <div class="time">2024-01-23 14:00:12</div>
                  </div>
                </template>
              </a-timeline-item>
              <a-timeline-item
                >服务中
                <template #label>
                  <div class="tipText">
                    <div class="text">服务人员还在为您服务中</div>
                    <div class="time">2024-01-23 14:00:12</div>
                  </div>
                </template>
              </a-timeline-item>
              <a-timeline-item>完成</a-timeline-item>
              <a-timeline-item>评价</a-timeline-item>
            </a-timeline>
          </div> -->
        </div>
      </div>
    </div>
    <!-- 单独pdf预览的组件 -->
    <a-modal
      v-model:visible="pdfVisible"
      title="pdf预览"
      width="800px"
      :mask-closable="false"
      :footer="false"
      unmount-on-close
      @cancel="pdfVisible = false"
    >
      <div style="height: 80vh; overflow: auto">
        <vue-pdf-embed ref="pdfRef" :source="pdfSrc" />
      </div>
    </a-modal>
  </a-spin>
</template>

<script setup lang="ts">
import { reactive, watchEffect, onMounted, ref } from "vue";
import CardView from "@/components/global-component/formContent/cardView.vue";
import { getImageSrc } from "@/utils";
import * as apis from "@/batchApi";
import { to } from "await-to-js";
import { useAppStore } from "@/store";
import { imageExtensions } from "@/constant";
import VuePdfEmbed from "vue-pdf-embed";
import pdfPng from "@/assets/images/<EMAIL>";
import { serviceInformation, OrderInfo, BaseHealthInfo } from "./config";

type IRecord = apis.QueryServiceReservationListUsingPOST_1ServiceReservationDto;

interface Props {
  loading: boolean;
  data: apis.GetServiceReservationByIdUsingPOSTServiceReservationDto | any;
  activeKey: string | number;
  historyList: IRecord[];
}
const appStore = useAppStore();
const props = defineProps(["id"]);
const pdfVisible = ref(false);
const pdfSrc = ref();
const pageState = reactive<Props>({
  loading: false,
  data: {},
  activeKey: props?.id,
  historyList: [],
});

watchEffect(() => {
  console.log("🤗", props);
});

const differentTypes = (list: any[]) => {
  if (!list?.length) return;
  // 1 报告 2 知情同意书
  const reportList = list?.filter(item => item?.type === "1");
  const letterList = list?.filter(item => item?.type === "2");
  return { reportList, letterList };
};

const handlePerviewPdf = (url: string) => {
  pdfSrc.value = url;
  pdfVisible.value = true;
};

const format = (item: any) => {
  const { attributeName, enumMap } = item || {};
  if (!attributeName) return "-";
  const result = pageState.data?.[attributeName];
  if (enumMap) {
    return enumMap[result] ?? "-";
  }
  return result ?? "-";
};

const handleToggleRecord = (record: any) => {
  pageState.activeKey = record.id;
  getData(record?.id);
};

const getList = async () => {
  pageState.loading = true;
  const [err, res] = await to(
    apis.postBdManageApiServiceReservationQueryServiceReservationList({
      patientId: appStore.patientInfo.patientId as unknown as number,
      pageNum: 1,
      pageSize: 10,
    }),
  );
  pageState.loading = false;
  if (err) {
    throw err;
  }
  if (res.code === "1") {
    const current = res?.data?.records?.[0];
    if (!props.id) {
      pageState.activeKey = current?.id as unknown as string;
    }

    pageState.historyList = res?.data?.records as IRecord[];
  }
};

const getData = async (id: string) => {
  pageState.loading = true;
  const [err, res] = await to(
    apis.postBdManageApiServiceReservationGetServiceReservationById({ id }),
  );
  pageState.loading = false;
  if (err) {
    throw err;
  }
  if (res.code === "1") {
    pageState.data = res?.data;
  }
};

onMounted(async () => {
  await getList();
  await getData(pageState.activeKey as string);
});
</script>

<style lang="scss" scoped>
.reservation-list {
  display: flex;
  align-items: start;
  justify-content: space-between;
  gap: var(--spacing-4);
  height: 100%;
  > .list {
    display: flex;
    flex-direction: column;
    height: 100%;
    border-radius: var(--border-radius-medium);
    background-color: white;
    > h2 {
      padding: var(--spacing-7) var(--spacing-7) 0 var(--spacing-7);
      color: var(--text-span-color);
      font-size: var(--font-size-body-3);
      margin: 0;
    }
    .listEmpty {
      width: 20vw;
      height: 100%;
      display: flex;
      align-items: center;
      flex: 1;
    }
    > .list-item-container {
      flex: 1;
      overflow-y: auto;
      margin-top: var(--spacing-9);
      padding: 0 var(--spacing-7) var(--spacing-7) var(--spacing-7);
      > .list-item {
        // width: 350px;
        width: 20vw;
        padding: var(--spacing-7);
        border: 1px solid var(--evaluate-border-color);
        border-radius: var(--border-radius-medium);
        &:not(:last-child) {
          margin-bottom: var(--spacing-7);
        }
        &.active {
          border-color: var(--evaluation-bg-color);
          background-color: var(--backlog-item-color);
        }
        > div {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;
          > h2 {
            color: var(--text-span-color);
            font-size: var(--font-size-body-3);
            margin: 0;
          }
          > span {
            color: var(--text-sub-color);
            font-size: var(--font-size-body-1);
          }
          > .red-circle {
            top: 5px;
            left: 0px;
            position: absolute;
            width: 5px;
            height: 5px;
            background-color: red;
            border-radius: 50%;
          }
        }
        > p {
          margin: 0;
          margin-top: var(--spacing-4);
          > span {
            font-size: var(--font-size-body-3);
            &:first-child {
              color: var(--text-sub-color);
            }
            &:last-child {
              margin-left: var(--spacing-2);
            }
          }
        }
      }
    }
  }

  .iconView {
    height: 30px;
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #0052d9;
    background: #f9fbff;
    padding: 0 20px;
    margin-bottom: 8px;
    margin-top: 20px;
    > img {
      width: 12px;
      height: 15px;
      margin-right: 10px;
    }
  }

  .imgList {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;

    img {
      width: 64px;
      height: 64px;
      border-radius: 8px;
    }
  }
  > .content {
    flex: 1;
    height: 100%;
    width: 100%;
    background-color: #f9fbff;
    // overflow: auto;

    > h2 {
      color: var(--text-span-color);
      font-size: 18px;
      font-weight: bold;
      background: #fff;
      padding: 20px 16px;
      padding-bottom: 0px;
      margin: 0;
    }

    .emptyView {
      width: 100%;
      height: 100%;
      height: calc(100% - 50px);
      background: #fff;
      display: flex;
      align-items: center;
    }

    .all-contnent {
      display: grid;
      height: calc(100% - 50px);
      // grid-template-columns: 1fr 480px;
      grid-template-columns: 1fr;

      > .main-content {
        width: 100%;
        height: 100%;
        border-radius: var(--border-radius-medium);
        background-color: #f9fbff;
        border-right: 1px solid var(--border-color);
        overflow: auto;

        > .card {
          padding: 16px !important;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .userList {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
          }

          .userInfo {
            display: flex;

            .content {
              width: 176px;
              padding: 16px;
              border-radius: 8px;
              padding-top: 8px;
              display: flex;
              align-items: center;
              background: rgba(0, 82, 217, 0.06);
              margin-right: 10px;
              > img {
                width: 48px;
                height: 48px;
                margin-right: 12px;
              }

              .patindUserInfo {
                display: flex;
                flex-direction: column;
                font-weight: normal;
                line-height: 14px;
                color: #86909c;

                .title {
                  font-size: 16px;
                  font-weight: bold;
                  color: #000000;
                  margin-bottom: 8px;
                }
              }
            }
          }

          // .imgList {
          //   display: flex;
          //   flex-wrap: wrap;
          //   align-items: center;
          //   gap: 16px;
          //   margin-bottom: 20px;

          //   img {
          //     width: 64px;
          //     height: 64px;
          //     border-radius: 8px;
          //   }
          // }
        }
      }

      .statusView {
        background: #fff;
        padding: 0 40px;

        .title {
          color: #767676;
          font-size: 14px;
          margin-bottom: 20px;
        }

        .tipText {
          background: #f9fbff;
          border-radius: 4px;
          font-size: 12px;
          padding: 12px;
          margin-bottom: 8px;

          .text {
            color: #1d2129;
          }
          .time {
            color: #d8d8d8;
            margin-top: 4px;
          }
        }

        :deep(.arco-timeline-item-content) {
          font-size: 14px;
          font-weight: bold;
        }
      }
    }
  }
}
</style>
