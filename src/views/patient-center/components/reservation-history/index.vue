<!--
 * @Description: 预约服务历史
 * @Author: yangrongxin
 * @Date: 2024-12-18 13:43:40
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-19 14:07:49
-->
<template>
  <div class="reservation-history-container">
    <div class="header">
      <h2>预约服务</h2>
      <div
        v-if="itemList.length !== 0"
        class="more cursor-pointer"
        @click="handleMore()"
      >
        <span>更多</span>
        <icon-right />
      </div>
    </div>
    <div class="content">
      <div
        v-for="(_item, _index) in itemList"
        :key="_index"
        class="content-item"
        @click="handleMore(_item.id)"
      >
        <div class="title">
          <span><label /> {{ _item?.serviceName }} </span>
          <span class="status"> {{ _item?.statusName }} </span>
        </div>
        <div class="info">
          <span>预约日期：</span>
          <span>{{ _item.xCreateTime ?? "-" }}</span>
        </div>
        <!-- <div class="info">
          <span>渠道</span>
          <span>{{ _item.channelName ?? "-" }}</span>
        </div> -->
        <div class="info">
          <span>支付类型</span>
          <span>{{
            _item?.payType === "0"
              ? "权益核销"
              : _item?.payType === "1"
                ? "患者支付"
                : "-"
          }}</span>
        </div>
        <!-- 引入随访之后 取消列表上 病情描述的展示 -->
        <!-- <div class="info">
          <span>病情描述：</span>
          <span>{{ _item.desc }}</span>
        </div> -->
      </div>
      <div v-if="itemList.length === 0" class="content-empty">
        <a-empty description="暂无预约服务历史" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePatientCenterStore, useAppStore } from "@/store";
import { markRaw, ref } from "vue";
import * as apis from "@/batchApi";
import { to } from "await-to-js";
import reservationList from "./list.vue";

type IRecord = apis.QueryServiceReservationListUsingPOST_1ServiceReservationDto;

const appStore = useAppStore();
const patientCenterStore = usePatientCenterStore();

const itemList = ref<IRecord[]>([]);
const loading = ref<boolean>(false);

const getList = async () => {
  loading.value = true;
  const [err, res] = await to(
    apis.postBdManageApiServiceReservationQueryServiceReservationList({
      patientId: appStore.patientInfo.patientId as unknown as number,
      pageNum: 1,
      pageSize: 999,
    }),
  );
  loading.value = false;
  if (err) {
    throw err;
  }
  if (res.code === "1") {
    itemList.value = res?.data?.records as IRecord[];
  }
};

const handleMore = (_id?: string | number) => {
  patientCenterStore.changeSecondaryMenu(true, "预约服务历史", [
    {
      name: "预约服务记录",
      component: markRaw(reservationList),
      componentParams: {
        id: _id,
      },
    },
  ]);
};

getList();
</script>

<style lang="scss" scoped>
.reservation-history-container {
  display: flex;
  flex-direction: column;
  // width: 25vw;
  padding: var(--spacing-7) 0;
  border-radius: var(--border-radius-medium);
  background-color: white;
  > .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-7);
    > h2 {
      color: var(--text-span-color);
      margin: 0 !important;
      // font-size: var(--font-size-body-4);
      font-size: var(--font-size-body-3);
    }
    > .more {
      color: var(--text-gray-color);
      > span {
        margin-right: var(--spacing-2);
      }
    }
  }
  > .content {
    flex: 1;
    overflow-y: auto;
    margin-top: var(--spacing-7);
    padding: 0 var(--spacing-7);
    > .content-item {
      &:not(:last-child) {
        margin-bottom: var(--spacing-6);
      }
      padding: var(--spacing-7) 14px;
      border-radius: var(--border-radius-medium);
      border: 1px solid #e5e5e5;
      > .title {
        position: relative;
        display: flex;
        justify-content: space-between;
        color: #0052d9;
        margin: 0 !important;
        font-size: 14px;
        font-weight: bold;
        > span label {
          display: inline-block;
          width: 8px;
          height: 8px;
          background: #0052d9;
          border-radius: 50%;
          margin-right: 8px;
        }
        > .status {
          color: #ff9a2e;
        }
      }
      > div.info {
        display: grid;
        margin-top: var(--spacing-5);
        grid-template-columns: 72px 1fr;
        > span {
          &:first-child {
            color: var(--text-sub-color);
            font-size: var(--font-size-body-3);
          }
          &:last-child {
            word-break: break-all;
            font-size: var(--font-size-body-3);
          }
        }
      }
      // >div.result {
      //   margin-top: var(--spacing-7);
      //   >div {
      //     >span {
      //       display: block;
      //       font-size: var(--font-size-body-3);
      //       &:first-child {
      //         color: var(--text-sub-color-two);
      //         margin-bottom: var(--spacing-2);
      //         background-color: #fafafa;
      //         padding-left: var(--spacing-4);
      //       }
      //       &:last-child {
      //         padding-left: var(--spacing-4);
      //       }
      //     }
      //   }
      // }
    }
    > .content-empty {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
