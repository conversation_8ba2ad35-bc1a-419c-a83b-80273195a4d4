<!--
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2024-10-22 14:56:14
 * @LastEditors: yangrong<PERSON>
 * @LastEditTime: 2024-10-22 16:39:26
-->
<template>
  <div>
    展示第一个页面 {{ props.name }}
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';

const props = defineProps([
  'name'
])

onMounted(() => {
  console.log('进入第一个页面', props)
})
</script>

<style scoped>

</style>