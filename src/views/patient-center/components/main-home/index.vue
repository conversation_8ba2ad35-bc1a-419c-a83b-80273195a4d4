<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-10-22 14:49:26
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 22:51:47
-->
<template>
  <!-- @click="showSecondaryMenu" -->
  <div class="main-home-container">
    <!-- 中医体质辨识信息 -->
    <tcm-info />
    <div class="page-two">
      <!-- 诊疗的信息 -->
      <diagnosisInfo />
      <reservation-history />
    </div>
    <!-- 评估历史的信息 -->
    <div class="page-three">
      <!-- 评估 -->
      <evaluation-history />
      <!-- 展示患者的随访的数据 -->
      <followup-history />
      <!-- <InsuranceHistory /> -->
      <!-- <a-empty> 敬请期待 </a-empty> -->
    </div>
    <!-- 待办的信息 -->
    <backlog />
  </div>
</template>

<script setup lang="ts">
import followupHistory from "../followup-history/index.vue";
import evaluationHistory from "../evaluation-history/index.vue";
import InsuranceHistory from "../insurance-history/index.vue";
import reservationHistory from "../reservation-history/index.vue";
import diagnosisInfo from "../diagnosis-info/index.vue";
import tcmInfo from "../tcm-info/index.vue";
import backlog from "../backlog/index.vue";
</script>

<style lang="scss" scoped>
.main-home-container {
  display: flex;
  gap: var(--spacing-4);
  height: 100%;
  .page-two {
    width: 28vw;
    // max-width: 540px;
    display: flex;
    flex-direction: column;
    > div.arco-empty {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: white;
      border-radius: var(--border-radius-medium);
    }
    > div {
      &:last-child {
        flex: 1;
      }
      overflow-y: hidden;
    }
  }
  .page-three {
    width: 25vw;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
    overflow-y: auto;
    > div {
      &:last-child {
        flex: 1;
      }
      overflow-y: hidden;
    }
    > div.arco-empty {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: white;
      border-radius: var(--border-radius-medium);
    }
  }
}
</style>
