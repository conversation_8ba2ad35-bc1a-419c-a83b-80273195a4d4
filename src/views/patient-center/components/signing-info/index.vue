<!--
 * @Description: 服务包签约信息展示
 * @Author: yangrong<PERSON>
 * @Date: 2025-07-15 19:16:16
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 22:29:06
-->
<template>
  <div class="package-list">
    <div
      v-for="item, index in packageList.slice(0, 2)"
      :key="index"
      class="package-item"
      @click="handlePackageInfo(item.channelServiceCode)"
    >
      <!-- 服务包的标题 -->
      <div class="title">
        <img :src="rightsIcon" alt="">
        <span>{{ item.packageName }}</span>
      </div>     
      <!-- 主要的服务包信息 -->
      <div class="info">
        <div class="info-item">
          <span>代理人：</span>
          <div>
            <img v-if="item.agentAvatar" :src="item.agentAvatar" alt="">
            <span v-if="item.agentName">
              {{ item.agentName }}
              （{{ item.agentPhone }}）
            </span>
            <span v-else>
              -
            </span>
          </div>
        </div>
        
        <div
          class="info-item"
        >
          <span>服务团队：</span>
          <div>
            <!-- 此处是头像 -->
            <img v-if="item.teamEquityPackage?.teamAvatar" :src="item.teamEquityPackage?.teamAvatar" alt="">
            <!-- 此处是团队信息 -->
            <span>{{ item.teamEquityPackage?.teamName || '-' }}</span>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="packageList.length > 2"
      class="package-item last"
      @click="handleShowPackage(packageList[0].channelServiceCode)"
    >
      <span>更多</span>
      <icon-right />
    </div>
    <div
      v-if="packageList.length === 0"
    >
      暂无信息
    </div>
  </div>
</template>

<script setup lang="ts">
import { h, onMounted, ref } from 'vue';
import { getBdManageApiHealthRecordsQueryHealthRecordsPackageList, QueryHealthRecordsPackageListUsingGETHealthRecordsPackageVo } from '@/batchApi';
import rightsIcon from '@/assets/images/patient/rights.png';
import { Drawer } from '@arco-design/web-vue';
import { useAppStore } from '@/store';
import content from './components/content.vue';

const appStore = useAppStore();

const {
  patientInfo
} = appStore

const packageList = ref<QueryHealthRecordsPackageListUsingGETHealthRecordsPackageVo[]>([])

// 获取患者的权益包信息
const handleRightsInfo = () => {
  getBdManageApiHealthRecordsQueryHealthRecordsPackageList({
    patientId: patientInfo.patientId as unknown as number
  }).then(_res => {
    if (
      Array.isArray(_res.data)
    ) {
      packageList.value = _res.data
    }
    console.log('_res', _res.data)
  })
}

// 点击展示对应服务包的信息
const handlePackageInfo = (_packageId: string | number | undefined) => {
  console.log('_packageId', _packageId)
  handleShowPackage(_packageId)
}

// 展示服务包情况
const handleShowPackage = (
  _packageId?: string | number | undefined
) => {
  const drawerReturn = Drawer.open({
    // 顶部内容自定义
    header: false,
    // 底部内容自定义
    footer: false,
    content: () => h(content, {
      drawerRef: drawerReturn,
      packageId: _packageId,
      packageList: packageList.value
    }),
    width: 768,
  });
}

onMounted(() => {
  handleRightsInfo();
})

</script>

<style scoped lang="scss">
.package-list {
  display: flex;
  column-gap: var(--spacing-4);
  >.package-item {
    cursor: pointer;
    padding: var(--spacing-5) var(--spacing-6);
    border-radius: var(--border-radius-medium);
    background-color: var(--package-title-bg-color);
    &.last {
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--primary-color);
      font-size: var(--font-size-body-3);
    }
    >.title {
      display: flex;
      align-items: center;
      >img {
        width: 16px;
        height: 16px;
        margin-right: var(--spacing-4);
      }
      >span {
        font-weight: bold;
      }
    }
    >.info {
      padding-left: var(--spacing-9);
      >.info-item {
        display: flex;
        align-items: center;
        >span {
          width: 80px;
          flex: none;
        }
        >div {
          display: flex;
          align-items: center;
          >img {
            width: 12px;
            height: 12px;
            margin-right: var(--spacing-2);
            border-radius: 50%;
          }
          >span {
            color: var(--text-span-color);
          }
        }
      }
    }
  }
}
</style>