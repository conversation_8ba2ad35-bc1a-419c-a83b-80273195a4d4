<template>
  <div class="signing-info-wrapper">
    <a-spin
      :loading="loadingContent"
    >
      <div
        v-if="signedInfo"
        class="signing-info-container"
      >
        <!-- 签约信息 -->
        <div class="signing-info">
          <h3>签约信息</h3>
          <!-- 展示患者的基本信息 -->
          <div class="content">
            <div class="header">
              <img :src="avatarPng" alt="">
            </div>
            <div class="info">
              <div>
                <h4>{{ signedInfo?.doctorName }}</h4>
                <div>
                  {{ signedInfo?.teamName }}
                </div>
              </div>
              <span>签约时间：{{ signedInfo?.signedTime }}</span>
            </div>
          </div>
        </div>
        <!-- 签约服务包 -->
        <div
          v-if="signedInfo?.rightsItemList?.length !== 0"
          class="signing-packages"
        >
          <h3>签约服务包</h3>
          <!-- 服务包的列表到第8个的时候 展示更多 -->
          <div class="packages-content">
            <package-item
              v-for="item, index in signedInfo?.rightsItemList?.slice(0, 7)"
              :key="index"
              :status="item.packageStatus"
              :initial="item.packageCode?.[0]"
              :name="item.packageName"
              :expire-time="item.expiredTime"
            />
            <package-item
              v-if="signedInfo?.rightsItemList && signedInfo?.rightsItemList?.length > 8"
              :status="0"
              initial="..."
              name="全部"
            >
              <template #sub-packages>
                <package-item
                  v-for="item, index in signedInfo?.rightsItemList"
                  :key="index"
                  :status="item.packageStatus"
                  :initial="item.packageCode?.[0]"
                  :name="item.packageName"
                  :expire-time="item.expiredTime"
                />
              </template>
            </package-item>
            <package-item
              v-if="signedInfo?.rightsItemList?.length === 8"
              :status="signedInfo?.rightsItemList?.[7].packageStatus"
              :initial="signedInfo?.rightsItemList?.[7].packageCode"
              :name="signedInfo?.rightsItemList?.[7].packageName"
              :expire-time="signedInfo?.rightsItemList?.[7].expiredTime"
            />
          </div>
        </div>
        <!-- 去续签的按钮 -->
        <!-- 07.11 因为存在其他第三方的包 所以这里不展示去续签的按钮了 -->
        <!-- <div
          v-if="signedInfo?.rightsItemList?.findIndex(_fi => _fi.packageStatus == 2) !== -1"
          class="signing-controls"
        >
          <a-button
            type="primary"
            @click="handleRenew"
          >
            去续约
          </a-button>
        </div> -->
      </div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import ManPng from '@/assets/images/man.png';
import WomanPng from '@/assets/images/woman.png';
import { onMounted, onUnmounted, ref, computed } from 'vue';
import useQuickAccessStore from '@/store/modules/quick-access';
import { listenerPatientSigned, removeSignedListener } from '@/utils/signed-listener';
import { postBdManageApiSignGetSignedTeamInfoByPatientId, postBdManageApiSignGetSignedTeamInfoByPatientIdResponse } from '@/batchApi';
import { useAppStore } from '@/store';
import packageItem from './package-item.vue';

const quickAccessStore = useQuickAccessStore();
const appStore = useAppStore()

const signedInfo = ref<postBdManageApiSignGetSignedTeamInfoByPatientIdResponse['data']>();
const loadingContent = ref(false)

const {
  idCard
} = defineProps<{
  idCard: string
}>()

const avatarPng = computed(() => {
  if (
    signedInfo.value?.doctorSex === 2
  ) {
    return WomanPng;
  }
  return ManPng;
})


// 去续签约
const handleRenew = () => {
  quickAccessStore.toggleSigningInterface(true, idCard);
}

const init = () => {
  loadingContent.value = true;
  postBdManageApiSignGetSignedTeamInfoByPatientId({
    patientId: appStore.patientCenterInfo.patientId as unknown as number
  }).then(_res => {
    if (
      _res.data
    ) {
      signedInfo.value =  {
        ..._res.data,
        rightsItemList: _res.data.rightsItemList?.filter(_fi => _fi)
      };
    }
    console.log('_res', _res)
    loadingContent.value = false;
  })
}

onMounted(() => {
  init()
  listenerPatientSigned((_patientInfo) => {
    console.log('_patientInfo', _patientInfo)
    if (
      _patientInfo
    ) {
      init();
    }
  })
})

onUnmounted(() => {
  removeSignedListener()
})


</script>

<style lang="scss" scoped>
.signing-info-wrapper {
  margin-right: 0;
  margin-left: auto;
}
.signing-info-container {
  display: flex;
  // column-gap: var(--spacing-7);
  border-left: 1px solid var(--border-gray-color);
  padding-left: var(--spacing-12);
  // 签约信息
  >.signing-info {
    margin-right: var(--spacing-15);
    >h3 {
      margin-top: 0;
      margin-bottom: var(--spacing-4);
      color: var(--text-gray-color);
      font-size: var(--font-size-body-3);
    }
    >.content {
      display: flex;
      align-items: center;
      >.header {
        width: 36px;
        height: 36px;
        margin-right: var(--spacing-4);
        border-radius: 50%;
        background-color: #D8D8D8;
        >img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      >.info {
        flex: 1;
        >div {
          display: flex;
          
          align-items: center;
          >h4 {
            margin-top: 0;
            margin-bottom: 0;
            margin-right: 4px;
            color: var(--text-span-color);
          }
          >div {
            color: white;
            padding: var(--spacing-1) var(--spacing-4);
            font-size: var(--font-size-body-1);
            background: linear-gradient(180deg, #FFCF8B 0%, #FF9A2E 100%);
            border-radius: var(--border-radius-large) var(--border-radius-large) var(--border-radius-large) 0px;
            // border-radius: var(--border-radius-large);
          }
        }
        >span {
          color: var(--text-gray-color);
          font-size: var(--font-size-body-1);
        }
      }
    }
  }
  // 签约服务包
  >.signing-packages {
    max-width: 260px;
    >h3 {
      margin-top: 0;
      margin-bottom: var(--spacing-4);
      color: var(--text-gray-color);
      font-size: var(--font-size-body-3);
    }
    >.packages-content {
      // display: grid;
      // grid-template-columns: repeat(4, 1fr);
      display: flex;
      flex-wrap: wrap;
      row-gap: 6px;
      column-gap: 16px;
    }
  }
  // 签约的模块可以进行的操作
  >.signing-controls {
    display: flex;
    align-items: center;
    margin-left: var(--spacing-22);
  }
}
</style>