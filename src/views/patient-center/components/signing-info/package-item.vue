<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2025-04-01 15:16:26
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-04-25 09:56:29
-->
<template>
  <!-- title="Title" -->
  <a-popover>
    <div
      class="package-item-container"
    >
      <!-- 展示圆点的签约状态 内容显示疾病首字母的缩写 -->
      <div
        class="status"
        :class="{
          'normal': status === 1,
          'expire': status === 2,
          'all': status === 0
        }"
      >
        <span>
          <!-- {{ initial ?? 'T' }} -->
          {{ name[0] ?? '高' }}
        </span>
      </div>
      <!-- 展示服务包的名称 -->
      <div class="name">
        {{ name ?? '糖尿病' }}
      </div>
    </div>
    <template #content>
      <div v-if="name !== '全部'" class="expire-content">
        <span>到期时间：</span>
        <span>{{ expireTime ?? '' }}</span>
      </div>
      <div v-else class="all-content">
        <slot name="sub-packages" />
      </div>
    </template>
  </a-popover>
</template>

<script setup lang="ts">
const {
  initial,
  status,
  name,
  expireTime,
} = defineProps([
  'initial',
  'status',
  'name',
  'expireTime',
])

</script>

<style lang="scss" scoped>
.package-item-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  >.status {
    width: 20px;
    height: 20px;
    color: white;
    border-radius: 50%; 
    margin-right: var(--spacing-2);
    text-align: center;
    &.normal {
      background-color: var(--package-normal-bg-color);
    }
    &.expire {
      background-color: var(--package-expire-bg-color);
    }
    &.all {
      position: relative;
      background-color: var(--border-gray-color);
      >span {
        position: absolute;
        left: 4.5px;
        top: -5px;
      }
    }
    >span {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      margin-top: 1px;
    }
  }
  >.name {
    color: var(--text-span-color);
    font-size: var(--font-size-body-3);
  }
}

// 到期时间的展示
.expire-content {
  >span {
    &:nth-of-type(1) {
      color: var(--text-gray-color);
    }
    &:nth-of-type(2) {
      color: black;
    }
  }
}

.all-content {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  column-gap: 6px;
  row-gap: 16px;
}
</style>