<!--
 * @Description: 标题的内容
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-07-16 09:36:45
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-25 10:34:42
-->
<template>
  <a-space
      direction="vertical"
    size="large"
    class="patient-center-container"
  >
    <a-tabs
      :active-key="activeKey"
      @change="handleTabChange"
    >
      <!-- 关闭的按钮 -->
      <template #extra>
        <a-button
          type="text"
          @click="handleClose"
        >
          <template #icon>
            <icon-close />
          </template>
        </a-button>
      </template>
      <a-tab-pane
        v-for="item in packageList"
        :key="item.channelServiceCode"
        :title="item.packageName"
      >
        <!-- 权益项目 -->
        <div class="rights-project-container">
          <!-- 权益项目 -->
          <div class="rights-project">
            <h2>
              权益项目
              <sub>所属服务包：{{ item.channelServiceCode }}</sub>
            </h2>
            <div>
              <span>生效时间：{{ dayjs(item.effectiveTime).format("YYYY-MM-DD") }}</span>
              <span>到期时间：{{ dayjs(item.expirationTime).format("YYYY-MM-DD") }}</span>
            </div>
          </div>
          <!-- 展示每一个权益的细项 -->
          <div class="rights-items">
            <div class="info-table">
              <div class="title">
                <span>权益名称</span>
                <span>可用次数</span>
              </div>  
              <div
                v-for="right, index in item.rightsList?.slice(0, item.rightsList.length / 2)"
                :key="index"
                class="info"
              >
                <span>{{ right.rightsName }}</span>
                <span>{{ right.serviceCount }}次</span>
              </div>
            </div>
            <div class="info-table">
              <div class="title">
                <span>权益名称</span>
                <span>可用次数</span>
              </div>              
              <div
                v-for="right, index in item.rightsList?.slice(item.rightsList.length / 2, )"
                :key="index"
                class="info"
              >
                <span>{{ right.rightsName }}</span>
                <span>{{ right.serviceCount }}次</span>
              </div>
            </div>
          </div>
        </div>
        <hr class="split" />
        <service-team
          title="服务团队"
          :form="{ userList: item.teamEquityPackage?.userList }"
          :split-number="6"
          view
        />
        <hr class="split" />
        <service-team
          title="特约专家团队"
          :form="{ userList: item.teamEquityPackage?.userList }"
          :split-number="6"
          professor
          view
        />
        <hr class="split" />
        <service-team
          title="代理人"
          :split-number="6"
          :form="{ userList: [
            {
              name: item.agentName,
              phone: item.agentPhone,
              avatar: item.agentAvatar,
              isServiceTeam: 1
            }
          ].filter(_fi => _fi.name) }"
          view
        >
            <template #item="{ record }">
              <div class="agent-container">
                <img :src="record.avatar || avatarPng" alt="">
                <div class="agent-info">
                  <h3>{{ record.name }}</h3>
                  <!-- @ts-expect-error 新增手机号 -->
                  <span>{{ record.phone }}</span>
                </div>
              </div>
            </template>
        </service-team>
      </a-tab-pane>
    </a-tabs>
  </a-space>
</template>

<script setup lang="ts">
import { QueryHealthRecordsPackageListUsingGETHealthRecordsPackageVo } from '@/batchApi';
import { DrawerReturn } from '@arco-design/web-vue';
import { ref, watch } from 'vue';
import serviceTeam from '@/views/system/package-team-manage/components/service-team.vue';
import avatarPng from '@/assets/images/avatar.png'
import dayjs from 'dayjs';

const {
  drawerRef,
  packageList,
  packageId = ""
} = defineProps<{
  drawerRef: DrawerReturn,
  packageList: QueryHealthRecordsPackageListUsingGETHealthRecordsPackageVo[],
  packageId: number | string | undefined,
}>();

const activeKey = ref<string | number>(packageId);

watch(() => packageId, () => {
  console.log('packageId', packageId)
  if (
    packageId
  ) {
    activeKey.value = packageId;
  }
  console.log('activeKey.value', activeKey.value)
})
// 点击关闭抽屉
const handleClose = () => {
  drawerRef.close();
}

const handleTabChange = (_tab: string | number) => {
  console.log('_tab', _tab)
  activeKey.value = _tab;
  // TODO: 根据值渲染下方的内容
}

</script>

<style scoped lang="scss">
.patient-center-container {
  width: 100%;
}

.rights-project-container {
  // 权益项目的样式
  .rights-project {
    display: flex;
    align-items: end;
    justify-content: space-between;
    >h2 {
      color: #000;
      font-size: var(--font-size-title-1);
      margin-top: 0;
      margin-bottom: 0;
      >sub {
        margin-left: 12px;
      }
    }
    >div {
      display: flex;
      align-items: center;
      column-gap: var(--spacing-7);
      >span {
        color: var(--text-expired-time-text-color);
        font-size: var(--font-size-body-3);
      }
    }
  }
  // 展示权益的名称
  .rights-items {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    column-gap: var(--spacing-11);
    margin-top: var(--spacing-8);
    margin-bottom: var(--spacing-8);
    >.info-table {
      >.title {
        display: grid;
        grid-template-columns: repeat(2, minmax(0, 1fr));        
        background-color: var(--gray-bg-color);
        >span {
          padding: var(--spacing-5) var(--spacing-9);
          color: var(--text-color);
          font-weight: bold;
        }
      }
      >.info {
        display: grid;
        grid-template-columns: repeat(2, minmax(0, 1fr));
        border-bottom: 1px solid var(--gray-bg-color);
        >span {
          padding: var(--spacing-5) var(--spacing-9);
        }
      }
    }
  }
}
.split {
  border: none;
  height: 12px;
  background-color: var(--backlog-item-color);
}

// 设置代理人的样式
.agent-container {
  display: flex;
  align-items: center;
  border-radius: var(--border-radius-large);
  background-color: var(--item-hit-bg-color);
  padding: var(--spacing-6);
  >img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: var(--spacing-6);
  }
  >.agent-info {
    >h3 {
      margin-top: 0;
      margin-bottom: var(--spacing-3);
      font-size: var(--font-size-body-3);
    }
    >span {
      color: var(--text-gray-deep-color);
      font-size: var(--font-size-body-1);
    }
  }
}
</style>