<template>
  <div class="diagnosis-info">
    <a-spin :loading="loading" style="width: 100%; height: 100%;">
      <PageContainer
        title="诊疗信息"
        :title-font-size="14"
      >
        <template #titleExtra>
          <div style="cursor: pointer; font-size: 14px; color: #767676;" @click="showSecondaryMenu">
            更多
            <icon-right />
          </div>
        </template>
        <!-- 诊疗信息主要元素的内容 -->
        <div
          v-if="Array.isArray(list) && list.length > 0"
          class="main"
        >
          <div v-for="item in list" :key="item.diagnoseId"  class="item" @click="showSecondaryMenu(item)">
            <div class="up">
              <div 
                class="tag"
                :style="{ 
                  backgroundColor: config.tag[item.type].bgColor, 
                  color: config.tag[item.type].color 
                }"
              >
                {{ config.tag[item.type].label }}
              </div>
              <div class="title">{{ `${item.date} | ${item.name}` }}</div>
            </div>
            <div class="down">
              <div class="left">{{ item.organ }}</div>
              <div 
                class="right" 
                :style="{ 
                  backgroundColor: config.uploadFrom?.[item.fromType]?.bgColor ?? '', 
                  color: config.uploadFrom?.[item.fromType]?.color ?? '',
                  border: config.uploadFrom?.[item.fromType]?.border ?? '',
                }"
              >
                {{ config.uploadFrom[item.fromType].label }}
              </div>
            </div>
          </div>
        </div>
        <div
          v-else
          class="empty-content"
        >
          <a-empty
          >
            暂无数据
          </a-empty>
        </div>
      </PageContainer>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { markRaw, onMounted, ref, computed, watch } from 'vue';
import { usePatientCenterStore } from '@/store';
import { usePatientStoreWidthOut } from '@/store/modules/patient';
import { EReportType, EStorage } from "@/types/enums";
import useAppStore from '@/store/modules/app';
import PageContainer from '@/components/page-container/index.vue';
import { ERefreshType, listenerPatientInfoChange, setPatientInfoChangeEmitter } from '@/utils/patient-listener';
import inspectionReport from '../inspectionReport/index.vue';

const patientCenterStore = usePatientCenterStore();
const useDiagnosisInfoStore = usePatientStoreWidthOut()
const appStore = useAppStore()

const isUpdate = computed(() => {
  return useDiagnosisInfoStore.updateReport;
})

const loading = ref(false);

const config = ref<{
  tag: { [key: number]: any };
  uploadFrom: { [key: number]: any };
}>({
  tag: {
    1: {
      label: '检验',
      bgColor: 'rgba(0, 82, 217, 0.15)',
      color: '#0052D9',
      econdaryPageSequence: 3,
    },
    2: {
      label: '检查',
      bgColor: 'rgba(20, 188, 183, 0.15)',
      color: '#108EA7',
      econdaryPageSequence: 2,
    },
    3: {
      label: '病历',
      bgColor: 'rgba(217, 22, 0, 0.15)',
      color: '#D94500',
      econdaryPageSequence: 1,
    },
    4: {
      label: '体检',
      bgColor: 'rgba(14, 205, 45, 0.15)',
      color: '#10AD2A',
      econdaryPageSequence: 0,
    },
    5: {
      label: '健康建议书',
      bgColor: 'rgba(22, 168, 103, 0.15)',
      color: '#16A867',
      econdaryPageSequence: 4,
    },
  },
  uploadFrom: {
    1: {
      label: '自动获取',
      bgColor: 'rgba(0, 82, 217, 0.1)',
      color: '#0052D9',
      border: '1px solid #0052D9'
    },
    2: {
      label: '主动上传',
      bgColor: 'rgba(0, 82, 217, 0.1)',
      color: '#0052D9',
      border: '1px solid #0052D9'
    }
  },

})

const list = ref<{
  diagnoseId: string;
  type: number;
  date: string;
  name: string;
  organ: string;
  fromType: number;
}[]>([])

const showSecondaryMenu = async (record: { type: any; }) => {

  loading.value = true
  const payload = {
    patientId: (appStore.patientInfo as any).patientId,
  }
  const res = await useDiagnosisInfoStore.patientDiagnoseData(payload)
  let defaultKey =  0 ;
  if(!['', undefined, null].includes((record as any))) {
    defaultKey = config.value.tag?.[record?.type]?.econdaryPageSequence ?? 0 ;
    sessionStorage.setItem(EStorage.DIAGNOSIS_INIT, '1')
  }

  loading.value = false
  patientCenterStore.changeSecondaryMenu(
    true, 
    '诊疗信息', 
    [
      {
        name: res ? `体检报告(${res?.examCount ?? 0})` : '体检报告', 
        component: markRaw(inspectionReport),
        componentParams: {
          type: EReportType.examCount,
          defaultViewRercord: record,
        }
      },
      {
        name: res ? `病历(${res?.caseCount ?? 0})` : '病历', 
        component: markRaw(inspectionReport),
        componentParams: {
          type: EReportType.caseCount,
          defaultViewRercord: record,
        }
      },
      {
        name: res ? `检查报告(${res?.inspectionCount ?? 0})` : '检查报告', 
        component: markRaw(inspectionReport),
        componentParams: {
          type:  EReportType.inspectionCount,
          defaultViewRercord: record,
        }
      },
      {
        name: res ? `检验报告(${res?.surveyCount ?? 0})` : '检验报告', 
        component: markRaw(inspectionReport),
        componentParams: {
          type: EReportType.surveyCount,
          defaultViewRercord: record,
        }
      },
      {
        name: res ? `健康建议书(${res?.proposalCount ?? 0})` : '检验报告', 
        component: markRaw(inspectionReport),
        componentParams: {
          type: EReportType.proposalCount,
          defaultViewRercord: record,
        }
      }
    ],
    defaultKey,
  );
}

const fetchData = async () => {
  loading.value = true
  const payload = {
    patientId: (appStore.patientInfo as any).patientId,
    pageNum: 1,
    pageSize: 15,
  }
  const res = await useDiagnosisInfoStore.patientDiagnoseList(payload)
  if(res) {
    list.value = res.content
  }
  loading.value = false
}

watch(() => isUpdate.value, () => {
  fetchData()
})


defineExpose({
  fetchData,
})

onMounted(() => {
  fetchData()
  // 获取数据的更新状态
  listenerPatientInfoChange((
    refreshPatientInfo
  ) => {
    if (
      refreshPatientInfo[ERefreshType.MedicalTreatmentInformation] === true
    ) {
      fetchData()
        .then(() => {
          setPatientInfoChangeEmitter(ERefreshType.MedicalTreatmentInformation, false)
        })
    }
  })
})

</script>

<style lang="scss" scoped>

.diagnosis-info {
  width: 28vw;
  // max-width: 540px;
  height: 38vh;
  user-select: none;
  margin-bottom: var(--spacing-4);
  // 设置默认的wrapper的高度
  .pageContentView {
    height: 100%;
  }
}

.main {
  height: 100%;
  overflow: auto;
  // padding: 0 16px;

  .item {
    width: 100%;
    border: 1px solid #E5E5E5;
    border-radius: 4px;
    padding: 12px;
    cursor: pointer;

    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .up {
      margin-bottom: 10px;
      display: flex;
      align-items: center;

      .tag {
        min-width: 40px;
        padding: 2px 8px;
        margin-right: 8px;
        border-radius: 4px;
        font-size: 12px;
        line-height: 12px;
        font-weight: normal;
        height: 18px;
        vertical-align: middle;
      }

      .title {
        font-size: 14px;
        font-weight: bold;
        line-height: 20px;
        color: #3D3D3D;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }

    .down {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .left {
        color: #767676;
        font-size: 12px;
        max-width: calc(100% - 88px);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .right {
        font-size: var(--font-size-small);
        padding: 0px var(--size-1);
        // min-width: 66px;
      }
    }
  }
}
.empty-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>