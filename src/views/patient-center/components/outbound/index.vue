<!--
* @description 智能外呼
* @fileName index.vue
* <AUTHOR>
* @date 2025/07/09 20:29:12
!-->
<script setup lang="ts">
import { ref } from "vue";
import { getImageSrc } from "@/utils";
import { Message } from "@arco-design/web-vue";

type IProps = {
  name: string;
  phone: string;
};
const visible = defineModel<boolean>("visible", { required: true });
const props = defineProps<IProps>();
const str = ref(props.phone);

const handleEditEnd = () => {
  const mobileReg = /^1[3-9]\d{9}$/;
  if (!mobileReg.test(str.value)) {
    str.value = props.phone;
    return Message.error("请输入正确的手机号码");
  }
};
const handleCallBack = async () => {
  // todo
  console.log("🚶‍♀️", 122);
  visible.value = false;
};
</script>

<template>
  <a-modal
    v-model:visible="visible"
    title="智能外呼"
    :mask-closable="false"
    hide-cancel
    closable
    modal-class="outboundModal"
    @cancel="visible = false"
  >
    <div class="tips">请确认是否拨打此号码？</div>
    <div class="contentView">
      <div class="content">
        <span>{{ name }}</span>
        <a-divider direction="vertical" />
        <a-typography-paragraph
          v-model:edit-text="str"
          editable
          @edit-end="handleEditEnd"
        >
          {{ str }}
        </a-typography-paragraph>
      </div>
    </div>
    <template #footer>
      <div class="footerView">
        <a-button class="btnSty" type="primary" @click="handleCallBack">
          <img class="iconSty" :src="getImageSrc('call.png')" />
          确认拨打</a-button
        >
      </div>
    </template>
  </a-modal>
</template>

<style lang="scss" scoped>
:deep(.arco-typography-edit-content) {
  margin-bottom: 0 !important;
}
:deep(.arco-typography) {
  margin-bottom: 0 !important;
}
.outboundModal {
  .tips {
    display: flex;
    justify-content: center;
    color: #3d3d3d;
    font-size: 14px;
  }

  .contentView {
    display: flex;
    justify-content: center;

    margin-top: 20px;
    .content {
      display: flex;
      align-items: center;
      background: #f2f3f5;
      padding: 20px 24px;
      border-radius: 8px;
      font-size: 20px;
      font-weight: bold;
    }
  }

  .footerView {
    display: flex;
    justify-content: center;
    border-top: none;

    .btnSty {
      .iconSty {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
    }
  }
}
</style>
