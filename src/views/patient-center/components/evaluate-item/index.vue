<!--
 * @Description: 展示评估表单的内容 以及提交数据
 * @Author: yangrong<PERSON>
 * @Date: 2024-10-22 17:49:54
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 22:55:39
-->
<template>
  <iframe v-if="iframeVisible" ref="iframeRef" :src="srcRef" frameborder="0" />
</template>

<script setup lang="ts">
import {
  nextTick,
  onMounted,
  onUnmounted,
  onUpdated,
  ref,
  watchEffect,
} from "vue";
import { evaluationExecute, IExecuteParams } from "@/api/evaluation";
import { useUserStore } from "@/store";
import to from "await-to-js";

const srcRef = ref();
const iframeRef = ref();
// 用于存放上层组件调用时候的方法
const parentCallback = ref();
const baseParamsInfo = ref<any>();
const iframeVisible = ref(false);
const userStore = useUserStore();
const timerRef = ref();

const props = defineProps([
  "name",
  "formId",
  "schemaId",
  "actionCode",
  "actionRecordId",
  "eventFlowId",
  "extendId",
  "taskId",
  "taskRecordId",
  // 用于编辑展示数据使用
  "contentId",
  // 自定义提交
  "customSubmit",
  "uuid",
  "patientId",
  // 用于控制当前表单的编辑状态
  "optType",
  // 用于设置当前表单的初始化数据
  "initData",
]);

watchEffect(() => {
  console.log("😘===>", props);
});

// 提供用于上层组件使用的 提交方法 触发表单的数据提交
const submitForm = (_done: (_bool: boolean) => void) => {
  parentCallback.value = _done;
  iframeRef.value.contentWindow.postMessage(
    {
      type: "submit",
    },
    "*",
  );
  // 如果超过1s没有提交成功 则取消加载的提示
  timerRef.value = setTimeout(() => {
    parentCallback.value(false);
  }, 1000);
};

// 重置当前表单的数据
const resetForm = () => {
  setIframeValue();
  // iframeRef.value.contentWindow.postMessage(
  //   {
  //     type: "reset",
  //   },
  //   "*",
  // );
};

// 暂存当前的表单数据
const handleTs = () => {
  iframeRef.value.contentWindow.postMessage(
    {
      type: "temporaryStorage",
      data: {
        uuid: props.taskRecordId ?? props.uuid,
      },
    },
    "*",
  );
};

// 设置当前表单的默认数据
const setDefaultValue = () => {
  if (typeof props?.initData === "object") {
    iframeRef.value.contentWindow.postMessage(
      {
        type: "initData",
        // data: props?.initData ? {
        //   visitingDoctorSignature: '杨荣鑫'
        // } : {}
        data: props?.initData,
      },
      "*",
    );
  }
};

const handleExecute = async (_data: IExecuteParams) => {
  window.clearTimeout(timerRef.value);
  console.log("_form_data", _data);
  if (props.customSubmit) {
    if (typeof parentCallback.value === "function") {
      parentCallback.value(_data);
      parentCallback.value = undefined;
    }
    return;
  }
  // 获取到数据 提交数据之后 关闭当前的抽屉进行剩下的逻辑
  const [err, res] = await to(
    evaluationExecute({
      actionCode: props.actionCode,
      actionRecordId: props.actionRecordId,
      eventFlowId: props.eventFlowId,
      extendId: props.extendId,
      taskId: props.taskId,
      taskRecordId: props.taskRecordId,
      executeData: {
        schemaId: props.schemaId,
        content: _data,
      },
      executeUserId: userStore.userInfo.accountInfo.userId as string,
      executeUserName: userStore.userInfo.accountInfo.name as string,
      role: "100",
    }),
  );
  if (err) {
    parentCallback.value(false);
    throw err;
  }
  if (typeof res?.data.recordId === "string" && res?.data.recordId !== "") {
    parentCallback.value(true);
  }
  console.log("res", res);
};

const invokeMessage = (data: MessageEvent<any>) => {
  // 获取表单中的数据 调用接口
  if (data.data.type === "formData") {
    const jsonData = JSON.parse(data.data.data);
    if (
      // 当前是表单初始化的通知 表单初始化成功之后执行初始化的操作
      jsonData?.type === "init"
    ) {
      setDefaultValue();
    } else {
      try {
        handleExecute(JSON.parse(data.data.data));
      } catch (error) {
        console.error(error);
      }
    }
    console.log("_data", data.data.data);
  }
};

const setIframeValue = () => {
  iframeVisible.value = false;
  const baseParams: any = {
    formId: props.formId ?? "679950718165057541",
    schemaId: props.schemaId ?? "***********6744574",
    env: import.meta.env.VITE_FORM_ENV ?? "1", // 当前是开发环境的标识
    envType: "web",
    triggerPoint: "outFormSubmit", // 外部表单提交的标识
    uuid: props.taskRecordId ?? props.uuid,
    patientId: props.patientId,
  };
  // 存在token的时候 增加一个token 用于文书管理的组件请求接口
  const token = localStorage.getItem("token");
  if (typeof token === "string" && token !== "") {
    baseParams.token = token;
  }
  if (typeof props.contentId === "string" && props.contentId !== "") {
    baseParams.contentId = props.contentId;
    if (typeof props.optType === "string" && props.optType !== "") {
      baseParams.oprtType = props.optType;
    } else {
      baseParams.oprtType = "view";
    }
  }

  baseParamsInfo.value = baseParams;
  const searchParams = new URLSearchParams(baseParams);
  srcRef.value = `${import.meta.env.VITE_FORM_BASE_URL}/#/formInstance?${searchParams.toString()}`;
  console.log("iframe src set to:", srcRef.value);

  nextTick(() => {
    iframeVisible.value = true;
  });
};

onUpdated(() => {
  console.log('_contentId_', baseParamsInfo.value.contentId, props.contentId)
  console.log('typeof_contentId_', typeof baseParamsInfo.value.contentId, typeof props.contentId)
  if (
    props.formId !== baseParamsInfo.value.formId ||
    props.schemaId !== baseParamsInfo.value.schemaId ||
    (
      props.contentId !== null &&
      baseParamsInfo.value.contentId !== null &&
      props.contentId !== '' &&
      props.contentId !== baseParamsInfo.value.contentId
    )
  ) {
    setIframeValue();
  }

  if (
    typeof props.patientId === "string" &&
    props.patientId !== "" &&
    props.patientId !== baseParamsInfo.value.patientId
  ) {
    setIframeValue();
  }
});

onMounted(() => {
  console.log("evaluate_item_props", props);
  setIframeValue();
  // 挂载事件 获取表单的数据
  window.addEventListener("message", invokeMessage);
});

onUnmounted(() => {
  window.removeEventListener("message", invokeMessage);
});

defineExpose({
  submitForm,
  resetForm,
  handleTs,
});
</script>

<style lang="scss" scoped>
iframe {
  width: 100%;
  height: 100%;
}
</style>
