<!--
 * @Description: 展示患者的头部信息
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-10-22 09:56:30
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-19 20:45:01
-->
<template>
  <div class="patient-info-container">
    <div>
      <!-- 展示头像 -->
      <div class="header">
        <img :src="patientInfo?.gender === EGenderType.Man ? ManPng : WomanPng" alt="">
        <div class="info-wrapper">
          <div class="info">
            <!-- 姓名 年龄 和 性别 -->
            <div class="base-info">
              <h1>{{ patientInfo?.name }}</h1>
              <a-popconfirm
                position="bottom"
                @ok="handleChangeFamilyMember"
              >
                <template #icon>
                  &nbsp;
                </template>
                <template #content>
                  <!-- 这里是选择进行切换的内容 -->
                  <div
                    class="family-container"
                  >
                    <span :class="{ active: familyMemberId === '1' }" @click="familyMemberId = '1'">张三</span>
                    <span :class="{ active: familyMemberId === '2' }" @click="familyMemberId = '2'">王五</span>
                  </div>
                </template>
                <!-- TODO: 家庭成员不增加这个数据 -->
                <!-- <div class="family-members">
                  <span>家庭成员(3)</span>
                  <icon-right />
                </div> -->
              </a-popconfirm>
            </div>
            <div class="other-info">
              <span>{{ patientInfo?.age }}岁</span>
              <span>{{ patientInfo?.gender === EGenderType.Man ? '男' : '女' }}</span>
            </div>
            <!-- TODO: 个人的企业暂无实现 -->
          </div>
        </div>
      </div>    
      <div class="contract-info">
        <div class="phone">
          <span>电话：</span>
          <span>{{ patientInfo?.contactMobile ?? '-' }}</span>
        </div>
        <div class="id-card">
          <span>证件号：</span>
          <!-- <span>{{ SensitiveIdCardDel(patientInfo?.idCard ?? '-') }}</span> -->
          <span>{{ patientInfo?.idCard ?? '-' }}</span>
        </div>
      </div>
      <!-- <div class="split-line"></div> -->
      <!-- 展示标签管理 -->
      <LabelManage 
        :show-numbers="2"
      />
    </div>
    <!-- 展示签约信息 -->
    <signing-info :id-card="patientInfo?.idCard ?? ''" />
  </div>
</template>

<script setup lang="ts">
import { IPatientInfoRes, queryPatientInfo } from '@/api/patients';
import { EGenderType } from '@/types/enums';
import ManPng from '@/assets/images/peopleMan.png'
import WomanPng from '@/assets/images/peopleGirl.png'
import useAppStore from '@/store/modules/app';
import to from 'await-to-js';
import { onMounted, ref } from 'vue';
import { SensitiveIdCardDel } from '@/utils/common'
import LabelManage from '../labelManage/index.vue';
import SigningInfo from '../signing-info/index.vue';

const appStore = useAppStore();

const patientInfo = ref<IPatientInfoRes>()
const familyMemberId = ref();

const init = async () => {
  const [err, res] = await to(queryPatientInfo({
    patientId: appStore.patientInfo?.patientId
  }))
  if (
    err
  ) {
    throw err;
  }
  patientInfo.value = res.data
  console.log('res', res)
}

// 切换当前选中的家庭成员的信息
const handleChangeFamilyMember = () => {
  console.log("familyMemberId", familyMemberId.value);
}

onMounted(async () => {
  console.log('appStore.patientInfo?.patientId', appStore.patientInfo?.patientId)
  if (
    typeof appStore.patientInfo?.patientId === 'string' &&
    appStore.patientInfo?.patientId !== ''
  ) {
    await init()
  }
})

</script>

<style lang="scss" scoped>
.patient-info-container {
  display: flex;
  align-items: center;
  padding: var(--spacing-7);
  background-color: white;
  overflow-y: auto;
  justify-content: space-between;
  >div {
    display: flex;
    overflow-x: auto;
    // ff
    >.split-line {
      margin: 0 24px;
      width: 1px;
      background-color: var(--border-gray-color);
      height: 24px;
    }
    >.contract-info {
      border-right: 1px solid var(--border-gray-color);
      margin-right: var(--spacing-12);
      padding-right: var(--spacing-12);
      line-height: var(--spacing-10);
      >.phone, >.id-card {
        >span {
          font-size: var(--font-size-body-3);
          &:nth-of-type(1) {
            color: var(--text-gray-color);
          }
          &:nth-of-type(2) {
            color: var(--text-span-color);
          }
        }
      }
    }
    >.header {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      min-width: fit-content;
      column-gap: var(--spacing-7);
      border-right: 1px solid var(--border-gray-color);
      margin-right: var(--spacing-12);
      padding-right: var(--spacing-12);
  
      >img {
        width: 60px;
        height: 60px;
        border-radius: 50%;
      }
      .info-wrapper {
        display: flex;
        align-items: end;
        column-gap: var(--spacing-7);
        >div.info {
          >.base-info {
            display: flex;
            >h1 {
              color: var(--color-black);
              font-size: var(--font-size-title-1);
              font-weight: var(--font-weight-700);
              margin: 0 var(--spacing-6) 0 0;
            }
            // 展示家庭成员的信息
            .family-members {
              display: flex;
              align-items: center;
              color: var(--primary-color);
              border: 1px solid var(--primary-color);
              border-radius: var(--border-radius-medium);
              font-size: var(--font-size-body-1);
              padding-left: var(--spacing-4);
              padding-right: var(--spacing-4);
            }
            >p {
              color: var(--text-gray-color);
              font-size: var(--font-size-body-3);
              margin: var(--spacing-2) 0 0;
              >span {
                &:first-child {
                  margin-right: var(--spacing-5);
                }
              }
            }
          }
          >.other-info {
            >span {
              display: inline-block;
              color: var(--text-gray-color);
              font-size: var(--font-size-body-3);
              &:nth-child(1) {
                margin-right: var(--spacing-4);
              }
            }
          }
        }
        >.disease-info {
          >div {
            &:first-child {
              // margin-bottom: var(--spacing-4);
            }
            >span {
              display: inline-block;
              font-size: var(--font-size-body-3);
              &:first-child {
                // width: 60px;
                text-align: right;
                color: var(--text-gray-color);
                margin-right: var(--spacing-9);
              }
              &:last-child {
                // color: var(--text-span-color);
                color: var(--text-color);
              }
            }
          }
        }
      }
    }
  }
}

// 切换当前家庭成员的信息
.family-container {
  >span {
    width: 104px;
    cursor: pointer;
    display: block;
    padding: 0 var(--spacing-4);
    border-radius: var(--border-radius-medium);
    &:not(:first-child) {
      margin-top: var(--spacing-4);
    }
    &:hover, &.active {
      color: white;
      background-color: var(--primary-color);
    }
  }
}

</style>