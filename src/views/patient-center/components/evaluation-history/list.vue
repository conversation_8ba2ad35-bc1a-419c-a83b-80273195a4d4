<!--
 * @Description: 展示评估表的内容
 * @Author: yangrong<PERSON>
 * @Date: 2024-10-24 09:38:58
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 11:55:03
-->
<template>
  <div class="evaluation-list">
    <!-- 评估列表 -->
    <div class="list">
      <h2>评估列表</h2>
      <div class="list-item-container">
        <div
          v-for="_item, _index in historyList"
          :key="_index"
          :class="{ 'list-item': true, 'active': activeKey === _item.taskRecordId }"
          @click="activeKey = _item.taskRecordId; activeInfo = _item; _item.read = true;"
        >
          <div>
            <h2>{{ _item.taskName }}</h2>
            <span>{{ _item.endTime }}</span>
            <!-- 评估如果是未读 展示一下红点 -->
            <div
              v-if="!_item.read"
              class="red-circle"
            >
              
            </div>
          </div>
          <p>
            <span>评估人：</span>
            <span>{{ _item.executeUserName }}</span>
          </p>
          <p>
            <span>评估结果：</span>
            <span>{{ _item.resultDesc || '-' }}</span>
          </p>
        </div>
      </div>
    </div>
    <!-- 评估的内容展示 -->
    <div class="content">
      <div class="main-content">
        <h2>{{ activeInfo?.taskName }}评估记录</h2>
        <div class="result-info">
          <div class="info">
            <span>评估日期</span>
            <span>{{ activeInfo?.activateTime }}</span>
          </div>
          <div
            v-if="activeInfo?.resultDesc"
            class="result"
          >
            <span>您本次评估结果为:</span>
            <span>{{ activeInfo?.resultDesc }}</span>
          </div>
        </div>
        <div
          v-if="activeInfo?.formId"
          class="result-content"
        >
          <evaluate-item
            :form-id="activeInfo?.formId"
            :schema-id="activeInfo?.schemaId"
            :content-id="activeInfo?.contentId"
            :patient-id="appStore.patientInfo.patientId"
          />
        </div>
      </div>
      <!-- 对应类型的评估历史记录展示 -->
      <history
        :active-key="activeKey"
        :task-name="activeInfo?.taskName"
        :task-code="activeInfo?.taskCode"
        @handle-detail="handleHistory"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { getCompleteTask, ICompleteTaskRes } from '@/api/patients'
import to from 'await-to-js';
import { onMounted, ref, watch } from 'vue';
import useAppStore from '@/store/modules/app';
import { makeAsRead } from '@/api/evaluation';
import history from './components/history.vue';
import evaluateItem from '../evaluate-item/index.vue';

const appStore = useAppStore();
const props = defineProps([
  'taskRecordId'
])

const historyList = ref<ICompleteTaskRes[]>([])
const activeKey = ref(props.taskRecordId);
const activeInfo = ref<ICompleteTaskRes>();

// 点击展示对应的历史记录
const handleHistory = (
  _info: ICompleteTaskRes
) => {
  const historyItem = historyList.value?.find(_fi => _fi.taskRecordId === _info.taskRecordId);
  if (
    historyItem
  ) {
    activeInfo.value = historyItem;
    activeKey.value = _info.taskRecordId;
  }
}

const init = async () => {
  const [err, res] = await to(getCompleteTask({
    patientId: appStore.patientInfo.patientId,
    pageSize: 999,
    pageNum: 1
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    Array.isArray(res.data.content)
  ) {
    historyList.value = res.data.content;
    if (
      typeof props.taskRecordId === 'string' &&
      props.taskRecordId !== ''
    ) {
      activeInfo.value = res.data.content.find(_item => _item.taskRecordId === props.taskRecordId);
    } else {
      activeKey.value = res.data.content?.[0].taskRecordId;
      activeInfo.value = res.data.content?.[0];
    }
  }
}

watch(() => activeKey.value, async (newValue, oldValue) => {
  if (
    oldValue !== newValue
  ) {
    await makeAsRead({
      taskRecordId: newValue
    })
  }
})

onMounted(async () => {
  await init()
})


</script>

<style lang="scss" scoped>
.evaluation-list {
  display: flex;
  align-items: start;
  justify-content: space-between;
  gap: var(--spacing-4);
  height: 100%;
  >.list {
    display: flex;
    flex-direction: column;
    height: 100%;
    border-radius: var(--border-radius-medium);
    background-color: white;
    >h2 {
      padding: var(--spacing-7) var(--spacing-7) 0 var(--spacing-7);
      color: var(--text-span-color);
      font-size: var(--font-size-body-3);
      margin: 0;
    }
    >.list-item-container {
      flex: 1;
      overflow-y: auto;
      margin-top: var(--spacing-9);
      padding: 0 var(--spacing-7) var(--spacing-7) var(--spacing-7);
      >.list-item {
        // width: 350px;
        width: 20vw;
        padding: var(--spacing-7);
        border: 1px solid var(--evaluate-border-color);
        border-radius: var(--border-radius-medium);
        &:not(:last-child) {
          margin-bottom: var(--spacing-7);
        }
        &.active {
          border-color: var(--evaluation-bg-color);
          background-color: var(--backlog-item-color);
        }
        >div {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          >h2 {
            color: var(--text-span-color);
            font-size: var(--font-size-body-3);
            margin: 0;
          }
          >span {
            color: var(--text-sub-color);
            font-size: var(--font-size-body-1);
          }
          >.red-circle {
            top: 5px;
            left: 0px;
            position: absolute;
            width: 5px;
            height: 5px;
            background-color: red;
            border-radius: 50%;
          }
        }
        >p {
          margin: 0;
          margin-top: var(--spacing-4);
          >span {
            font-size: var(--font-size-body-3);
            &:first-child {
              color: var(--text-sub-color);
            }
            &:last-child {
              margin-left: var(--spacing-2);
            }
          }
        }
      }
    }
  }
  >.content {
    flex: 1;
    height: 100%;
    display: flex;
    >.main-content {
      display: flex;
      flex-direction: column;
      width: 100%;
      padding: var(--spacing-7);
      border-radius: var(--border-radius-medium);
      background-color: white;
      border-right: 1px solid var(--border-color);
      >h2 {
        color: var(--text-span-color);
        font-size: var(--font-size-body-3);
        margin: 0;
      }
      >.result-info {
        padding-bottom: var(--spacing-7);
        border-bottom: 1px solid var(--border-color);
        >.info {
          margin-top: var(--spacing-9);
          padding: var(--spacing-3) var(--spacing-7);
          background-color: var(--backlog-item-color);
          >span {
            font-size: var(--font-size-body-3);
            &:first-child {
              color: var(--text-sub-color);
            }
            &:last-child {
              color: var(--text-h1-color);
              font-weight: var(--font-weight-700);
              margin-left: var(--spacing-7);
            }
          }
        }
        >.result {
          margin-top: var(--spacing-7);
          padding: var(--spacing-8) var(--spacing-7);
          border-radius: var(--border-radius-medium);
          background-color: var(--evaluation-bg-color);
          >span {
            color: white;
            &:first-child {
              font-size: var(--font-size-body-1);
            }
            &:last-child {
              font-weight: var(--font-weight-700);
              margin-left: var(--spacing-7);
            }
          }
        }
      }
      >.result-content {
        flex: 1;
        padding-top: var(--spacing-7);
      }
    }
  }
}
</style>