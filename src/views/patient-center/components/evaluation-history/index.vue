<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-10-23 16:01:39
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 22:52:49
-->
<template>
  <div class="evaluation-history-container">
    <div class="header">
      <h2>评估历史</h2>
      <div
        v-if="itemList.length !== 0"
        class="more cursor-pointer"
        @click="handleMore()"
      >
        <span>更多</span>
        <icon-right />
      </div>
    </div>
    <div class="content">
      <div
        v-for="_item, _index in itemList"
        :key="_index"
        class="content-item"
        @click="handleMore(_item.taskRecordId)"
      >
        <!-- 标题元素 -->
        <h2>{{ _item.taskName }}</h2>
        <!-- 评估的信息 -->
        <div class="info">
          <div>
            <span>评估人</span>
            <span>{{ _item.executeUserName }}</span>
          </div>
          <div>
            <span>评估日期</span>
            <span>{{ _item.date }}</span>
          </div>
          <div>
            <span>评估状态</span>
            <span>已完成</span>
          </div>
        </div>
        <!-- 评估的结果 -->
        <div
          v-if="_item.resultDesc"
          class="result"
        >
          <div>
            <span>评估结论</span>
            <span>{{ _item.resultDesc }}</span>
          </div>
        </div>
      </div>
      <div
        v-if="itemList.length === 0"
        class="content-empty"
      >
        <a-empty
          description="暂无评估历史"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getTaskList, ITaskListRes } from '@/api/patients'
import { usePatientCenterStore } from '@/store';
import { onMounted, ref, markRaw, onUnmounted } from 'vue';
import useAppStore from '@/store/modules/app';
import to from 'await-to-js';
import { ERefreshType, listenerPatientInfoChange, removePatientInfoChangeListener, setPatientInfoChangeEmitter } from '@/utils/patient-listener';
import evaluationList from './list.vue';

const appStore = useAppStore()
const patientCenterStore = usePatientCenterStore()
const itemList = ref<ITaskListRes[]>([]);

const init = async () => {
  const [err, res] = await to(getTaskList({
    patientId: appStore.patientInfo.patientId
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    Array.isArray(res.data.items)
  ) {
    itemList.value = res.data.items
  }
  console.log('res', res)
}

const handleMore = (_taskRecordId?: string) => {
  patientCenterStore.changeSecondaryMenu(true, '评估信息', [
    {
      name: '评估表',
      component: markRaw(evaluationList),
      componentParams: {
        taskRecordId: _taskRecordId
      }
    }
  ]);
}



onMounted(async () => {
  await init()
  // 做完评估之后 自动刷新评估的数据
  listenerPatientInfoChange((
    refreshPatientInfo
  ) => {
    // 需要刷新评估的数据
    if (
      refreshPatientInfo[ERefreshType.EvaluationHistory] === true
    ) {
      init()
        .then(() => {
          setPatientInfoChangeEmitter(ERefreshType.EvaluationHistory, false)
        })
    }
  });
})

onUnmounted(() => {
  removePatientInfoChangeListener();
})


</script>

<style lang="scss" scoped>
.evaluation-history-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 25vw;
  // min-width: 460px;
  padding: var(--spacing-7) 0;
  border-radius: var(--border-radius-medium);
  background-color: white;
  >.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-7);
    >h2 {
      color: var(--text-span-color);
      margin: 0 !important;
      font-size: var(--font-size-body-3);
    }
    >.more {
      color: var(--text-gray-color);
      >span {
        margin-right: var(--spacing-2);
      }
    }
  }
  >.content {
    flex: 1;
    overflow-y: auto;
    margin-top: var(--spacing-7);
    padding: 0 var(--spacing-7);
    // 展示每一个评估结论的元素
    >.content-item {
      &:not(:last-child) {
        margin-bottom: var(--spacing-6);
      }
      padding: var(--spacing-7) 14px;
      border-radius: var(--border-radius-medium);
      border: 1px solid #E5E5E5;
      >h2 {
        color: var(--text-h1-color);
        margin: 0 !important;
        font-size: var(--font-size-body-3);
      }
      >div.info {
        margin-top: var(--spacing-7);
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        >div {
          >span {
            display: block;
            text-align: left;
            font-size: var(--font-size-body-3);
            &:first-child {
              color: var(--text-sub-color-two);
              margin-bottom: var(--spacing-2);
              background-color: #fafafa;
              padding-left: var(--spacing-4);
            }
            &:last-child {
              padding-left: var(--spacing-4);
            }
          }
        }
      }
      >div.result {
        margin-top: var(--spacing-7);
        >div {
          >span {
            display: block;
            font-size: var(--font-size-body-3);
            &:first-child {
              color: var(--text-sub-color-two);
              margin-bottom: var(--spacing-2);
              background-color: #fafafa;
              padding-left: var(--spacing-4);
            }
            &:last-child {
              padding-left: var(--spacing-4);
            }
          }
        }
      }
    }
    >.content-empty {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>