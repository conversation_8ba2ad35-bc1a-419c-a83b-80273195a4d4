<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-10-24 09:43:02
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-04-02 09:20:48
-->
<template>
  <div class="content-record">
    <h2>{{ props.taskName }}历史记录</h2>
    <div class="content-record-container">
      <div
        v-for="_item, _index in historyList"
        :key="_index"
        class="content-record-wrapper"
        @click="handleShowDetail(_item)"
      >
        <div class="content-record-item">
          <div class="time-info">
            <span>{{ _item.date }}</span>
            <span>{{ _item.executeUserName }}</span>
          </div>
          <div
            :class="{ 'content-wrapper': true, 'last': _index === historyList.length - 1 }"
          >
            <div :class="{ content: true, active: _item.taskRecordId === props.activeKey }">
              {{ _item.resultDesc || '-' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getTaskList, ITaskListRes } from '@/api/patients'
import to from 'await-to-js';
import { onMounted, ref, watch } from 'vue';
import useAppStore from '@/store/modules/app';

const appStore = useAppStore();
const props = defineProps([
  'taskCode',
  'taskName',
  'activeKey'
])
const emits = defineEmits([
  'handleDetail'
])
const historyList = ref<ITaskListRes[]>([])

const init = async () => {
  const [err, res] = await to(getTaskList({
    patientId: appStore.patientInfo.patientId,
    taskCode: props.taskCode
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    Array.isArray(res.data.items)
  ) {
    historyList.value = res.data.items
  }
  console.log('history_res', res)
}

// 点击展示历史数据
const handleShowDetail = (_item: unknown) => {
  emits('handleDetail', _item)
}

onMounted(async () => {
  await init()
})

watch(() => props.taskCode, async () => {
  await init();
})


</script>

<style scoped>
.content-record {
  width: 380px;
  border-radius: var(--border-radius-medium);
  background-color: white;
  display: flex;
  flex-direction: column;
  >h2 {
    padding: var(--spacing-7);
    color: var(--text-span-color);
    font-size: var(--font-size-body-3);
    margin: 0;
  }
  >.content-record-container {
    flex: 1;
    overflow-y: auto;
    padding: 0 var(--spacing-7) var(--spacing-7) var(--spacing-7);
    >.content-record-wrapper {
      >.content-record-item {
        >.time-info {
          &::before {
            content: '';
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: var(--backlog-item-sub-color);
            margin-right: var(--spacing-4);
          }
        }
        >.content-wrapper {
          padding: 20px;
          margin-left: 4px;
          border-left: 1px solid var(--backlog-item-sub-color);
          &.last {
            border-left: none;
          }
          >.content {
            padding: 14px var(--spacing-7);
            border-radius: var(--border-radius-medium);
            border: 1px solid var(--gray-bg-color);
            background-color: var(--gray-bg-color);
            &.active {
              border: 1px solid var(--evaluation-bg-color) !important;
              background-color: var(--backlog-item-color);
            }
          }
        }
      }
    }
  }
}
</style>