<!--
 * @Description: 展示文件分类的内容
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-06-25 16:40:39
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-25 14:56:30
-->
<template>
  <a-modal
    v-model:visible="visible"
    title="文件分类"
    width="680px"
    unmount-on-close
    :on-before-ok="handleBeforeOk"
    @cancel="handleCancel"
  >
    <model-content ref="modelContentRef" />
    <!-- 调用 onlyoffice 展示数据的组件 -->
    <!-- <div class="max-container">
      <only-office />
    </div> -->
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useAppStore } from '@/store';
import { usePatientStoreWidthOut } from '@/store/modules/patient';
import { Message } from '@arco-design/web-vue';
import modelContent from './components/model-content.vue';
import { ERefreshType, setPatientInfoChangeEmitter } from '@/utils/patient-listener';
// import onlyOffice from '@/components/only-office/index.vue';

const props = defineProps<{
  open: boolean,
  images: string[]
}>();

const emit = defineEmits(['update:open', 'delete'])

const visible = ref();
const modelContentRef = ref();
const useStore = usePatientStoreWidthOut()
const appStore = useAppStore()

watch(() => props.open, () => {
  visible.value = props.open
})

watch(visible, (val) => {
  emit('update:open', val)
})

const handleCancel = () => {
  visible.value = false;
}

const handleBeforeOk = (
  done: (closed: boolean) => void
) => {
  modelContentRef.value.formRef.validate().then((res: any) => {
    if (res) {
      done(false)
    } else {
      // 调用接口 提交参数
      const values = modelContentRef.value.form;
      if (
        values
      ) {
        useStore.patientDiagnoseAdd({
          date: values.date,
          images: props.images,
          name: values.name,
          organ: values.organ,
          type: values.type,
          patientId: (appStore.patientInfo as any).patientId
        }).then(() => {
          emit('delete')
          Message.success({
            content: '文件分类成功',
          });
          // 更新诊疗信息数据
          setPatientInfoChangeEmitter(ERefreshType.MedicalTreatmentInformation, true);
          done(true)
        }).catch(() => {
          done(false)
        })
        console.log('modelContentRef.value.form', modelContentRef.value.form)
      }
    }
  }).catch((err: any) => {
    console.log('err', err)
    done(false)
  })
}

</script>

<style scoped lang="scss">
.max-container {
  height: 500px;
}
</style>