<!--
 * @Description: 用于展示文件上传的按钮
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-06-25 14:28:36
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-06-25 14:45:23
-->
<template>
  <a-popover
    v-if="waitDealNum"
    position="bottom"
  >
    <a-button type="primary" @click="handleOpenFileUpload">
      <template #icon>
        <icon-upload />
      </template>
      文件上传
    </a-button>
    <template #content>
      <div class="notice-container">
        <h1>温馨提示</h1>
        <p>您还有{{ waitDealNum }}份未分类文件，请及时处理！</p>
      </div>
    </template>
  </a-popover>
  <a-button
    v-else
    type="primary"
    @click="handleOpenFileUpload"
  >
    <template #icon>
      <icon-upload />
    </template>
    文件上传
  </a-button>
  <!-- 这里是用于展示弹窗的组件 -->
  <file-upload
    v-model:open="open"
  />
</template>

<script setup lang="ts">
import { nextTick, ref } from 'vue';
import fileUpload from './index.vue';

// 待处理的文件数量
const waitDealNum = ref();
const open = ref(false);

// 点击打开文件上传的弹窗
const handleOpenFileUpload = () => {
  open.value = true;
}

const onOpenReportUploadModal = () => {
  nextTick(() => {
    console.log('open dialog');
    // reportUploadRef.value.openModal();
  });
};

</script>

<style scoped lang="scss">
.notice-container {
  >h1 {
    margin: 0;
    font-size: var(--font-size-body-3);
    color: var(--text-notice-title-text-color);
  }
  >p {
    margin: 0;
    margin-top: var(--spacing-4);
    font-size: var(--font-size-body-3);
    color: var(--text-gray-deep-color);
  }
}

</style>