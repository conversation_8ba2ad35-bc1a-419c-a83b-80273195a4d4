<!--
 * @Description: 文件上传的抽屉的内容
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-06-25 14:42:14
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-06-28 17:05:20
-->
<template>
  <a-drawer
    unmount-on-close
    :visible="visible"
    :width="1098"
    :mask-closable="true"
    :on-before-ok="handleBeforeOk"
    :footer="false"
    title="文件上传"
    @cancel="handleCancel"
  >
    <content />
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import content from './content.vue';

const visible = ref()

const props = defineProps<{
  open: boolean,
}>()

const emit = defineEmits([
  'update:open',
  'refresh'
]);

watch(() => props.open, () => {
  if (
    props.open !== visible.value
  ) {
    visible.value = props.open;
    if (props.open === true) {
      handleInit()
    }
  }
  console.log('props.open', props.open)
});

watch(visible, (val) => {
  emit('update:open', val)
})

// 获取当前待进行分类的文件
const handleInit = () => {
  console.log("init");
}

const handleBeforeOk = (): Promise<boolean> =>  {
  return Promise.resolve(true);
}

const handleCancel = () => {
  visible.value = false;
}

</script>

<style scoped>

</style>