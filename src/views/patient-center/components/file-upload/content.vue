<!--
 * @Description: 用于展示文件上传的内容
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-06-25 14:28:03
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-25 14:35:10
-->
<template>
  <!-- 需要上传的文件的区域 -->
  <div class="upload-area-container">
    <h2>请选择需上传的文件</h2>
    <div
      class="content"
      :class="{ 'drag-over': isDragOver }"
      @dragover.prevent="onDragOver"
      @dragleave.prevent="onDragLeave"
      @drop.prevent="onDrop"
    >
      <!-- 上传图标 -->
      <img :src="uploadPng" alt="上传图标">
      <!-- 上传文案 -->
      <h3>拖拽文件到这里或点击上传</h3>
      <!-- 文件选择按钮 -->
      <span>支持 PDF、Word、Excel、图片等多种格式，单个文件最大 100MB</span>
      <a-button
        type="primary"
        :loading="uploadLoading"
        @click="handleFileInputClick"
      >
        选择文件
      </a-button>
      <input
        id="file-input"
        type="file"
        multiple
        hidden
        @change="handleFileInputChange"
      />
    </div>
  </div>
  <div class="split-line"></div>
  <!-- 已经上传的文件的列表 -->
  <div class="uploaded-file-list">
    <div class="title">
      <h2>文件列表</h2>
      <div class="opr">
        <a-button type="primary" @click="handleSelectAll">
          <template #icon>
            <img v-if="!checkedAll" class="icon-img" :src="squarePng" alt="">
            <img v-else class="icon-img" :src="checkAllPng" alt="">
          </template>
          全选
        </a-button>
        <a-button type="primary" @click="handleClassify">
          <template #icon>
            <img class="icon-img" :src="filterPng" alt="">
          </template>
          文件分类
        </a-button>
        <a-popconfirm
          content="确认要删除选中的记录?"
          type="info"
          :on-before-ok="(done) => handleDelete(done)"
        >
          <a-button type="primary">
            <template #icon>
              <img class="icon-img" :src="deletePng" alt="">
            </template>
            删除选中
          </a-button>
        </a-popconfirm>
      </div>
    </div>
    <div class="content-wrapper">
      <a-checkbox-group v-model="data" @change="handleChange">
        <div class="content">
          <upload-item
            v-for="item, index in fileList"
            :key="index"
            :item="item"
            @refresh="getFileList"
          />
        </div>
      </a-checkbox-group>
    </div>
  </div>
  <!-- 展示文件分类的配置 -->
  <model
    v-model:open="openModal"
    :images="images"
    @delete="handleDelete"
  />
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { Message } from '@arco-design/web-vue';
import uploadPng from '@/assets/images/service-package-config/upload-icon.png';
import squarePng from '@/assets/images/file-upload/square.png'
import deletePng from '@/assets/images/file-upload/delete.png'
import filterPng from '@/assets/images/file-upload/filter.png';
import checkAllPng from '@/assets/images/file-upload/check-all.png'
import { getBdManageApiFileManageQueryFileByPatientId, postBdManageApiFileManageDeleteFileByIds, postBdManageApiFileManageUploadByPatientId, QueryFileByPatientIdUsingGETWenJianLieBiaoFanCan } from '@/batchApi';
import { useAppStore } from '@/store';
import uploadItem from './components/upload-item.vue';
import model from './model.vue'

const appStore = useAppStore();

// 上传的loading
const uploadLoading = ref(false);
const data = ref<string[]>([])
// 是否全选数据
const checkedAll = ref(false);
const openModal = ref(false);
const isDragOver = ref(false)

const fileList = ref<QueryFileByPatientIdUsingGETWenJianLieBiaoFanCan[]>([
  // {
  //   fileName: 'pdf文件',
  //   fileType: EFileType.PDF
  // },
  // {
  //   fileName: 'ppt文件',
  //   fileType: EFileType.PPT
  // },
  // {
  //   fileName: '销售数据',
  //   fileType: EFileType.XLS
  // },
  // {
  //   fileName: '文档数据',
  //   fileType: EFileType.DOC
  // },
  // {
  //   fileName: '图片数据',
  //   fileType: EFileType.PNG
  // },
  // {
  //   fileName: '产品展示图片',
  //   fileType: EFileType.JPG
  // }
])

const onDragOver = (e: DragEvent) => {
  isDragOver.value = true;
}

const onDragLeave = (e: DragEvent) => {
  isDragOver.value = false;
}

const onDrop = async (e: DragEvent) => {
  isDragOver.value = false;
  const files = e.dataTransfer?.files;
  if (files && files.length > 0) {
    handleFileInputChange({
      target: {
        files
      }
    } as unknown as Event);
  }
}

const images = computed(() => {
  return data.value.map(_item => {
    const item = fileList.value.find(_fi => _fi.id === _item as unknown as number);
    if (item) {
      return item.filePath as unknown as string;
    }
    return '';
  }).filter(_item => _item !== '');
})

const handleDelete = (
  _done: (closed: boolean) => void
) => {
  postBdManageApiFileManageDeleteFileByIds({
    ids: data.value.map(_item => _item as unknown as number)
  }).then(_res => {
    if (
      typeof _done === 'function'
    ) {
      _done(true)
    }
    getFileList();
  }, (err) => {
    if (
      typeof _done === 'function'
    ) {
      _done(false)
    }
  })
}

const handleSelectAll = () => {
  checkedAll.value = !checkedAll.value;
  if (
    checkedAll.value
  ) {
    data.value = fileList.value.map(_item => _item.id as unknown as string);
  } else {
    data.value = [];
  }
}

const handleChange = (values: any) => {
  console.log('values', values)
  if (
    values.length === fileList.value.length
  ) {
    checkedAll.value = true;
  } else {
    checkedAll.value = false;
  }
}

// 调用文件分类弹窗进行文件分类 
const handleClassify = () => {
  if (
    data.value.length === 0
  ) {
    Message.info('请选择需要进行分类的文件')
    return
  }
  openModal.value = true;
}

const handleFileInputClick = () => {
  const fileInput = document.getElementById('file-input') as HTMLInputElement;
  fileInput.click();
}

const handleFileInputChange = (event: Event) => {
  const fileInput = event.target as HTMLInputElement;
  console.log('fileInput', fileInput.files)
  if (
    fileInput?.files?.length !== 0 &&
    fileInput?.files
  ) {
    const formData = new FormData();
    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < fileInput.files.length; i++) {
      formData.append('files', fileInput.files[i]);
    }
    uploadLoading.value = true;
    postBdManageApiFileManageUploadByPatientId(
      {
        patientId: appStore.patientInfo.patientId as unknown as number,
      },
      formData,
    ).then(_res => {
      Message.success('上传成功');
      getFileList();
      console.log('res', _res)
    }).finally(() => {
      fileInput.value = '';
      uploadLoading.value = false;
    })
  }
}

// 获取当前的文件列表
const getFileList = () => {
  getBdManageApiFileManageQueryFileByPatientId({
    patientId: appStore.patientInfo.patientId as unknown as number,
  }).then(_res => {
    if (
      Array.isArray(_res.data)
    ) {
      fileList.value = _res.data;
    }
    console.log('fileList_res', _res)
  })
}

onMounted(() => {
  getFileList();
})

</script>

<script lang="ts">
export type TFileItem = {
  title: string
  type: EFileType
}

export const enum EFileType {
  PDF = 'pdf',
  PPT = 'ppt',
  PPTX = 'pptx',
  XLS = 'xls',
  XLSX = 'xlsx',
  DOC = 'doc',
  DOCX = 'docx',
  PNG = 'png',
  JPG = 'jpg',
}

</script>

<style scoped lang="scss">
.upload-area-container {
  >h2 {
    margin: 0;
    font-size: var(--font-size-title-1);
  }
  >.content {
    padding: var(--spacing-9) 0;
    text-align: center;
    margin-top: var(--spacing-8);
    border: 1px dashed var(--border-gray-color);
    border-radius: var(--border-radius-large);
    background-color: var(--gray-bg-color);
    transition: background 0.2s;
    &.drag-over {
      background: #e6f7ff;
      border: 1px dashed #1890ff;
    }
    >img {
      width: 56px;
    }
    >h3 {
      color: var(--text-span-color);
      font-size: var(--font-size-title-1);
      margin-top: var(--spacing-8);
      margin-bottom: var(--spacing-6);
    }
    >span {
      color: var(--text-gray-deep-colo);
      font-size: var(--font-size-body-1);
    }
    >button {
      display: block;
      margin-top: var(--spacing-9);
      margin-left: auto;
      margin-right: auto;
    }
  }
}
.uploaded-file-list {
  >.title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    >h2 {
      margin: 0;
      font-size: var(--font-size-title-1);
    }
    >div.opr {
      >button {
        &:nth-child(2) {
          margin-left: var(--spacing-7);
          margin-right: var(--spacing-7);
        }
      }
    }
  }
  // 展示所有文件的内容
  >.content-wrapper {
    :deep(.arco-checkbox-group) {
      width: 100%;
    }
    .content {
      margin-top: var(--spacing-8);
      display: grid;
      column-gap: var(--spacing-7);
      row-gap: var(--spacing-7);
      grid-template-columns: repeat(3, 1fr);
    }
  }
}
.icon-img {
  width: 16px;
  vertical-align: text-bottom;
}
.split-line {
  width: 100%;
  height: 12px;
  margin-top: var(--spacing-8);
  margin-bottom: var(--spacing-8);
  background-color: var(--backlog-item-color);
}
</style>