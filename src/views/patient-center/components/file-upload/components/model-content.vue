<!--
 * @Description: 文件分类的内容
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-06-25 16:43:50
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-01 17:51:40
-->
<template>
  <a-form ref="formRef" :model="form" @submit="handleSubmit">
    <a-form-item field="type" label="报告类型" :rules="[{required:true,}]" >
      <a-radio-group v-model="form.type">
        <a-radio value="1">检验报告</a-radio>
        <a-radio value="2">检查报告</a-radio>
        <a-radio value="3">病历</a-radio>
        <a-radio value="4">体检报告</a-radio>
        <a-radio value="5">健康建议书</a-radio>
      </a-radio-group>
    </a-form-item>
    <a-form-item
      field="name" 
      label="报告名称"
      :rules="[{required:true, message: '请填写报告名称'}]"
    >
      <a-input v-model="form.name" placeholder="请输入报告名称" :max-length="100" allow-clear show-word-limit />
    </a-form-item>
    <a-form-item
      field="date"
      label="检查日期"
      :rules="[{required:true, message: '请选择检查日期'}]">
      <a-date-picker v-model="form.date" class="full-width" placeholder="请选择日期" :disabled-date="(current) => dayjs(current).isAfter(dayjs()) "/>
    </a-form-item>
    <a-form-item
      field="organ" 
      label="诊疗机构"
      :rules="[{required:true, message: '请填写诊疗机构'}]"
    >
      <a-input v-model="form.organ" placeholder="请输入诊疗机构" :max-length="100"  allow-clear  show-word-limit />
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import dayjs from 'dayjs'

interface Istate {
  visible: boolean;
  uploadImageType: string;
  uploadImageList: { uri: string, name: string }[];
  previewGroupVisible: boolean;
  previewGroupCurrent: number;
  pdfVisible: boolean;
  pdfSrc: string;
  uploadLoading: boolean;
  infoSaveLoading: boolean;
}

const form = ref<any>({
  type: '1',
  upload: true,
})

const formRef = ref();

const pageState = ref<Istate>({
  visible: false,
  uploadImageType: '1',
  uploadImageList: [],
  previewGroupVisible: false,
  previewGroupCurrent: 1,
  pdfVisible: false,
  pdfSrc: '',
  uploadLoading: false,
  infoSaveLoading: false,
})

const handleSubmit = async ({values, errors}: {values: Record<string, any>; errors: any}) => {
  // useStore.setUpdateReport();
  // console.log('uploadRef', uploadRef.value)
  // // if (!pageState.value.uploadImageList.length) {
  // if (
  //   uploadRef.value.fileList.length === 0
  // ) {
  //   Message.warning({
  //     content:'请添加报告附件',
  //   });
  //   return;
  // }
  // if(errors === undefined) {
  //   pageState.value.infoSaveLoading = true;
  //   const payload = {
  //     ...values,
  //     // images: pageState.value.uploadImageList.map(item => item.uri),
  //     images: uploadRef.value.fileList.map((item: any) => item.response.data.uri),
  //     patientId: (appStore.patientInfo as any).patientId
  //   };

  //   delete (payload as any).upload;

  //   const res = await useStore.patientDiagnoseAdd(payload)
  //   if(res) {
  //     Message.success({
  //       content:'报告上传成功',
  //     });
  //     form.value = {type: '1', upload: true,}
  //     pageState.value.uploadImageList = []
  //     pageState.value.uploadImageType = '1'
  //     useStore.setUpdateReport();
  //     pageState.value.visible = false
  //   }
  //   pageState.value.infoSaveLoading = false;
  // }
}

defineExpose({
  formRef,
  form
})

</script>

<style scoped lang="scss">

</style>