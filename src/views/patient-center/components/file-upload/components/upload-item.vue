<!--
 * @Description: 展示每个元素的样式
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-06-25 15:21:25
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-01 19:42:11
-->
<template>
  <div class="upload-item-wrapper">
    <!-- 基础信息 -->
    <div class="info">
      <img :src="fileTypeImg" alt="">
      <!-- 文件的相关信息 -->
      <div class="file-info">
        <a-tooltip :content="props.item.fileName ?? '-'">
          <h2>{{ props.item.fileName ?? '-' }}</h2>
        </a-tooltip>
        <p>
          <span>{{ props.item.fileSize ?? '-' }}</span>
          <span>上传时间：{{ changeTime(props.item.uploadTime) ?? '-' }}</span>
        </p>
      </div>
    </div>
    <!-- 操作按钮 -->
    <div class="opr">
      <a-checkbox :value="props.item.id as unknown as string"></a-checkbox>
      <div class="btns">
        <a-button type="text" size="small">
          <template #icon>
            <img class="icon-style" :src="fileDownloadImage" alt="">
          </template>
          <a :href="props.item?.filePath" target="_blank" download>下载</a>
        </a-button>
        <a-popconfirm
          content="确认要删除此条记录?"
          type="info"
          :on-before-ok="(done) => handleDelete(done)"
        >
          <a-button type="text" size="small">
            <template #icon>
              <img class="icon-style" :src="deleteFileImage" alt="">
            </template>
            删除
          </a-button>        
        </a-popconfirm>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import jpgImage from '@/assets/images/file-upload/jpg.png'
import docImage from '@/assets/images/file-upload/doc.png'
import pdfImage from '@/assets/images/file-upload/pdf.png'
import pngImage from '@/assets/images/file-upload/png.png'
import pptImage from '@/assets/images/file-upload/ppt.png'
import xlsImage from '@/assets/images/file-upload/xls.png'
import deleteFileImage from '@/assets/images/file-upload/delete-file.png'
import fileDownloadImage from '@/assets/images/file-upload/file-download.png'
import dayjs from 'dayjs';
import { Message } from '@arco-design/web-vue';
import { postBdManageApiFileManageDeleteFileByIds, QueryFileByPatientIdUsingGETWenJianLieBiaoFanCan } from '@/batchApi';
import { EFileType } from '../content.vue';


const props = defineProps<{
  item: QueryFileByPatientIdUsingGETWenJianLieBiaoFanCan
}>()

const emit = defineEmits(['refresh'])

const fileTypeImg = computed(() => {
  switch(props.item.fileType) {
    case EFileType.PDF: return pdfImage;
    case EFileType.DOC: case EFileType.DOCX: return docImage;
    case EFileType.JPG: return jpgImage;
    case EFileType.PNG: return pngImage;
    case EFileType.PPT: case EFileType.PPTX: return pptImage;
    case EFileType.XLS: case EFileType.XLSX: return xlsImage;
    default: return pngImage;
  }
})

const changeTime = (_time: string | undefined) => {
  return dayjs(_time).format('YYYY-MM-DD')
}

const handleDelete = (
  _done: (closed: boolean) => void
) => {
  postBdManageApiFileManageDeleteFileByIds({
    ids: [props.item.id as unknown as number]
  }).then(_res => {
    Message.success("删除成功")
    _done(true)
    emit('refresh')
  }, (err) => {
    _done(false)
  })
}

</script>

<style scoped lang="scss">
.upload-item-wrapper {
  padding: var(--spacing-7);
  border: 1px solid var(--gray-bg-color);
  border-radius: var(--border-radius-large);
  >.info {
    display: flex;
    align-items: center;
    >img {
      width: 46px;
      height: 50px;
      margin-right: var(--spacing-6);
    }
    >div.file-info {
      >h2 {
        margin: 0;
        font-size: var(--font-size-title-1);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: 220px;
      }
      >p {
        margin-top: 8px;
        margin-bottom: 0px;
        color: var(--text-gray-deep-color);
        font-size: var(--font-size-body-3);
        >span {
          &:nth-child(1) {
            margin-right: var(--spacing-6);
          }
        }
      }
    }
  }
  >.opr {
    :deep(.arco-checkbox-icon) {
      width: 16px;
      height: 16px;
    }
    margin-top: var(--spacing-9);
    display: flex;
    align-items: center;
    justify-content: space-between;
    a {
      color: var(--primary-color);
      text-decoration: none;
    }
  }
}

.icon-style {
  width: 16px;
  vertical-align: text-bottom;
}
</style>