<template>
  <a-spin :loading="pageState.loading" style="height: 100%; width: 100%;">
    <div class="inspectionReport">
      <div class="left-container">
        <PageContainer :title="pageState.title[type]">
          <div v-if="pageState.list.length" class="main">
            <div 
              v-for="(item, i) in pageState.list" 
              :key="item.diagnoseId"  
              class="item"
              :class="pageState.activeKey === item.diagnoseId && 'activeKey'"
              @click="onView(item, i)"
            >
            <a-popover position="right">
              <div class="up">
                <div class="title">
                  {{ `${item.name}` }}
                </div>
                <div class="date">{{ item.date }}</div>
                <div v-if="item.isRead === 2 && item.fromType !== 2" class="badge"></div>
              </div>
              <div class="down">
                <div class="left">{{ item.organ }}</div>
                <div 
                  class="right" 
                  :style="{ 
                    backgroundColor: pageState.uploadFrom?.[item.fromType]?.bgColor ?? '', 
                    color: pageState.uploadFrom?.[item.fromType]?.color ?? '',
                    border: pageState.uploadFrom?.[item.fromType]?.border ?? '',
                  }"
                >
                  {{ pageState.uploadFrom[item.fromType].label }}
                </div>
              </div>
              <template #content>
                <div class="popoverBox">
                  <div class="textTitle">报告名称:</div>
                  <div class="text-indent">{{ `${item.name}` }}</div>
                  <div class="textTitle">诊疗机构:</div>
                  <div class="text-indent">{{ `${item.organ}` }}</div>
                </div>
              </template>
            </a-popover>
            </div>
          </div>
          <div v-else class="emptyBox"><a-empty>暂无报告</a-empty></div>
        </PageContainer>
      </div>
      <div class="right-container">
        <PageContainer>
          <div v-if="pageState.selectData?.images?.length" style="height: 100%;">
            <template v-if="pageState.pdfLoading">
              <div class="loadContainer"><a-spin dot></a-spin></div>
            </template>
            <template v-else>
              <a-carousel
              :style="{
                width: '100%',
                height: '100%',
              }"
              @change="handleChange"
            >
              <a-carousel-item v-for="(image, i) in pageState.selectData?.images" :key="image">
                <template v-if="!['', null, undefined].includes(image) && (i + 1) === pageState.carouselIndex">
                  <div v-if="image?.indexOf('png') !== -1 || image?.indexOf('jpg') !== -1" class="imageWarp">
                    <a-image
                      :style="{
                        maxWidth: '80%',
                        maxHeight: '100%'
                      }"
                      :src="image"
                      width="100%"
                    ></a-image>
                  </div>
                  <div v-else-if="image?.indexOf('pdf') !== -1" class="pdfWrap" @click="onPDFPreview(image)">
                    <vue-pdf-embed ref="pdfRef" :source="image" />
                  </div>
                  <div v-else class="otherWrap">
                    <only-office :url="image" :type="image?.split('.').pop()" :title="image" />
                  </div>
                </template>
                <div v-else class="emptyBox"><a-empty>报告链接获取失败</a-empty></div>
              </a-carousel-item>
            </a-carousel>
            </template>
          </div>
          <div v-else class="emptyBox"><a-empty>请选择报告</a-empty></div>
        </PageContainer>
      </div>
    </div>
    <a-modal 
      v-model:visible="pageState.pdfVisible" 
      title="pdf预览" 
      width="1000px" 
      :mask-closable="false" 
      :footer="false" 
      unmount-on-close
      @cancel="handlePdfCancel"
    >
      <div style="height: 80vh; overflow: auto;">
        <vue-pdf-embed ref="pdfRef" :source="pageState.pdfSrc" />
      </div>
    </a-modal>
  </a-spin>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue';
import { usePatientStoreWidthOut } from '@/store/modules/patient'
import { usePatientCenterStore } from '@/store';
import useAppStore from '@/store/modules/app';
import { EStorage } from "@/types/enums";
import PageContainer from '@/components/page-container/index.vue';
import VuePdfEmbed from 'vue-pdf-embed';
import onlyOffice from '@/components/only-office/index.vue';

interface Istate {
  loading: boolean;
  title: {[key: number]: string};
  list: {[key: string]: any}[];
  uploadFrom: { [key: number]: any };
  selectData: { [key: string]: any };
  pdfSrc: string;
  pdfVisible: boolean;
  carouselIndex: any;
  pdfLoading: boolean;
  activeKey: string;
}

const useDiagnosisInfoStore = usePatientStoreWidthOut()
const patientCenterStore = usePatientCenterStore();
const appStore = useAppStore()

/** 接受父组件传递的数据 */
const props = withDefaults(
	defineProps<{
		type?: any;
    defaultViewRercord?: any;
	}>(),
	{
    type: '',
    defaultViewRercord: {},
  },
);

const isUpdate = computed(() => {
  return useDiagnosisInfoStore.updateReport;
})

const pageState = ref<Istate>({
  loading: false,
  title: {
    1: '检验报告',
    2: '检查报告',
    3: '病历',
    4: '体检报告',
    5: '健康建议书',
  },
  list: [],
  uploadFrom: {
    1: {
      label: '自动获取',
      bgColor: 'rgba(0, 82, 217, 0.1)',
      color: '#0052D9',
      border: '1px solid #0052D9'
    },
    2: {
      label: '主动上传',
      bgColor: 'rgba(0, 82, 217, 0.1)',
      color: '#0052D9',
      border: '1px solid #0052D9'
    }
  },
  selectData: {},
  pdfSrc: '',
  pdfVisible: false,
  carouselIndex: 1,
  pdfLoading: false,
  activeKey: '',
})

const fetchData = async (type: any) => {
  pageState.value.loading = true
  const payload = {
    patientId: (appStore.patientInfo as any).patientId,
    type,
    pageNum: 1,
    pageSize: 9999,
  }
  const res = await useDiagnosisInfoStore.patientDiagnoseList(payload)
  if(res) {
    pageState.value.list = res.content
  } else {
    pageState.value.list = []
  }
  pageState.value.loading = false
}

const type = computed(() => {
  // eslint-disable-next-line no-use-before-define
  fetchData(props.type)
  return props.type
})

const onView = async (record: any, index: number) => {
  pageState.value.activeKey = record.diagnoseId
  pageState.value.selectData = record;
  pageState.value.carouselIndex = 1;
  pageState.value.pdfLoading = true;

  if(record.isRead === 2 && record.fromType !== 2) {
    await useDiagnosisInfoStore.patientDiagnoseMarkAsRead({diagnoseId: record.diagnoseId })
    pageState.value.list[index].isRead = 1; 
  }

  setTimeout(() => {
    pageState.value.pdfLoading = false;
  },1000)
};

const handleChange=(value: any)=>{
  pageState.value.carouselIndex = value;
}

const handlePdfCancel = () => {
  pageState.value.pdfVisible = false
}

const onPDFPreview = (item: any) => {
  pageState.value.pdfSrc = item;
  pageState.value.pdfVisible = true;
}

const updateSecondaryMenu = async () => {

  const payload = {
    patientId: (appStore.patientInfo as any).patientId,
  }
  const res = await useDiagnosisInfoStore.patientDiagnoseData(payload)

  patientCenterStore.updateSecondaryMenuName(
    [
    res ? `体检报告(${res?.examCount ?? 0})` : '体检报告', 
    res ? `病历(${res?.caseCount ?? 0})` : '病历', 
    res ? `检查报告(${res?.inspectionCount ?? 0})` : '检查报告',
    res ? `检验报告(${res?.surveyCount ?? 0})` : '检验报告', 
    res ? `健康建议书(${res?.proposalCount ?? 0})` : '检验报告', 
    ],
  );
}

watch(() => isUpdate.value, () => {
  updateSecondaryMenu()
  fetchData(type.value)
})

onMounted(() => {
  const isInit = sessionStorage.getItem(EStorage.DIAGNOSIS_INIT);

  if(isInit){

    onView(props.defaultViewRercord, 0)
    sessionStorage.removeItem(EStorage.DIAGNOSIS_INIT)
  }
})
</script>

<style lang="scss" scoped>
.inspectionReport {
  // background-color: #fff;
  height: 100%;
  display: flex;
  align-items: center;

  :global(.arco-carousel-arrow > div) {
    background-color: #E5E6EB;
    color: #000;
  }

  :global(.arco-carousel-arrow > div:hover) {
    background-color: #E5E6EB;
  }


  .bg-white {
    background-color: #fff;
  }
  
  .left-container {
    width: 20%;
    min-width: 350px;
    height: 100%;
    overflow: auto;
    margin-right: var(--spacing-4);
  }

  .right-container {
    flex: 1;
    height: 100%;
    overflow: auto;
    :deep(.pageContentView) {
      height: 100%;
    }
  }

  .main {
    height: 100%;
    overflow: auto;

    .item {
      width: 100%;
      border: 1px solid #E5E5E5;
      border-radius: 4px;
      padding: 12px;
      cursor: pointer;

      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .up {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;

        .title {
          font-size: 14px;
          font-weight: bold;
          line-height: 20px;
          color: #3D3D3D;
          position: relative;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .date {
          color: #767676;
          font-size: 12px;
          min-width: 80px;
          margin-left: 8px;
          text-align: right;
        }

        .badge {
            width: 12px;
            height: 12px;
            background-color: red;
            border-radius: 50%;
            border: 2px solid #fff;
            position: absolute;
            left: -6px;
            top: -4px;
            z-index: 99;
          }
      }

      .down {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left {
          max-width: calc(100% - 88px);
          color: #767676;
          font-size: 12px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .right {
          font-size: var(--font-size-small);
          padding: 0px var(--size-1);
        }
      }
    }
    
    .activeKey {
      border: 1px solid rgb(var(--primary-6));
    }
  }

  .loadContainer {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .imageWarp, .otherWrap {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow-y: auto;
  }

  .pdfWrap {
    width: 80%;
    height: 100%; 
    overflow: auto;
    margin: 0 auto;
  }

  .emptyBox {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.popoverBox {
    width: 300px;
    max-height: 300px;
    overflow: auto;
    word-break: break-all;

    .textTitle {
      font-weight: bolder;
    }

    .text-indent {
      text-indent: 2rem;
    }
  }
</style>

<style lang="scss" global>
.arco-carousel-arrow {
  >.arco-carousel-arrow-left {
    background-color: #EBEBEB;
    &:hover {
      background-color: #999;
    }
  }
  >.arco-carousel-arrow-right {
    background-color: #EBEBEB;
    &:hover {
      background-color: #999;
    }
  }
}
</style>