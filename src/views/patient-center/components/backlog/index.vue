<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-10-22 16:01:28
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-24 22:44:38
-->
<template>
  <div class="backlog-container">
    <h2>待办</h2>
    <div class="items-wrapper-container">
      <div
        v-if="itemsWarn.length !== 0"
        class="items-wrapper warn"
      >
        <h2>逾期待执行</h2>
        <div class="items-container">
          <div
            v-for="_item, _index in itemsWarn"
            :key="`${_index}${Math.random() * 10 + 1}`"
            class="item warn"
          >
            <h3>{{ _item.servicePkg.taskName }}</h3>
            <p>
              <span>待执行</span>
              <span>已逾期{{ _item.overdueDays }}天</span>
            </p>
            <div class="footer">
              <div>
                <img :src="HealthPackagePng" alt="">
                <span>{{ _item.servicePkg.solutionName }}</span>
              </div>
              <a-button
                type="primary"
                size="small"
                @click="handleEvaluate(_item)"
              >
                去评估
              </a-button>
            </div>
          </div>
        </div>
      </div>
      <div v-for="[dateKey, dateItems] in groupedItems" :key="dateKey" class="items-wrapper">
        <h2>{{ dateKey }}</h2>
        <div class="items-container">
          <div v-for="item in dateItems" :key="item.servicePkg.taskId" class="item">
            <h3>{{ item.servicePkg.taskName }}</h3>
            <p>
              <span>待家医执行</span>
            </p>
            <div class="footer">
              <div>
                <img :src="HealthPackagePng" alt="">
                <!-- <span>{{ item.servicePkg.solutionName }}</span> -->
                <span>基础服务</span>
              </div>
              <a-button type="primary" size="small" @click="handleEvaluate(item)">
                去评估
              </a-button>
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="itemsWarn.length === 0 && groupedItems.length === 0"
        class="items-empty"
      >
        <a-empty
          description="暂无待办"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IPatientBacklogRes, queryPatientBacklog } from '@/api/patients';
import useAppStore from '@/store/modules/app';
import HealthPackagePng from '@/assets/images/health-package.png';
import to from 'await-to-js';
import { onMounted, h, ref, computed, watch } from 'vue';
import { Drawer, Message } from '@arco-design/web-vue';
import dayjs from 'dayjs';
import { useQuickAccessStore } from '@/store';
import { ERefreshType, setPatientInfoChangeEmitter } from '@/utils/patient-listener';
import evaluateItem from '../evaluate-item/index.vue';
import drawerFooter from './drawer-footer.vue';

const appStore = useAppStore()
const quickAccessStore = useQuickAccessStore()
// 用于控制表单引擎页面的ref
const evaluateItemRef = ref();
const items = ref<IPatientBacklogRes[]>([]);
const itemsWarn = ref<(IPatientBacklogRes & { overdueDays: number })[]>([]);

const init = async () => {
  items.value = [];
  itemsWarn.value = [];
  const [err, res] = await to(queryPatientBacklog({
    patientId: appStore.patientInfo.patientId
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    Array.isArray(res.data.items)
  ) {
    const newItemsWarnValue: (IPatientBacklogRes & { overdueDays: number })[] = [];
    const newItemsValue: IPatientBacklogRes[] = []
    res.data.items.forEach((_item: IPatientBacklogRes) => {
      const {
        servicePkg,
        // type
      } = _item
      
      if (servicePkg.expireDays >= 1) {
        newItemsWarnValue.push({
          ..._item,
          overdueDays: servicePkg.expireDays
        });
        itemsWarn.value = newItemsWarnValue;
      } else {
        newItemsValue.push(_item);
        items.value = newItemsValue;
      }
      console.log('count_result', itemsWarn, items)
    })
  }
  console.log('backlog_res', res)
}

const groupedItems = computed(() => {
  const groups: { [key: string]: IPatientBacklogRes[] } = {};
  items.value.forEach(item => {
    const dateKey = dayjs(item.activateTime).format('MM月DD日');
    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }
    groups[dateKey].push(item);
  });
  return Object.entries(groups).sort(([a], [b]) => 
    dayjs(a, 'MM月DD日').diff(dayjs(b, 'MM月DD日'))
  );
});

const handleEvaluate = (_item: IPatientBacklogRes) => {
  let isSubmit = false;
  const drawerRef = Drawer.open({
    title: _item.servicePkg.taskName,
    content: () => h(evaluateItem, {
      ref: evaluateItemRef,
      name: _item.servicePkg.solutionName,
      formId: _item.servicePkg.formId,
      schemaId: _item.servicePkg.schemaId,
      actionCode: _item.servicePkg.actionCode,
      actionRecordId: _item.servicePkg.actionRecordId,
      eventFlowId: _item.servicePkg.eventFlowId,
      extendId: _item.servicePkg.extendId,
      taskId: _item.servicePkg.taskId,
      taskRecordId: _item.servicePkg.taskRecordId,
      patientId: appStore.patientInfo.patientId,
    }),
    footer: () => h(drawerFooter, {
      name: '这是底部的信息',
      closeDrawer: () => {
        drawerRef.close();
      },
      onSubmit: (done: (_bool: boolean) => void) => {
        evaluateItemRef.value?.submitForm((
          _inlineBool: boolean
        ) => {
          if (
            _inlineBool
          ) {
            setPatientInfoChangeEmitter(ERefreshType.EvaluationHistory, true);
            if (
              !isSubmit
            ) {
              Message.success('评估已提交');
              // 评估提交
              init().then(_res => {
                console.log('_res', _res);
              });
              isSubmit = true;
            }
          }
          done(_inlineBool)
        });
      },
      onReset: () => {
        evaluateItemRef.value?.resetForm();
      }
    }),
    width: 800,
    onBeforeOk(done) {
      evaluateItemRef.value?.submitForm(done);
      console.log('evaluateItemRef', evaluateItemRef)
    },
    onCancel: () => {
      // 处理取消操作
      return true; // 返回 true 会关闭抽屉
    }
  });
}

watch(
  () => quickAccessStore.showSendEvaluation,
  () => {
    if (
      quickAccessStore.showSendEvaluation === false
    ) {
      init();
    }
  }
)

onMounted(async () => {
  await init();
})

</script>

<style lang="scss" scoped>
.backlog-container {
  width: 25vw;
  // width: 100%;
  // min-width: 460px;
  display: flex;
  flex-direction: column;
  padding: var(--spacing-7) 0 var(--spacing-7) var(--spacing-7);
  border-radius: var(--border-radius-medium);
  background-color: white;
  >h2 {
    color: var(--text-span-color);
    margin: 0 !important;
    font-size: var(--font-size-body-3);
  }
  >.items-wrapper-container {
    flex: 1;
    overflow-y: auto;
    margin-top: var(--spacing-7);
    padding-right: var(--spacing-7);
    >.items-wrapper {
      &.warn {
        >h2 {
          color: var(--text-warn-color);
          &::before {
            background-color: var(--text-warn-color);
          }
        }
      }
      >h2 {
        margin: 0;
        font-size: var(--font-size-body-3);
        &::before {
          content: '';
          display: inline-block;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          background-color: var(--backlog-item-sub-color);
          margin-right: var(--spacing-4);
        }
      }
      >div.items-container {
        padding: var(--spacing-6);
        border-left: 1px solid var(--progress-line-bg-color);
        margin-left: var(--spacing-2);
        >div.item {
          padding: var(--spacing-7);
          border-radius: var(--border-radius-medium);
          background-color: var(--backlog-item-color);
          &:not(:first-child) {
            margin-top: var(--spacing-7);
          }
          &.warn {
            background-color: var(--warn-bg-color);
          }
          >h3 {
            margin: 0;
            color: var(--text-h1-color);
            font-size: var(--font-size-body-3);
          }
          >p {
            margin: var(--spacing-2) 0 !important;
            >span {
              font-size: var(--font-size-body-1);
              &:first-child {
                color: var(--text-sub-color) !important;
                margin-right: var(--spacing-2);
              }
              &:last-child {
                color: var(--text-warn-color);
              }
            }
          }
          >div.footer {
            display: flex;
            align-items: center;
            justify-content: space-between;
            >div {
              display: flex;
              align-items: center;
              >img {
                width: 16px;
                height: 16px;
              }
              >span {
                color: var(--text-sub-color-two);
                font-size: var(--font-size-body-1);
                margin-left: var(--spacing-2);
              }
            }
            // >button {
            //   margin-left: 200px;
            // }
          }
        }
      }
    }
    >.items-empty {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>