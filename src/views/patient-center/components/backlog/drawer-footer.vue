<!--
 * @Description: 展示抽屉自定义弹窗的内容
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-11-05 17:23:13
 * @LastEditors: yangrongxin
 * @LastEditTime: 2024-11-05 18:02:52
-->
<template>
  <a-button
    type="primary"
    :loading="loading"
    @click="handleSubmit"
  >
    提交
  </a-button>
  <a-button
    @click="handleReset"  
  >
    重置
  </a-button>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const loading = ref(false)

const props = defineProps([
  'name',
  'onSubmit',
  'onReset',
  'closeDrawer'
])

const handleReset = () => {
  if (
    typeof props.onReset === 'function'
  ) {
    props.onReset()
  }
}


const handleSubmit = () => {
  if (
    typeof props.onSubmit === 'function'
  ) {
    loading.value = true;
    props.onSubmit((_bool: boolean) => {
      loading.value = _bool;
      if (
        _bool
      ) {
        props.closeDrawer()        
      }
    })
  }
}

</script>

<style scoped>

</style>