<!--
 * @Description: 预约服务历史
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-12-18 13:43:40
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-04-01 16:04:11
-->
<template>
  <div class="reservation-history-container">
    <div class="header">
      <h2>保险服务</h2>
      <div
        v-if="itemList.length !== 0"
        class="more cursor-pointer"
        @click="handleMore()"
      >
        <span>更多</span>
        <icon-right />
      </div>
    </div>
    <div class="content">
      <div
        v-for="(_item, _index) in itemList"
        :key="_index"
        class="content-item"
        @click="handleMore(_item.id)"
      >
        <h2>{{ _item.title }}</h2>
        <div class="info">
          <span>预约日期：</span>
          <span>{{ _item.date }}</span>
        </div>
        <div class="info">
          <span>就诊医院：</span>
          <span>{{ _item.hospital }}</span>
        </div>
        <!-- 引入随访之后 取消列表上 病情描述的展示 -->
        <!-- <div class="info">
          <span>病情描述：</span>
          <span>{{ _item.desc }}</span>
        </div> -->
      </div>
      <div v-if="itemList.length === 0" class="content-empty">
        <a-empty description="暂无预保险服务" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePatientCenterStore } from "@/store";
import { markRaw, ref } from "vue";
import reservationList from "./list.vue";

const patientCenterStore = usePatientCenterStore();
const itemList = ref<
  {
    id: string;
    title: string;
    date: string;
    hospital: string;
    desc: string;
  }[]
>([
  {
    id: "1",
    title: "预约渠道+服务项目+治疗类型+状态",
    date: "2024-12-16",
    hospital: "北大国际医院",
    desc: "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  },
  {
    id: "2",
    title: "预约渠道+服务项目+治疗类型+状态",
    date: "2024-12-16",
    hospital: "北大国际医院",
    desc: "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  },
  {
    id: "3",
    title: "预约渠道+服务项目+治疗类型+状态",
    date: "2024-12-16",
    hospital: "北大国际医院",
    desc: "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  },
  {
    id: "4",
    title: "预约渠道+服务项目+治疗类型+状态",
    date: "2024-12-16",
    hospital: "北大国际医院",
    desc: "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  },
]);

const handleMore = (_id?: string) => {
  patientCenterStore.changeSecondaryMenu(true, "预约服务历史", [
    {
      name: "预约服务记录",
      component: markRaw(reservationList),
      componentParams: {
        id: _id,
      },
    },
  ]);
};
</script>

<style lang="scss" scoped>
.reservation-history-container {
  display: flex;
  flex-direction: column;
  width: 25vw;
  padding: var(--spacing-7) 0;
  border-radius: var(--border-radius-medium);
  background-color: white;
  > .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-7);
    > h2 {
      color: var(--text-span-color);
      margin: 0 !important;
      font-size: var(--font-size-body-4);
    }
    > .more {
      color: var(--text-gray-color);
      > span {
        margin-right: var(--spacing-2);
      }
    }
  }
  > .content {
    flex: 1;
    overflow-y: auto;
    margin-top: var(--spacing-7);
    padding: 0 var(--spacing-7);
    > .content-item {
      &:not(:last-child) {
        margin-bottom: var(--spacing-6);
      }
      padding: var(--spacing-7) 14px;
      border-radius: var(--border-radius-medium);
      border: 1px solid #e5e5e5;
      > h2 {
        color: var(--text-h1-color);
        margin: 0 !important;
        font-size: var(--font-size-body-3);
      }
      > div.info {
        display: grid;
        margin-top: var(--spacing-7);
        grid-template-columns: 72px 1fr;
        > span {
          &:first-child {
            color: var(--text-sub-color);
            font-size: var(--font-size-body-3);
          }
          &:last-child {
            word-break: break-all;
            font-size: var(--font-size-body-3);
          }
        }
      }
      // >div.result {
      //   margin-top: var(--spacing-7);
      //   >div {
      //     >span {
      //       display: block;
      //       font-size: var(--font-size-body-3);
      //       &:first-child {
      //         color: var(--text-sub-color-two);
      //         margin-bottom: var(--spacing-2);
      //         background-color: #fafafa;
      //         padding-left: var(--spacing-4);
      //       }
      //       &:last-child {
      //         padding-left: var(--spacing-4);
      //       }
      //     }
      //   }
      // }
    }
    > .content-empty {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
