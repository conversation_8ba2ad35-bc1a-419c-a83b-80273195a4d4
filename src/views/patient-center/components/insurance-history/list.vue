<!--
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-12-18 13:46:38
 * @LastEditors: yangrongxin
 * @LastEditTime: 2024-12-18 14:27:30
-->
<template>
  <div class="reservation-list">
    <!-- 评估列表 -->
    <div class="list">
      <h2>保险服务记录</h2>
      <div class="list-item-container">
        <div
          v-for="(_item, _index) in historyList"
          :key="_index"
          :class="{ 'list-item': true, active: activeKey === _item.id }"
          @click="
            activeKey = _item.id;
            activeInfo = _item;
            _item.read = true;
          "
        >
          <div>
            <h2>{{ _item.title }}</h2>
            <span>状态</span>
            <!-- 评估如果是未读 展示一下红点 -->
            <div v-if="!_item.read" class="red-circle"></div>
          </div>
          <p>
            <span>就诊时间：</span>
            <span>{{ _item.date }}</span>
          </p>
          <p>
            <span>就诊医院：</span>
            <span>{{ _item.hospital || "-" }}</span>
          </p>
        </div>
      </div>
    </div>
    <!-- 评估的内容展示 -->
    <div class="content">
      <div class="main-content">
        <h2>预约详情</h2>
        <div class="result-info">
          <div class="info">
            {{ activeInfo?.title ?? "-" }}
          </div>
        </div>
        <div class="result-content">
          <div class="result-item">
            <span>预约渠道：</span>
            <span>健康险</span>
          </div>
          <div class="result-item">
            <span>服务项目：</span>
            <span>7天住院陪护</span>
          </div>
          <div class="result-item">
            <span>治疗项目：</span>
            <span>住院中</span>
          </div>
          <div class="result-item">
            <span>就诊日期：</span>
            <span>2024-12-12</span>
          </div>
          <div class="result-item">
            <span>就诊医院：</span>
            <span>北大国际医院</span>
          </div>
          <div class="result-item">
            <span>病情描述：</span>
            <span
              >在快节奏的现代生活中，培养一种健康、平衡且充实的生活方式至关重要。以下是一些建议，希望能为您的日常生活增添一抹亮色：保持每天固定的起床和睡觉时间，有助于调整生物钟，提高睡眠质量；多吃蔬菜、水果、全谷物和优质蛋白质，减少加工食品和高糖食品的摄入，适量饮水，保持身体水分平衡；无论是散步、跑步、瑜伽还是游泳，选择一项喜欢的运动并坚持下去，每周至少150分钟中等强度或75分钟高强度运动；通过冥想、深呼吸、阅读或听音乐等方式，每天留出时间让自己从日常压力中解脱出来，保持心灵宁静；与家人、朋友保持良好沟通，定期聚会或进行线上交流，增强情感联系，提升幸福感；无论是学习新技能、语言还是兴趣爱好，保持好奇心和学习热情，让大脑保持活跃；减少不必要的物质追求，整理家居环境，保持空间整洁有序，有助于提升生活品质和精神状态；培养感恩的习惯，每天记录下至少三件让你感到感激的事情，这有助于提升积极情绪和幸福感。通过这些简单却有效的实践，您可以逐步建立起一种更加健康、和谐的生活方式，让生活变得更加美好。</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

const props = defineProps(["id"]);

const activeKey = ref(props.id);
const historyList = ref<any>([
  {
    id: "1",
    title: "健康险-7天住院陪护",
    date: "2023-09-11",
    hospital: "北大国际医院",
    read: false,
  },
]);
const activeInfo = ref();
</script>

<style lang="scss" scoped>
.reservation-list {
  display: flex;
  align-items: start;
  justify-content: space-between;
  gap: var(--spacing-4);
  height: 100%;
  > .list {
    display: flex;
    flex-direction: column;
    height: 100%;
    border-radius: var(--border-radius-medium);
    background-color: white;
    > h2 {
      padding: var(--spacing-7) var(--spacing-7) 0 var(--spacing-7);
      color: var(--text-span-color);
      font-size: var(--font-size-body-3);
      margin: 0;
    }
    > .list-item-container {
      flex: 1;
      overflow-y: auto;
      margin-top: var(--spacing-9);
      padding: 0 var(--spacing-7) var(--spacing-7) var(--spacing-7);
      > .list-item {
        // width: 350px;
        width: 20vw;
        padding: var(--spacing-7);
        border: 1px solid var(--evaluate-border-color);
        border-radius: var(--border-radius-medium);
        &:not(:last-child) {
          margin-bottom: var(--spacing-7);
        }
        &.active {
          border-color: var(--evaluation-bg-color);
          background-color: var(--backlog-item-color);
        }
        > div {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-between;
          > h2 {
            color: var(--text-span-color);
            font-size: var(--font-size-body-3);
            margin: 0;
          }
          > span {
            color: var(--text-sub-color);
            font-size: var(--font-size-body-1);
          }
          > .red-circle {
            top: 5px;
            left: 0px;
            position: absolute;
            width: 5px;
            height: 5px;
            background-color: red;
            border-radius: 50%;
          }
        }
        > p {
          margin: 0;
          margin-top: var(--spacing-4);
          > span {
            font-size: var(--font-size-body-3);
            &:first-child {
              color: var(--text-sub-color);
            }
            &:last-child {
              margin-left: var(--spacing-2);
            }
          }
        }
      }
    }
  }
  > .content {
    flex: 1;
    height: 100%;
    display: flex;
    > .main-content {
      display: flex;
      flex-direction: column;
      width: 100%;
      padding: var(--spacing-7);
      border-radius: var(--border-radius-medium);
      background-color: white;
      border-right: 1px solid var(--border-color);
      > h2 {
        color: var(--text-span-color);
        font-size: var(--font-size-body-3);
        margin: 0;
      }
      > .result-info {
        padding-bottom: var(--spacing-7);
        border-bottom: 1px solid var(--border-color);
        > .info {
          margin-top: var(--spacing-9);
          padding: var(--spacing-3) var(--spacing-7);
          background-color: var(--backlog-item-color);
          font-weight: bold;
        }
      }
      > .result-content {
        flex: 1;
        padding-top: var(--spacing-7);
        > .result-item {
          display: grid;
          grid-template-columns: 72px 1fr;
          &:not(:last-child) {
            margin-bottom: var(--spacing-9);
          }
          > span {
            font-size: var(--font-size-body-3);
            &:first-child {
              color: var(--text-gray-color);
            }
            &:last-child {
              word-break: break-all;
            }
          }
        }
      }
    }
  }
}
</style>
