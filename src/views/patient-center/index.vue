<!--
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2024-10-18 16:16:07
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-25 15:05:01
-->
<template>
  <div class="patient-content-wrap">
    <!-- 标题的内容 -->
    <div class="header">
      <div>
        <a-button @click="handleClose">
          <icon-close />
        </a-button>
      </div>
      <div>
        <!-- <a-button type="primary" @click="visible = true">
          <template #icon>
            <icon-schedule />
          </template>
          智能外呼
        </a-button> -->
        <a-button type="primary" @click="handleQuickAccess('发送短信')">
          <template #icon>
            <icon-schedule />
          </template>
          发送短信
        </a-button>
        <a-button type="primary" @click="handleQuickAccess('保险服务')">
          <template #icon>
            <icon-schedule />
          </template>
          保险服务
        </a-button>
        <a-button type="primary" @click="handlePushQuestion">
          <template #icon>
            <icon-schedule />
          </template>
          推送问卷
        </a-button>
        <a-button type="primary" @click="handleQuickAccess('服务预约')">
          <template #icon>
            <icon-idcard />
          </template>
          服务预约
        </a-button>
        <!-- 上传报告按钮 以前用于上传 pdf/图片 的按钮 -->
        <a-button type="primary" @click="onOpenReportUploadModal">
          <template #icon>
            <icon-upload />
          </template>
          上传报告
        </a-button>
        <!-- 文件上传的按钮 在7月11日版本中 因为没有正式环境的onlyOffice -->
        <!-- <file-upload-button /> -->
      </div>
    </div>
    <div class="content">
      <div class="main-header">
        <PatientInfo />
      </div>
      <!-- 如果存在二级菜单的页面 用于展示二级菜单的内容 -->
      <div v-if="patientCenterStore.isShowSecondaryMenu" class="main-menu">
        <div class="opt">
          <a-button @click="handleBack">
            <icon-left />
          </a-button>
          <span>{{ patientCenterStore.secMenuName }}</span>
        </div>
        <!-- 展示二级菜单的切换的内容 -->
        <a-tabs
          v-model:active-key="activeKey"
          class="tabs-content"
          @change="handleChangeSecondaryMenu"
        >
          <a-tab-pane
            v-for="(_item, _index) in patientCenterStore.secondaryMenus"
            :key="_index"
            :title="_item.name"
          >
            <template #title>
              <!-- <a-badge
                :count="_item.badge || 0"
              >
                {{ _item.name }} {{ _item.badge }}
              </a-badge> -->
              {{ _item.name }}
            </template>
          </a-tab-pane>
        </a-tabs>
      </div>
      <!-- 如果现在是展示二级菜单的内容 跟随二级菜单的内容进行展示 -->
      <div class="main-content">
        <!-- 如果是实现多个组件的切换的话 可以渲染全部的组件 然后根据v-if切换 -->
        <component
          :is="curComponents"
          v-if="!patientCenterStore.isShowSecondaryMenu"
        />
        <template
          v-for="(_item, _index) in patientCenterStore.secondaryMenus"
          v-else
        >
          <component
            :is="_item.component"
            v-if="_index === activeKey"
            :key="_index"
            v-bind="curComponentParams"
          />
        </template>
      </div>
    </div>
    <!-- 上传报告弹窗 -->
    <ReportUpload ref="reportUploadRef" />
    <!-- 智能外呼 -->
    <Outbound
      v-model:visible="visible"
      :name="patientInfo?.name"
      :phone="patientInfo?.contactMobile"
    />
  </div>
</template>

<script setup lang="ts">
import { useAppStore, usePatientCenterStore } from "@/store";
import { ref, nextTick, computed, watch } from "vue";
import { storeToRefs } from "pinia";
import useQuickAccessStore from "@/store/modules/quick-access";
import mainHome from "./components/main-home/index.vue";
import PatientInfo from "./components/patient-info/index.vue";
import ReportUpload from "./components/reportUpload/index.vue";
import fileUploadButton from "./components/file-upload/button.vue";
import Outbound from "./components/outbound/index.vue";

const appStore = useAppStore();
const { patientInfo } = storeToRefs(appStore);
const patientCenterStore = usePatientCenterStore();
const quickAccessStore = useQuickAccessStore();

const reportUploadRef = ref();
const visible = ref(false);
const activeKey = ref<number>(0);
const storeActiveKey = computed(() => patientCenterStore.secondaryDefaultKey);

const curComponents = computed(() => {
  if (patientCenterStore.isShowSecondaryMenu === true) {
    return patientCenterStore.secondaryMenus[activeKey.value].component;
  }
  return mainHome;
});

const curComponentParams = computed(() => {
  if (patientCenterStore.isShowSecondaryMenu === true) {
    return (
      patientCenterStore.secondaryMenus[activeKey.value].componentParams ?? {}
    );
  }
  console.log(
    "patientCenterStore.secondaryMenus[activeKey.value].componentParams",
    patientCenterStore.secondaryMenus,
  );
  return {};
});

watch(
  () => patientCenterStore.isShowSecondaryMenu,
  () => {
    activeKey.value = 0;
  },
);

const handleChangeSecondaryMenu = (_key: number | string) => {
  console.log("key", _key);
  activeKey.value = Number(_key);
};

// 定义关闭360弹窗的方法
const emits = defineEmits(["close"]);

const handleClose = () => {
  emits("close");
};

// 点击二级菜单 返回上一级页面
const handleBack = () => {
  patientCenterStore.changeSecondaryMenu(false, "");
};

// 点击进行服务预约
const handleQuickAccess = (title: string) => {
  const eventMap: Record<string, () => void> = {
    保险服务: () =>
      quickAccessStore.toggleServiceReservation(
        true,
        "",
        appStore.patientInfo.patientId,
      ),
    服务预约: () =>
      quickAccessStore.toggleServiceAppointment(
        true,
        "",
        appStore.patientInfo?.patientId,
      ),
    发送短信: () =>
      quickAccessStore.toggleSendSMS(true, [
        {
          patientId: appStore.patientInfo?.patientId,
          phone: appStore.patientInfo?.contactMobile,
        },
      ] as any),
  };
  const eventFunc = eventMap[title];
  eventFunc?.();
};

const handlePushQuestion = () => {
  quickAccessStore.toggleSendEvaluation(true, [appStore.patientInfo.patientId]);
};

const onOpenReportUploadModal = () => {
  nextTick(() => {
    reportUploadRef.value.openModal();
  });
};

// 深度观察storeActiveKey
watch(
  storeActiveKey,
  newValue => {
    activeKey.value = newValue as number;
  },
  { deep: true },
);
</script>

<style lang="scss" scoped>
.patient-content-wrap {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.header {
  display: flex;
  justify-content: space-between;
  background-color: white;
  padding: var(--spacing-4);
  > div {
    &:first-child {
      > button {
        padding: 0 var(--spacing-4);
        border-radius: var(--border-radius-medium);
      }
    }
    &:last-child {
      > button {
        &:not(:last-child) {
          margin-right: var(--spacing-7);
        }
      }
    }
  }
}
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--spacing-4);
  gap: var(--spacing-4);
  overflow-y: hidden;
  > .main-header,
  .main-menu {
    background-color: white;
  }
  // 二级菜单的展示
  > .main-menu {
    display: flex;
    align-items: end;
    border-radius: var(--border-radius-medium);
    > .opt {
      display: flex;
      align-items: center;
      padding: var(--spacing-2) var(--spacing-7);
      > button {
        padding: 0 var(--spacing-4);
        border-radius: var(--border-radius-medium);
      }
      > span {
        display: block;
        padding-right: var(--spacing-9);
        margin-left: var(--spacing-9);
        color: var(--text-h1-color);
        font-size: var(--font-size-body-3);
        font-weight: var(--font-weight-700);
        border-right: 1px solid var(--border-gray-color);
      }
    }
    > .tabs-content {
      :deep(.arco-tabs-content) {
        padding-top: 0px !important;
      }
      :deep(.arco-tabs-nav::before) {
        display: none;
      }
    }
  }
  > .main-content {
    flex: 1;
    overflow-y: hidden;
  }
}
</style>
