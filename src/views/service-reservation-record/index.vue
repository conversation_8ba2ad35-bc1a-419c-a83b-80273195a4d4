<template>
  <div class="table-wrapper">
    <table-filter
      @search="handleSearch"
    />
    <div class="table-container">
      <a-table
        :data="tableData"
        :pagination="false"
        :loading="loading"
        :scroll="scroll"
        scrollbar
      >
        <template #tr="{ record }">
          <tr class="my-tr" @dblclick.prevent="handleOpen360(record)" />
        </template>
        <template #columns>
          <a-table-column
            title="序号"
            :width="65"
          >
            <template #cell="{ rowIndex }">
              <span>{{ rowIndex + 1 }}</span>
            </template>
          </a-table-column>
          <a-table-column
            title="申请单号"
            :width="200"
            data-index="orderNo"
          ></a-table-column>
          <a-table-column
            title="姓名"
            data-index="name"
            :min-width="150"
          >
            <template #cell="{ record }">
              <div class="name-item">
                <img :src="record.gender === EGenderType.Man ? ManPng : WomanPng" alt="">
                <a-trigger 
                  position="bottom"
                  auto-fit-position
                  :unmount-on-close="false"
                  :popup-translate="[20, 20]"
                >
                  <span>{{ record.name }}</span>
                  <template #content>
                    <span class="name-item-desc">{{ record.name }}</span>
                  </template>
                </a-trigger>
              </div>
            </template>
          </a-table-column>
          <a-table-column
            title="性别"
            :width="65"
            data-index="sex"
          ></a-table-column>
          <a-table-column
            title="年龄"
            :width="65"
            data-index="age"
          ></a-table-column>
          <a-table-column
            title="预约渠道"
            data-index="reservationChannelName"
          ></a-table-column>
          <a-table-column
            title="服务分类"
            data-index="serviceParentName"
          ></a-table-column>
          <a-table-column
            title="服务项目"
            data-index="serviceItemCode"
          ></a-table-column>
          <a-table-column
            title="预约提交时间"
            :width="180"
            data-index="createTime"
          ></a-table-column>
          <!-- <a-table-column
            title="就诊日期"
            :width="180"
            data-index="visitDate"
          ></a-table-column>
          <a-table-column
            title="就诊医院"
            data-index="visitHospital"
          ></a-table-column> -->
          <a-table-column
            title="状态"
          >
            <template #cell="{ record }">
              <span>{{ record.statusCodeName }}</span>
            </template>
          </a-table-column>
          <a-table-column title="操作">
            <template #cell="{ record }">
              <div class="table-operate">
                <a-button
                  size="mini"
                  type="outline"
                  @click="handleView(record)"
                >
                  查看
                </a-button>
                <!-- <a-popconfirm
                  content="确认要更新此条服务预约记录的状态？"
                  type="info"
                  :on-before-ok="(e) => handleUpdate(e, record)"
                >
                  <a-button
                    size="mini"
                    type="outline"
                  >
                    更新状态
                  </a-button>
                </a-popconfirm>
                <a-popconfirm
                  content="确认要删除此条服务预约记录？"
                  type="warning"
                  :on-before-ok="(e) => handleDelete(e, record)"
                >
                  <a-button
                    size="mini"
                    type="outline"
                  >
                    删除
                  </a-button>
                </a-popconfirm> -->
              </div>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import to from 'await-to-js';
import { postBdManageApiHealthInsuranceCancelById, postBdManageApiHealthInsuranceQueryByParams, postBdManageApiHealthInsuranceQueryByUserId, QueryByUserIdUsingPOSTServiceAppointmentVo } from '@/batchApi';
import { onMounted, reactive, ref } from 'vue';
import { EGenderType } from '@/types/enums';
import ManPng from '@/assets/images/man.png'
import WomanPng from '@/assets/images/woman.png'
import { useAppStore } from '@/store';
import { IPatientResponse } from '@/api/patients';
import useQuickAccessStore from '@/store/modules/quick-access';
import tableFilter from './components/table-filter.vue';

const appStore = useAppStore()

const scroll = ref({
  y: '100%'
})

const quickAccessStore = useQuickAccessStore()

const loading = ref(false);
const tableData = ref<QueryByUserIdUsingPOSTServiceAppointmentVo[]>([
  {
    visitHospital: '测试医院'
  }
])

// 存储表格分页信息
// const pagination = reactive({
//   current: 1,
//   total: 0,
//   pageSize: 15,
// })

const handleOpen360 = (_item: IPatientResponse) => {
  console.log('handleOpen360', _item)
  // 向患者360的页面注入当前患者的数据
  appStore.openPatientCenter(_item)
}

// 根据传入的参数 进行筛选
const handleSearch = async (_values: any) => {
  // pagination.current = 1;
  await init(_values);
  console.log('_values', _values);
}

const handleUpdate: ( done: (closed: boolean) => void, record: any ) => void = (
  done,
  record
) => {

}

const handleDelete: ( done: (closed: boolean) => void, record: any ) => void = (
  done,
  record
) => {
  console.log('handleDelete', done, record);
  postBdManageApiHealthInsuranceCancelById({
    id: record?.xId
  }).then(() => {
    done(true)
  })
}

const handleView = (_record) => {
  console.log('handleView', _record)
  quickAccessStore.toggleServiceReservation(true, _record.xid)
  // quickAccessStore.toggleServiceReservation(true, "1")
}

const init = async (_params: any = {}) => {
  loading.value = true;
  const [err, res] = await to(postBdManageApiHealthInsuranceQueryByParams({
    ..._params
  }))
  loading.value = false;
  if (
    err
  ) {
    throw err;
  }
  if (
    res.data
  ) {
    tableData.value = res.data;
  }
  // pagination.total = res.data.total;
  // pagination.current = res.data.pageNum;
}

onMounted(async () => {
  await init();
});

</script>

<style lang="scss" scoped>
.table-wrapper {
  background-color: white;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  .table-operate {
    display: flex;
    gap: 8px;
  }
  .table-container {
    overflow-y: scroll;
  }
}

.name-item {
  display: flex;
  align-items: center;
  >img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: var(--spacing-4);
  }
  // 展示具体的名称
  >span {
    cursor: pointer;
    text-decoration: underline;
    color: var(--primary-color);
    display: inline-block;
    width: 80px;
    white-space: nowrap; /* 确保文本不换行 */
    overflow: hidden; /* 超出部分隐藏 */
    text-overflow: ellipsis;
  }
}
.name-item-desc {
  padding: 8px;
  border-radius: 4px;
  background-color: white;
  box-shadow: 2px 2px 5px #ddd;
}
</style>