import { TableColumnData, TableData } from "@arco-design/web-vue";
import { IconSearch } from "@arco-design/web-vue/es/icon";
import Filterable from "@/components/filterable/index.vue";
import { h } from "vue";

/* 自定义模版Id */
export  enum CustomTemplateIdEnum {
  custom = '10372', // 自定义模版Id
}

const statusColorMap = new Map([
  [true, { color: "#00B42A", text: "发送成功" }],
  [false, { color: "#F53F3F", text: "发送失败" }],
]);
export const column: Array<TableColumnData> = [
  {
    title: "序号",
    dataIndex: "id",
    render: ({ rowIndex }) => {
      return rowIndex + 1;
    },
    width: 60,
  },
  {
    title: "发送时间",
    dataIndex: "sendTime",
    width: 180,
    align: "center",
    render: ({ record }: { record: TableData }) => {
      return record?.sendTime ?? "-";
    },
    sortable: {
      sortDirections: ["ascend", "descend"],
    },
  },
  {
    title: "接收方",
    dataIndex: "phone",
    align: "center",
    width: 140,
    filterable: {
      alignLeft: true,
      filter: (value, record) => {
        return record?.phone?.includes(value)
      },
      icon: () => h(IconSearch),
      renderContent: ({
        filterValue,
        setFilterValue,
        handleFilterConfirm,
        handleFilterReset,
      }) =>
        h(Filterable, {
          value: filterValue[0],
          onSetFilterValue: setFilterValue,
          onHandleFilterConfirm: handleFilterConfirm as unknown as any,
          onHandleFilterReset: handleFilterReset as unknown as any,
        }),
    },
  },
  {
    title: "短信内容",
    dataIndex: "value",
    ellipsis: true,
    tooltip: true,
    align: "center",
    // filterable: {
    //   alignLeft: true,
    //   filter: (value, record) => {
    //     return record?.serviceName?.includes(value)
    //   },
    //   icon: () => h(IconSearch),
    //   renderContent: ({
    //     filterValue,
    //     setFilterValue,
    //     handleFilterConfirm,
    //     handleFilterReset,
    //   }) =>
    //     h(Filterable, {
    //       value: filterValue[0],
    //       onSetFilterValue: setFilterValue,
    //       onHandleFilterConfirm: handleFilterConfirm as unknown as any,
    //       onHandleFilterReset: handleFilterReset as unknown as any,
    //     }),
    // },
  },
  {
    title: "发送类型",
    dataIndex: "type",
    width: 120,
    align: "center",
    render: ({ record }: { record: TableData }) => {
      if(!record?.type) return '-'
      return record?.type === 1 ? '手动发送': '系统发送'
    }
  },
  {
    title: "状态",
    width: 120,
    dataIndex: "sendResult",
    render: ({ record }: { record: TableData }) => {
      return h(
        "span",
        { style: { color: statusColorMap.get(record?.sendResult)?.color } },
        statusColorMap.get(record?.sendResult)?.text,
      );
    },
    align: "center",
    filterable: {
      filters: [
        {
          text: "发送成功",
          value: 'true',
        },
        {
          text: "发送失败",
          value: 'false',
        },
      ],
      alignLeft: true,
      filter: (value, record) => value?.includes(`${record?.sendResult}`),
      multiple: true,
    },
  },
  {
    title: "操作",
    dataIndex: "id",
    slotName: "operate",
    align: "center",
    width: 100,
  },
];
