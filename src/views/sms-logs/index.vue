<!--
* @description 短信记录
* @fileName index.vue
* <AUTHOR>
* @date 2025/07/04 10:19:50
!-->
<script setup lang="ts">
import { reactive } from "vue";
import {
  Message,
  PaginationProps,
  TableChangeExtra,
  TableData,
} from "@arco-design/web-vue";
import { to } from "await-to-js";
import * as apis from "@/batchApi";
import tableFilter from "@/views/system/components/table-filter.vue";
import { column } from "./config";

type IRecord = apis.PageUsingPOST_3SmsSendRecord;
interface IState {
  loading: boolean;
  data: IRecord[];
  keyWord: string | undefined;
  isShowPagination: boolean;
}

const pageState = reactive<IState>({
  loading: false,
  data: [],
  keyWord: undefined,
  isShowPagination: false,
});

const pagination = reactive<PaginationProps>({
  size: "small",
  showTotal: true,
  showJumper: true,
  showPageSize: true,
  total: 0,
  current: 1,
  pageSize: 10,
});

const getList = async () => {
  pageState.loading = true;
  const [err, res] = await to(
    apis.postBdManageApiSmsRecordPage({
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      query: {
        keyword: pageState.keyWord,
      },
    }),
  );
  pageState.loading = false;
  if (err) {
    throw err;
  }
  if (res.data) {
    pageState.data = res.data?.content as any;
    pagination.total = res.data.total as number;
  }
};

const handleSending = async (record: IRecord) => {
  pageState.loading = true;
  const [err, res] = await to(
    apis.postBdManageApiSmsRecordSendSms({
      patientId: record?.patientId,
      phone: record?.phone,
      smsRecordId: record?.id,
      templateId: record?.smsTemplateId,
      params: JSON.parse(record?.extend || "{}"),
    }),
  );
  pageState.loading = false;
  if (err) {
    throw err;
  }
  if (res?.code === "1") {
    Message.success("成功");
    getList();
  }
};

const handleTableChange = (_data: TableData, extra: TableChangeExtra) => {
  const { page, pageSize, type, filters } = extra || {};
  // 如果有filter条件隐藏分页
  if (type === "filter" && filters) {
    const hasValidArray = Object.values(filters)?.some(
      v => Array.isArray(v) && v.length > 0,
    );
    pageState.isShowPagination = hasValidArray;
  }
  if (type === "pagination") {
    pagination.current = page;
    pagination.pageSize = pageSize;
    getList();
  }
};

const handleSearch = ({ keyWord }: { keyWord: string }) => {
  pageState.keyWord = keyWord;
  getList();
};

getList();
</script>

<template>
  <div class="table-wrapper">
    <tableFilter @search="handleSearch" @handle-reset="handleSearch">
      <!-- <template #operate>
        <a-button type="primary">
          <template #icon> <icon-sync /> </template>同步信息</a-button
        >
      </template> -->
    </tableFilter>
    <div class="table-container">
      <a-table
        :loading="pageState.loading"
        row-key="name"
        :columns="column"
        :data="pageState.data"
        :pagination="pagination"
        :scroll="{ y: '100%' }"
        @page-change="current => (pagination.current = current)"
        @page-size-change="pageSize => (pagination.pageSize = pageSize)"
        @change="handleTableChange"
      >
        <template #operate="{ record }">
          <a-button
            v-if="!record?.sendResult"
            size="mini"
            type="outline"
            @click="handleSending(record)"
            >重发</a-button
          >
        </template>
      </a-table>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.table-wrapper {
  background-color: white;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  .table-operate {
    display: flex;
    gap: 8px;
  }
  .table-container {
    overflow-y: scroll;
  }
}
</style>
