<template>
  <div>
    <div class="table-wrapper">
      <!-- 搜索条件 -->
      <table-filter
        @search="handleSearch"
      />
      <!-- 表格的主要内容 -->
      <div class="table-container">
        <a-table
          :data="tableData"
          :pagination="false"
          :loading="loading"
          :scroll="scroll"
          scrollbar
        >
          <template #columns>
            <a-table-column
              title="序号"
              :width="65"
              :align="'center'"
            >
              <template #cell="{ rowIndex }">
                <span>{{ rowIndex + 1 }}</span>
              </template>
            </a-table-column>
            <a-table-column
              title="权益包ID"
              :width="200"
              data-index="packageId"
              :align="'center'"
            ></a-table-column>
            <a-table-column
              title="权益渠道"
              :width="200"
              data-index="channelName"
              :align="'center'"
            ></a-table-column>
            <a-table-column
              title="权益包名称"
              :width="200"
              data-index="packageName"
              :align="'center'"
            ></a-table-column>
            <a-table-column
              title="详情信息是否配置"
              :width="200"
              :align="'center'"
            >
              <template #cell="{ record }">
                <span v-if="record.isDetailsConfig">是</span>
                <span v-else>否</span>
              </template>
            </a-table-column>
            <a-table-column
              title="状态"
              :align="'center'"
              :width="60"
            >
              <template #cell="{ record }">
                <span v-if="record.packageStatus === EStatus.ENABLE" class="enabled" >启用</span>
                <span v-else-if="record.packageStatus === EStatus.DISABLED" class="disabled">停用</span>
                <span v-else>-</span>
              </template>
            </a-table-column>
            <a-table-column title="操作" :width="100">
              <template #cell="{ record }">
                <div class="table-operate">
                  <a-button
                    size="mini"
                    type="outline"
                    @click="handleEdit(record)"
                  >
                    编辑
                  </a-button>
                  <a-popconfirm
                    v-if="record.packageStatus"
                    content="确认要更新此条权益包的状态？"
                    type="info"
                    :on-before-ok="(e) => handleUpdate(e, record)"
                  >
                    <a-button
                      size="mini"
                      type="outline"
                    >
                      {{ record.packageStatus === EStatus.ENABLE ? "停用"  : "启用" }}
                    </a-button>
                  </a-popconfirm>
                </div>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>
    </div>
    <!-- 存放用于进行编辑的弹窗 -->
    <package-config-panel
      v-model:open="showDrawer"
      :package-id="editorPackageId"
      @refresh="init"
    />
  </div>
</template>

<script setup lang="ts">
import to from 'await-to-js';
// eslint-disable-next-line camelcase
import { postBdManageApiPackageQueryByParams, postBdManageApiPackageUpdateByPackageId, QueryByParamsUsingPOST_2PackagesListResVo } from '@/batchApi';
import { onMounted, reactive, ref } from 'vue';
import { EGenderType } from '@/types/enums';
import ManPng from '@/assets/images/man.png'
import WomanPng from '@/assets/images/woman.png'
import { useAppStore } from '@/store';
import { IPatientResponse } from '@/api/patients';
import tableFilter from './components/table-filter.vue';
import packageConfigPanel from './components/package-config-panel.vue';

const appStore = useAppStore()
const showDrawer = ref(false);
const editorPackageId = ref();

const scroll = ref({
  y: '100%'
})

const loading = ref(false);
// eslint-disable-next-line camelcase
const tableData = ref<QueryByParamsUsingPOST_2PackagesListResVo[]>([])

// 根据传入的参数 进行筛选
const handleSearch = async (_values: any) => {
  // pagination.current = 1;
  await init(_values);
  console.log('_values', _values);
}

// 更新当前数据的状态
// eslint-disable-next-line camelcase
const handleUpdate: ( done: (closed: boolean) => void, record: QueryByParamsUsingPOST_2PackagesListResVo ) => void = (
  done,
  record
) => {
  console.log('_record', record);
  postBdManageApiPackageUpdateByPackageId({
    packageId: record.packageId,
    status: record.packageStatus === EStatus.ENABLE ? EStatus.DISABLED : EStatus.ENABLE
  }).then(_res => {
    console.log('_res')
    done(true);
    init();
  }).catch(e => {
    console.error('e', e);
    done(false);
    init();
  })
}

// eslint-disable-next-line camelcase
const handleEdit = (_record: QueryByParamsUsingPOST_2PackagesListResVo) => {
  showDrawer.value = true;
  editorPackageId.value = _record.packageId;
  console.log('handleEdit', _record)
}

// 获取服务包的数据
const init = async (_params: any = {}) => {
  console.log('_params', _params)
  loading.value = true;
  const [err, res] = await to(
    postBdManageApiPackageQueryByParams({
      ..._params
    })
  )
  console.log('err, res', err, res);
  loading.value = false;
  if (
    err
  ) {
    throw err;
  }
  if (
    res.data
  ) {
    tableData.value = res.data;
  }
  // pagination.total = res.data.total;
  // pagination.current = res.data.pageNum;
}

onMounted(async () => {
  await init();
});

</script>

<script lang="ts">
export const enum EStatus {
  /**
   * 启用状态
   */
  ENABLE = 1,
  /**
   * 停用撞痛
   */
  DISABLED = 2
}

</script>

<style lang="scss" scoped>
.table-wrapper {
  background-color: white;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  .table-operate {
    display: flex;
    gap: 8px;
  }
  .table-container {
    overflow-y: scroll;
  }
}

.name-item {
  display: flex;
  align-items: center;
  >img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: var(--spacing-4);
  }
  // 展示具体的名称
  >span {
    cursor: pointer;
    text-decoration: underline;
    color: var(--primary-color);
    display: inline-block;
    width: 80px;
    white-space: nowrap; /* 确保文本不换行 */
    overflow: hidden; /* 超出部分隐藏 */
    text-overflow: ellipsis;
  }
}
.name-item-desc {
  padding: 8px;
  border-radius: 4px;
  background-color: white;
  box-shadow: 2px 2px 5px #ddd;
}
// 定义状态的单独颜色
.enabled {
  color: var(--text-enabled-color);
}
.disabled {
  color: var(--text-outdate-color);
}
</style>