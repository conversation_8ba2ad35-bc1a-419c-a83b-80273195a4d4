<!--
 * @Description: 服务包的详情信息录入
 * @Author: ya<PERSON><PERSON><PERSON>
 * @Date: 2025-06-24 16:12:51
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-18 14:52:13
-->
<template>
  <div class="detail-wrapper">
    <h1>详情信息</h1>
    <div class="content">
      <div class="content-item">
        <p>权益包介绍</p>
        <a-form-item
          name="introduce"
          no-style
          :rules="[
            {
              required:true,
              message:'请填写权益包介绍'
            }
          ]"
        >
          <a-textarea
            v-model="form.introduce"
            class="item-style"
            placeholder="请输入"
            allow-clear
            auto-size
            :max-length="100"
            show-word-limit
          />
        </a-form-item>
      </div>
      <div class="content-item">
        <p>产品图上传<span>（宽度为1029px，大小1M以内）</span></p>
        <a-form-item
          name="image"
          no-style
          :rules="[
            {
              required:true,
              message:'请上传产品图'
            }
          ]"
        >
          <upload-img-custom-btn
            v-model="form.image"
          >
            <template #upload-item="{ fileItem, onDeleteImg }">
              <div class="item-style-container">
                <a-image :width="120" :src="fileItem.url"></a-image>
                <div
                  class="delete-icon"
                  @click="onDeleteImg"
                >
                  删除
                </div>
              </div>
            </template>
            <template #upload-btn>
              <div class="item-style-container">
                <div class="placeholder">
                  <img width="30px" :src="uploadIconPng" />
                  <span>点击或拖拽上传JPG/PNG图片</span>
                </div>
              </div>
            </template>
          </upload-img-custom-btn>
        </a-form-item>
      </div>
      <div class="content-item">
        <p>授权同意书配置</p>
        <a-form-item
          name="authConfigUrl"
          no-style
          :rules="[
            {
              required:true,
              message:'请上传产品图'
            }
          ]"
        >
          <upload-img-custom-btn
            v-model="form.authConfigUrl"
          >
            <template #upload-item="{ fileItem, onDeleteImg }">
              <div class="item-style-container">
                <a-image :width="120" :src="fileItem.url"></a-image>
                <div
                  class="delete-icon"
                  @click="onDeleteImg"
                >
                  删除
                </div>
              </div>
            </template>
            <template #upload-btn>
              <div class="item-style-container">
                <div class="placeholder">
                  <img width="30px" :src="uploadIconPng" />
                  <span>点击或拖拽上传JPG/PNG图片</span>
                </div>
              </div>
            </template>
          </upload-img-custom-btn>
        </a-form-item>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import uploadImgCustomBtn from '@/components/bd-upload/upload-img-custom-btn.vue';
import uploadIconPng from '@/assets/images/service-package-config/upload-icon.png'
import { inject } from 'vue';
import { TForm } from './package-config-panel.vue';

const form = inject('form') as TForm

</script>

<style scoped lang="scss">
.detail-wrapper {
  padding: var(--spacing-8);
  >h1 {
    margin: 0;
    font-size: var(--font-size-title-1);
    margin-bottom: var(--spacing-8);
  }
  >.content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    column-gap: var(--spacing-10);
    >.content-item {
      >p {
        color: black;
        font-size: var(--font-size-body-3);
        margin-top: 0px;
        margin-bottom: var(--spacing-6);
        >span {
          color: var(--text-info-color);
        }
      }
      .item-style-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 164px;
        text-align: center;
        border-radius: var(--border-radius-medium);
        background-color: var(--gray-bg-color);
        overflow: hidden;
        >div.placeholder {
          >span {
            display: block;
            color: var(--text-upload-btn-info-text-color);
            margin-top: var(--spacing-7);
          }
        }
        >div.delete-icon {
          cursor: pointer;
          color: white;
          position: absolute;
          right: var(--spacing-6);
          bottom: var(--spacing-4);
          font-size: var(--font-size-body-3);
          padding: var(--spacing-1) var(--spacing-6);
          border-radius: var(--border-radius-delete);
          background-color: var(--text-upload-btn-delete-icon-bg-color);
        }
      }
      .item-style {
        height: 164px;
      }
    }
  }
}
</style>