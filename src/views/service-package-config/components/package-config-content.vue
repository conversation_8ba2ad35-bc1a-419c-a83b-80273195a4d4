<!--
 * @Description: 服务包配置的内容
 * @Author: yangrong<PERSON>
 * @Date: 2025-06-24 14:33:50
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-06-25 15:04:39
-->
<template>
  <!-- 展示减重服务包的信息 -->
  <package-config-content-title />
  <div class="split-line"></div>
  <!-- 展示减重服务包的权益信息 -->
  <package-config-content-right-info />
  <div class="split-line"></div>
  <!-- 展示减重服务包的详情信息 -->
  <package-config-content-detail />
</template>

<script setup lang="ts">
import packageConfigContentTitle from './package-config-content-title.vue';
import packageConfigContentRightInfo from './package-config-content-right-info.vue';
import packageConfigContentDetail from './package-config-content-detail.vue';

</script>

<style scoped lang="scss">
.split-line {
  width: 100%;
  height: 12px;
  background-color: var(--backlog-item-color);
}
</style>