<!--
 * @Description: 顶部的服务包信息
 * @Author: yangrong<PERSON>
 * @Date: 2025-06-24 15:24:23
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-06-26 14:43:53
-->
<template>
  <div class="title-wrapper">
    <div class="title-container">
      <div class="title">
        <a-image width="20px" :src="packageRightsPng" />
        <h1>{{ curPackageInfo.packageName ?? '-' }}</h1>
      </div>
      <div class="content">
        <div class="content-item">
          <span>渠道编码</span>
          <span>{{ curPackageInfo.channelCode ?? '-' }}</span>
        </div>
        <div class="content-item">
          <span>渠道名</span>
          <span>{{ curPackageInfo.channelName ?? '-' }}</span>
        </div>
        <div class="content-item">
          <span>权益包ID</span>
          <span>{{ curPackageInfo.packageId ?? '-' }}</span>
        </div>
        <div class="content-item">
          <span>权益包名称</span>
          <span>{{ curPackageInfo?.packageName ?? '-' }}</span>
        </div>
        <div class="content-item">
          <span>外部编码</span>
          <span>{{ curPackageInfo?.externalProductCode ?? '-' }}</span>
        </div>
        <div class="content-item">
          <span>外部服务名</span>
          <span>{{ curPackageInfo?.externalProductName ?? '-' }}</span>
        </div>
        <div class="content-item">
          <span>有效期</span>
          <!-- <span>2025.04.34 - 2025.12.23</span> -->
          <span>{{ curPackageInfo.expiredTime ?? '-' }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import packageRightsPng from '@/assets/images/service-package-config/package-rights.png';
import { QueryByPackageIdUsingGETPackagesListResVo } from '@/batchApi';
import { inject } from 'vue';

const curPackageInfo = inject("packageInfo") as QueryByPackageIdUsingGETPackagesListResVo
console.log('curPackageInfo', curPackageInfo)

</script>

<style scoped lang="scss">
.title-wrapper {
  padding: var(--spacing-8);
  >.title-container {
    padding: var(--spacing-8);
    border-radius: var(--border-radius-large);
    background-color: var(--package-title-bg-color);
    >.title {
      display: flex;
      align-items: center;
      column-gap: var(--spacing-4);
      > h1 {
        font-size: var(--size-4);
        margin: 0;
      }
    }
    >.content {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      padding: 0 var(--size-7);
      >.content-item {
        margin-top: var(--spacing-7);
        >span {
          display: inline-block;
          font-size: var(--font-size-body-3);
          &:nth-child(1) {
            width: 70px;
            color: var(--text-gray-deep-color);
            margin-right: var(--spacing-8);
          }
          &:nth-child(2) {
            
          }
        }
      }
    }
  }
}
</style>