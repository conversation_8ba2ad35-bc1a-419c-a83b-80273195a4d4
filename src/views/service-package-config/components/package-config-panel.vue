<!--
 * @Description: 用于进行服务包配置的界面
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-06-24 14:32:31
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-06-25 13:55:23
-->
<template>
  <a-drawer
    unmount-on-close
    :width="1098"
    :visible="visible"
    :mask-closable="true"
    body-class="noPadding"
    title="权益包编辑"
    @cancel="handleCancel"
  >
    <a-spin
      class="spinContainer"
      :loading="loadingContent"
    >
      <a-form
        ref="formRef"
        :model="form"
        @submit="handleSubmit"
      >
        <package-config-content />
      </a-form>
    </a-spin>
    <template #footer>
      <package-config-panel-footer
        :form-ref="formRef"
        @handle-cancel="handleCancel"
        @handle-submit="handleSubmit"
      />
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { provide, reactive, ref, watch } from 'vue';
import { FormInstance } from '@arco-design/web-vue';
import { getBdManageApiPackageQueryByPackageId, QueryByPackageIdUsingGETPackagesListResVo } from '@/batchApi';
import packageConfigContent from './package-config-content.vue';
import packageConfigPanelFooter from './package-config-panel-footer.vue';
import { EStatus } from '../index.vue';

// 用于存储整个表单的数据对象
const form = reactive<TForm>({
  status: false,
  introduce: '',
  image: [],
  authConfigUrl: [],
});

const formRef = ref<FormInstance>();
const loadingContent = ref(false);
const curPackageInfo = ref<QueryByPackageIdUsingGETPackagesListResVo>({});

provide('form', form);
provide('packageInfo', curPackageInfo);

const props = defineProps<{
  open: boolean,
  packageId: string
}>()

const emit = defineEmits([
  'update:open',
  'refresh'
]);

const visible = ref(props.open);

watch(() => props.open, () => {
  if (
    props.open !== visible.value
  ) {
    visible.value = props.open;
    if (props.open === true) {
      handleInit(props.packageId)
    }
  }
  console.log('props.open', props.open)
});

watch(visible, (val) => {
  emit('update:open', val)
}) 

// 初始化当前服务包的编辑数据
const handleInit = (_packageId: string) => {
  console.log('_packageId', _packageId)
  loadingContent.value = true;
  getBdManageApiPackageQueryByPackageId({
    packageId: _packageId as unknown as number
  }).then(_res => {
    if (
      _res.data
    ) {
      const resData = _res.data;
      curPackageInfo.value = resData;
      form.introduce = resData.introduce;
      form.status = resData.packageStatus === EStatus.ENABLE
      if (
        resData.image
      ) {
        form.image = [
          {
            name: '产品图',
            url: resData.image,
          }
        ]        
      }
      if (
        resData.authConfigUrl
      ) {
        form.authConfigUrl = [
          {
            name: '授权同意书',
            url: resData.authConfigUrl,
          }
        ]        
      }
    }
  }).finally(() => {
    loadingContent.value = false;
  })
}

const handleCancel = () => {
  formRef.value?.resetFields();
  visible.value = false;
  curPackageInfo.value = {};
}

const handleSubmit = () => {
  handleCancel();
  emit('refresh')
}

</script>

<script lang="ts">
export type TForm = {
  status?: boolean
  introduce?: string
  image: any[]
  authConfigUrl: any[]
}

</script>

<style scoped lang="scss">
.spinContainer {
  width: 100%;
}

</style>