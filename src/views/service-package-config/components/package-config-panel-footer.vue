<!--
 * @Description: 展示自定义页脚的内容
 * @Author: yang<PERSON><PERSON>
 * @Date: 2025-06-24 14:53:27
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-06-25 13:56:26
-->
<template>
  <div class="footer-wrapper">
    <div class="footer-opr">
      <a-form-item
        :label-col-props="{ span: 14 }"
        :wrapper-col-props="{ span: 10 }"
        field="status"
        label="服务启停状态"
      >
        <a-switch
          v-model="form.status"
          class="footer-switch"
        />
      </a-form-item>  
      <span v-if="form.status" class="enabled">已启用</span>
      <span v-else class="disabled">未启用</span>
    </div>
    <div class="footer-buttons">
      <a-button
        :loading="updateLoading"
        type="primary"
        @click="handleValidate"
      >
        确定
      </a-button>
      <a-button
        @click="handleCancel"
      >
        取消
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, ref } from 'vue';
import { FormInstance, Message } from '@arco-design/web-vue';
import { postBdManageApiPackageUpdateByPackageId, QueryByPackageIdUsingGETPackagesListResVo } from '@/batchApi';
import { TForm } from './package-config-panel.vue';
import { EStatus } from '../index.vue';


const form = inject('form') as TForm;
const curPackageInfo = inject('packageInfo') as QueryByPackageIdUsingGETPackagesListResVo;
const updateLoading = ref(false);
console.log('form', form, curPackageInfo)

const props = defineProps<{
  formRef: FormInstance | undefined
}>()

const emit = defineEmits([
  'handleCancel',
  'handleSubmit'
])

const handleValidate = () => {
  props.formRef?.validate((_res) => {
    console.log('_res', _res, form)
    if (
      !_res
    ) {
      if (
        !form.introduce ||
        form.introduce.length === 0
      ) {
        Message.error('请填写权益包介绍！')
      }
      if (
        form.authConfigUrl.length === 0
      ) {
        Message.error('请上传授权同意书！')
        return
      }
      if (
        form.image.length === 0
      ) {
        Message.error('请上传产品图！')
      }
      updateLoading.value = true;
      postBdManageApiPackageUpdateByPackageId({
        // @ts-ignore
        packageId: curPackageInfo?.value.packageId,
        authConfigUrl: form.authConfigUrl[0]?.response?.data?.uri ?? form.authConfigUrl[0]?.url,
        image: form.image[0]?.response?.data?.uri ?? form.image[0]?.url,
        introduce: form.introduce,
        status: form.status ? EStatus.ENABLE : EStatus.DISABLED
      }).then(_res => {
        if (
          _res.data
        ) {
          Message.success('更新成功');
          emit('handleSubmit')
        }
        console.log('_res', _res)
      }).finally(() => {
        updateLoading.value = false;
      })
    }
    
  })
  // emit('handleValidate')
}

const handleCancel = () => {
  emit('handleCancel')
}
</script>

<style scoped lang="scss">
.footer-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  :deep(.arco-form-item) {
    width: 200px;
    margin-bottom: 0px !important;
  }
  >.footer-opr {
    flex: 1;
    display: flex;
    align-items: center;
  }
  >.footer-buttons {
    display: flex;
    align-items: center;
    column-gap: var(--spacing-7);
  }
}

.footer-switch {
  width: 60px;
}

// 定义状态的单独颜色
.enabled {
  color: var(--text-enabled-color);
}
.disabled {
  color: var(--text-outdate-color);
}
</style>