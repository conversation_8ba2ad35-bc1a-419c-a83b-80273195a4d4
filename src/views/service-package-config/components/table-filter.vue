<!--
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2024-12-12 18:11:20
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-06-28 17:00:22
-->
<template>
  <div class="table-filter">
    <a-form
      ref="filterForm"
      :model="form"
      layout="inline"
    >
      <a-form-item field="channelId" label="权益渠道">
        <a-select 
          v-model="form.channelId" :style="{width:'180px'}" placeholder="请选择"
          allow-clear
        >
          <a-option
            v-for="item, index in channelList"
            :key="index"
            :value="item.channelId"
          >
            {{ item.name }}
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="status" label="权益包状态">
        <a-select 
          v-model="form.status" :style="{width:'180px'}" placeholder="请选择"
          allow-clear
        >
          <a-option
            :value="EStatus.ENABLE"
          >
            启用
          </a-option>
          <a-option
            :value="EStatus.DISABLED"
          >
            停用
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item
        class="keyword-search"
        field="packageNameOrId"
      >
        <bd-input
          ref="bdInputRef"
          width="253px"
          field="packageNameOrId"
          placeholder="请输入权益包名称/权益包ID"
          @search="handleSearch"
        />
      </a-form-item>
      <a-form-item>
        <a-button @click="handleReset">重置</a-button>
      </a-form-item>
    </a-form>
    <!-- 同步权益包信息 -->
    <a-button
      type="primary"
      :loading="syncLoading"
      @click="handleSyncRightsPackage"
    >
      <template #icon>
        <a-image
          width="16"
          :src="syncPackageIcon"
          :style="{
            verticalAlign: 'text-bottom'
          }"
        />
      </template>
      <template #default>
        同步权益包信息
      </template>
    </a-button>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue';
import bdInput from '@/components/bd-input/index.vue'
import { getBdManageApiSystemDictDataTypeByDictType, getBdManageApiSystemDictDataTypeByDictTypeResponse, ListUsingPOSTChannel, postBdManageApiChannelList, postBdManageApiPackageSyncServicePackage } from '@/batchApi';
import to from 'await-to-js';
import syncPackageIcon from '@/assets/images/service-package-config/sync-package.png';
import { FormInstance } from '@arco-design/web-vue';
import { EStatus } from '../index.vue';

const projectList = ref<getBdManageApiSystemDictDataTypeByDictTypeResponse['data']>([])
const channelList = ref<ListUsingPOSTChannel[]>([]);

const syncLoading = ref(false);
const filterForm = ref<FormInstance>();
const bdInputRef = ref();

const form = reactive({
  // 权益渠道ID
  channelId: undefined,
  // 权益包名称/权益包ID
  packageNameOrId: undefined,
  // 权益包状态
  status: undefined,
});


const emits = defineEmits([
  'search'
])

watch(() => form, () => {
  console.log('new_vale', form)
  emits('search', form)
}, {
  deep: true
})

// 点击确定的时候 调用对应的方法
const handleSearch = (_searchValue: any) => {
  emits('search', {
    ...form,
    ..._searchValue
  })
}

const handleDictData = async (
  _dictType: string
) => {
  const [err, res] = await to(getBdManageApiSystemDictDataTypeByDictType({
    dictType: _dictType
  }))
  if (
    err
  ) {
    throw err;
  }
  if (
    res.data
  ) {
    return res.data
  }
  return []
}

const handleReset = () => {
  filterForm.value?.resetFields();
  bdInputRef.value?.handleClear();
  emits('search', {})
}

// 同步权益包信息
const handleSyncRightsPackage = () => {
  syncLoading.value = true
  postBdManageApiPackageSyncServicePackage().then(() => {
    emits('search', {})
  }).finally(() => {
    syncLoading.value = false
  })
}

// 获取渠道列表的信息
const handleChannelList = async () => {
  const [err, res] = await to(postBdManageApiChannelList());
  if (err) {
    throw err;
  }
  if (
    res.data
  ) {
    return res.data
  }
  return []
}

onMounted(async () => {
  projectList.value = await handleDictData('service_parent_item')
  channelList.value = await handleChannelList();
})

</script>

<style lang="scss" scoped>
.table-filter {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  :deep(.arco-form-layout-inline .arco-form-item) {
    margin-bottom: 0px !important;
  }
  >.arco-form {
    div.arco-form-item {
      margin-bottom: 0px;
      margin-right: var(--spacing-7);
      &.keyword-search {
        margin-right: 0px;
        :deep(.arco-form-item-label-col) {
          padding-right: 0px;
        }
      }
    }
  }
}
</style>