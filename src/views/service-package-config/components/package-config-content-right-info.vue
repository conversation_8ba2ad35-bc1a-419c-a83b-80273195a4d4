<template>
  <div class="right-info-wrapper">
    <h1>权益信息</h1>
    <a-table
      :data="curPackageInfo.rights ?? []"
      :pagination="false"
      :bordered="false"
    >
    <!--       :scroll="{
        y: 200
      }"
     -->
      <template #columns>
        <a-table-column
          title="序号"
          :width="65"
          :align="'center'"
        >
          <template #cell="{ rowIndex }">
            <span>{{ rowIndex + 1 }}</span>
          </template>
        </a-table-column>
        <a-table-column
          title="权益细项ID"
          data-index="rightsId"
          :align="'center'"
        ></a-table-column>
        <a-table-column
          title="权益细项"
          data-index="name"
          :align="'center'"
        ></a-table-column>
        <a-table-column
          title="权益次数"
          data-index="serviceCount"
          :align="'center'"
        ></a-table-column>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { QueryByPackageIdUsingGETPackagesListResVo } from '@/batchApi';
import { inject } from 'vue';

const curPackageInfo = inject("packageInfo") as QueryByPackageIdUsingGETPackagesListResVo
console.log('curPackageInfo', curPackageInfo)

</script>

<style scoped lang="scss">
.right-info-wrapper {
  padding: var(--spacing-8);
  >h1 {
    margin: 0;
    font-size: var(--font-size-title-1);
    margin-bottom: var(--spacing-8);
  }
}
</style>