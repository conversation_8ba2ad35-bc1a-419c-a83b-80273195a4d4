/*
 * @Description: insert description
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-10-23 14:03:39
 * @LastEditors: yangrong<PERSON>
 * @LastEditTime: 2025-07-13 14:40:26
 */
import { HostEnum } from "@/constant";
import type { EExecuteType } from "@/types/enums";
import axios from "axios";

export interface IExecuteParams {
  actionCode: string
  actionRecordId: string
  eventFlowId: string
  executeData: any
  executeUserId: string
  executeUserName: string
  extendId: string
  role: string
  taskId: string
  taskRecordId: string
  // 用于存放其他表单的数据
  [props: string]: any
}

export interface IEvaluationItem {
  taskId: string
  recordId: string
  executeData: {
    data: any
    executeStatus: EExecuteType
  }
}

export function evaluationExecute(data: IExecuteParams) {
  return axios.post<IEvaluationItem>(
    // 'http://***********:8261/bd-client/flow/execute',
    `/flow/execute`,
    data, {
      baseURL: "/bd-client"
    }
  )
}

interface IMakeReadParams {
  taskRecordId: string
}

// 任务标记为已读
export function makeAsRead(data: IMakeReadParams) {
  return axios.post(
    '/patient/servicePackage/markAsRead',
    data
  )
}