import axios from "axios";

interface IToDoPageParams {
  patientId: string
  // type,1文书待办,2车位待办,未完待续
  type: number
}

interface IToDoPageRes {
  patientId: string
  patientName: string
  // 患者性别 1：男   2：女   3：未知
  gender: number
  age: number
  phone: number
  // 完成状态 0未完成,1已完成,默认0
  status: number
  finishTime: string
  // 推送状态,0未推送,1已推送,默认0
  pushStatus: number
  handlerName: string
}

// 获取待办列表的内容
export function getTodoPage(data: IToDoPageParams) {
  return axios.post<IToDoPageRes>(
    `${import.meta.env.VITE_CLIENT_BASE_URL}/ToDo/page`,
    data
  )
}