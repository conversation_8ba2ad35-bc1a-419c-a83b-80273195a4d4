/*
 * @Description: 进行axios注入的文件
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-09-20 15:57:10
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-18 17:59:52
 */
import axios from 'axios';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import { Message, Modal } from '@arco-design/web-vue';
// import { useUserStore } from '@/store';
import { getToken } from '@/utils/auth';
import {
  LOGIN_EXPIRED
} from '@/types/enums'

// 增加一个对应的配置的定义信息
declare module 'axios' {
  interface AxiosRequestConfig {
    customerErrorMessage?: boolean; // 当前是不是自定义处理报错的信息
  }
}


export interface HttpResponse<T = unknown> {
  code: string | number; // 消息code
  msg: string; // 消息内容
  errCode: string // 错误编码
  timestamp: number; // 响应的时间戳
  data: T; // 返回的响应体
}

if (import.meta.env.VITE_API_BASE_URL) {
  axios.defaults.baseURL = import.meta.env.VITE_API_BASE_URL;
  if (Array.isArray(axios.defaults.transformRequest)) {
    axios.defaults.transformRequest.unshift(function transformRequest(
      data,
      headers
    ) {
      if (
        typeof data === 'object' ||
        (headers && headers['Content-Type'] === 'application/json')
      ) {
        data.appCode = 'HEALTHAPP';
        data.channelCode = data.channelCode || 'health';
      }
      return data;
    });
  }
}

axios.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 检查 URL 是否以 http:// 或 https:// 开头
    if (config.url?.match(/^https?:\/\//)) {
      // 如果是完整的 URL，则清除 baseURL
      config.baseURL = '';
    }
    
    // let each request carry token
    // this example using the JWT token
    // Authorization is a custom headers key
    // please modify it according to the actual situation
    const token = getToken();
    if (token) {
      if (!config.headers) {
        config.headers = {};
      }
      if (
        // 当前是使用的本地的开发环境
        import.meta.env.VITE_API_BASE_URL === 'http://localhost:8081'
      ) {
        // 当前是本地环境的情况下-无网关的时候 增加默认的数据 用于登陆 php服务的时候使用
        // config.headers["JWT-USER-ID"] = '442789018619346944';
        // config.headers["JWT-ORGAN-CODE"] = '349478602556836617';
        config.headers["JWT-USER-ID"] = '770029295006384128';
        config.headers["JWT-ORGAN-CODE"] = '1856959614728355840';
      } else {
        config.headers.Authorization = `Bearer ${token}`;
        config.headers.accessToken = token
      }
    }
    return config;
  },
  (error) => {
    // do something
    return Promise.reject(error);
  }
);

let isShowingLoginExpiredModal = false;

// add response interceptors
axios.interceptors.response.use(
  (
    response: AxiosResponse<HttpResponse>,
  ) => {
    const res = response.data;
    // 增加针对 接口失效的请求处理
    console.log('interceptors_res', res, response.config);
    if (
      res.errCode === LOGIN_EXPIRED
    ) {
      // 在用户登录失效的时候 弹窗进行提示
      if (!isShowingLoginExpiredModal) {
        isShowingLoginExpiredModal = true;
        Modal.info({
          title: '提醒',
          content: '账号登陆失效，请重新登陆。',
          maskClosable: false,
          onOk: () => {
            window.location.href = `${window.location.origin}${window.location.pathname}/#/login`;
          },
          onClose: () => {
            // 弹窗关闭后重置标志位
            isShowingLoginExpiredModal = false;
          }
        });
      }
      return Promise.reject(new Error('登录已失效'));
    }
    // 当前是不是自定义异常信息的处理
    let customerErrorMessage = false;
    if (
      // 当前如果是签约相关的接口的时候 单独进行处理
      response.config.url?.includes('/bd-manage-api/sign/add') ||
      response.config.url?.includes('/bd-manage-api/sign/update')
    ) {
      customerErrorMessage = true
    }
    if (
      res.code !== '1' &&
      res.code !== 20000
    ) {
      // debugger
      if (
        !customerErrorMessage
      ) {
        Message.error({
          content: res.msg || 'Error',
          duration: 5 * 1000,
        });
      }
      
      // 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
      // if (
      //   typeof res.code === 'string' &&
      //   ['50008', '50012', '50014'].includes(res.code) &&
      //   response.config.url !== '/api/user/info'
      // ) {
      //   Modal.error({
      //     title: 'Confirm logout',
      //     content:
      //       'You have been logged out, you can cancel to stay on this page, or log in again',
      //     okText: 'Re-Login',
      //     async onOk() {
      //       const userStore = useUserStore();

      //       await userStore.logout();
      //       window.location.reload();
      //     },
      //   });
      // }
      return Promise.reject(new Error(res.msg || 'Error'));
    }
    return res;
  },
  (error) => {
    // 网络等其他原因导致的请求失败
    Message.error({
      content: error.msg || 'Request Error',
      duration: 5 * 1000,
    });
    return Promise.reject(error);
  }
);

export default axios.request
