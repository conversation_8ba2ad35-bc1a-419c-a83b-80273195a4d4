import axios from 'axios';

/** 诊疗数据添加 */
export function patientDiagnoseAdd(data: any) {
  return axios.post('/patient/diagnose/add', data);
}

/** 诊疗数据列表 */
export function patientDiagnoseList(data: any) {
  return axios.post('/patient/diagnose/list', data);
}

/** 检验检查聚合统计 */
export function patientDiagnoseData(data: any) {
  return axios.post('/patient/diagnose/aggregatedData', data);
}

/** 查询患者标签 */
export function patientLabelList(data: any) {
  return axios.post('/patient/label/list', data);
}

/** 标签分组列表 */
export function patientLabelGroupList(data: any) {
  return axios.post('/patient/label/groupList', data);
}

/** 添加自定义标签 */
export function patientUserDefinedLabelAdd(data: any) {
  return axios.post('/user/userDefinedLabel/add', data);
}

/** 患者添加标签 */
export function patientLabelAdd(data: any) {
  return axios.post('/patient/label/add', data);
}

/** 用户自定义标签 - 删除 */
export function patientUserDefinedLabelDelete(data: any) {
  return axios.post('/user/userDefinedLabel/delete', data);
}

/** 诊疗结果标记已读 */
export function patientDiagnoseMarkAsRead(data: any) {
  return axios.post('/patient/diagnose/markAsRead', data);
}