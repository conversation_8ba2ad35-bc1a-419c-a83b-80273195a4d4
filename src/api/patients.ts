/*
 * @Description: 患者信息相关的接口
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-10-17 17:13:47
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-05-12 15:30:01
 */
import type { ECrowdType, EGenderType, EIsOrNotOne, EIsOrNotTwo } from "@/types/enums";
import axios from "axios";

interface ISubItem {
  categoryId: string; // 分组ID
  name: string; // 名字
  pid: string; // 父id
  count: number; // 数量
  subs: ISubItem[];
  checked: EIsOrNotTwo; // 是否默认选中 1：选中 2:不选中
  code: string; // 分组编号
}

// 每一个分组元素的字段
export interface ICategoryItem {
  categoryId: string; // 分组ID
  name: string; // 名字
  describe: string; // 描述
  pid: string; // 父id
  parentName: string; // 上级名字
  count: number; // 数量
  subs: ISubItem[]; // 子元素列表
  checked: EIsOrNotTwo; // 是否默认选中 1：选中 2:不选中
  code: string; // 分组编号
  pCode: string; // 上级编号
  isCat: boolean; // 是否仅为分类 【为true表示无统计数量】
  levelCondition: string; // 分级规则说明
  level: number; // 层级
  increased: number; // 今日新增
  decreased: number; // 今日减少
  icon: string; // 图标
  topCode: string; // 一级code
  filterFieldName: string; // 分类筛选字段名
  isShow: EIsOrNotTwo; // 是否显示(1-是,2-否)
}

export interface ICategoryRes {
  content: ICategoryItem[];
}

export interface ICategoryParams {
  crowdType: ECrowdType; // 人群类型 1:潜在人群 2：服务人群
  isSignedCrowd?: EIsOrNotOne; // 是否签约人群(0-否,1-是)
  teamId?: number; // 团队ID
  // onlyBPHS?: number, // 只取基公服质量改进(0-否,1-是)
}

// 请求patient接口的数据
export interface IPatientParams {
  keyword?: string
  labelIds?: string[]
  isRegistered?: number
  serviceGroupIds?: string[]
  signPkgTypes?: string[]
  gender?: number
  isSignedCrowd?: number
  teamId?: string
  signedDoctorId?: string
  signStartTime?: string
  signEndTime?: string
  pageNum: number
  pageSize: number
  orderBy?: string
  asc?: boolean
}

type TDiseaseInfo = {
  // 编码
  "code": string,
  // 名称
  "name": "高血压",
  // 名称缩写
  "briefName": "高",
  // 是不是有这个标识
  "isEnabled": false,
  // 这个高亮的颜色值
  "color": "EA9839"
}

export interface IPatientResponse {
  "patientId": string,
  /** 姓名 */
  "name": string,
  /** 头像地址 */
  "headImg": string,
  /** 性别 */
  "gender": number,
  /** 手机号 */
  "contactMobile": number,
  /** 身份证号码 */
  "idCard": string,
  /** 年龄 */
  "age": number,
  /** 患者标签名列表 */
  "labelNames": string[],
  /** 签约人群 */
  "isSignedCrowd": boolean,
  /** 权益到期日期 */
  "contractEnd": string,
  /** 权益发放日期 */
  "contractStart": string,
  /** 服务包类型列表 */
  "pkgTypes": string[]
  "idCardEncrypt": string,
  /** 企业微信地址 */
  "weComeUrl": string,
  /** 重点随访队列Codes */
  "diseases": TDiseaseInfo[]
}

export function getCategoryList(data: ICategoryParams) {
  return axios.post<ICategoryRes>(
    "/patient/patientCategory/categoryList",
    data
  );
}

export function queryPatientList(data: IPatientParams) {
  return axios.post(
    '/patient/patient/list',
    data
  )
}

export function addFormContent(data: IPatientFollowup) {
  const requestData = {
    ...data,
    contentCompress: false
  }
  return axios.post(
    '/form/content/add',
    requestData
  )
}

interface IPatientFollowup {
  formId: string
  schemaId: string
  content: string
}

interface IPatientInfoParams {
  patientId: string
}

export interface IPatientInfoRes {
  patientId: string
  name: string
  headImg: string
  gender: EGenderType
  contactMobile: string
  idCard: string
  age: number
  cityText: string
  countyText: string
  labels: string[] | null
  diseaseHistory: null,
  allergyHistory: null
}

// 查询患者的基本信息
export function queryPatientInfo(data: IPatientInfoParams) {
  return axios.post<IPatientInfoRes>(
    '/patient360/home/<USER>',
    data
  )
}

export type TServicePkg = {
  servicePkgType: string
  solutionId: string
  solutionName: string
  taskName: string
  actionCode: string
  actionRecordId: string
  isDoctorExec: string
  eventFlowId: string
  taskId: string
  taskRecordId: string
  activateTime: string
  expireDays: number
  extendId: string
  formId: string
  schemaId: string
  formBaseUrl: string
}

export interface IPatientBacklogRes {
  activateTime: string
  servicePkg: TServicePkg
  // solution
  type: string
}

// 患者360 近期待办
export function queryPatientBacklog(data: IPatientInfoParams) {
  return axios.post<{
    items: IPatientBacklogRes
  }>(
    '/patient360/home/<USER>',
    data
  )
}

interface ITaskListParam {
  // TCM_constitution
  taskCode?: string
  patientId: string
}

export interface ITaskListRes {
  patientId: string
  date: string
  executeUserName: string
  resultDesc: string
  taskName: string
  taskRecordId: string
}

// 任务执行的历史记录
export function getTaskList(data: ITaskListParam) {
  return axios.post<{
    items: ITaskListRes[]
  }>(
    '/patient/servicePackage/taskHistory',
    data
  )
}

interface ICompleteTaskParams {
  patientId: string
  pageNum: number
  pageSize: number
}

export interface ICompleteTaskRes {
  patientId: string
  doctorName: string
  resultDesc: string
  servicePkgType: string
  solutionId: string
  solutionName: string
  taskName: string
  actionCode: string
  isDoctorExec: boolean
  eventFlowId: string
  taskId: string
  taskCode: string
  activateTime: string
  formId: string
  schemaId: string
  formBaseUrl: string
  read: boolean
  taskRecordId: string
  executeUserName: string
  contentId: string
  endTime: string
}

// 获取已经完成的任务列表
export function getCompleteTask(data: ICompleteTaskParams) {
  return axios.post<{
    content: ICompleteTaskRes[]
  }>(
    '/patient/servicePackage/completedTasks',
    data
  )
}

interface ILabelListParams {
  patientId: string
}

export type TLabel = {
  labelId: string
  name: string
}

interface ILabelListRes {
  content: TLabel[]
}

export function getLabelList(data: ILabelListParams) {
  return axios.post<ILabelListRes>(
    '/patient/label/groupList',
    data
  )
}