/*
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2024-09-20 15:57:10
 * @LastEditors: yangrong<PERSON>
 * @LastEditTime: 2025-07-18 10:15:09
 */
import axios from 'axios';
import type { RouteRecordNormalized } from 'vue-router';
import { UserState } from '@/store/modules/user/types';
import type { EIsOrNotOne, EIsOrNotTwo } from '@/types/enums';

export interface LoginData {
  username: string;
  password: string;
}

export interface ILoginByPhone {
  phone: string;
  authKey: string;
  authCode: string;
  deviceNum: string;
  deviceType: string;
}

export interface IGetAuthCode {
  phone: string;
  // 登陆验证码
  templateCode?: 'HEALTH_ACCOUNT_LOGIN';
}

export interface LoginRes {
  token: string;
}

export interface IGetAuthCodeRes {
  needSlider: boolean;
  authKey: string;
  // 过期时间 秒
  exprieTime: number;
  canNext: boolean;
}

export type TMenuItem = {
  briefName: string; // 名称缩写
  name: string; // 名称
  icon: string; // icon地址
  code: string;
  subMenuItems: TMenuItem[]; // 子菜单弹项
  isDir: boolean; // 是否为目录
  level: number; // 层级
};

export type TRoleInfo = {
  roleId: string; // 角色ID
  roleName: string; // 角色名字
  // 新版本取消 roleCode 字段
  // roleCode: string; // 角色code
  roleType: string; // 角色类型 角色类型 0超级管理员 1机构管理员 2普通用户 3自定义角色
};

export type TAccountInfo = {
  name: string; // 账户名称
  avatar: string; // 用户头像
  // adminId: string; // 注释的代码为后端已经提供 但是前端现在不需要使用 所以未定义
  sex: number; // 用户性别 1 男 2 女
  phone: string; // 手机号码
  userId: string; // 用户id
  // isSystemAccount: boolean; // 是否为系统账号
  // hasQrCode: boolean; // 是否有二维码
  // lastLoginStationOrganId: string; // 最后登录的站点机构id
  organId: string; // 机构id
  organName: string; // 机构名称
  organType: string; // 机构类型
  teamRoleName: string; // 团队角色名 -- 团队是放在机构下面的 每次切换团队等于是重新登陆
  teamId: string; // 团队id
  teamName: string; // 团队名称
  // isGroupPracticeDoctor: string; // 是否为联合门诊医生(1.是,2.否)
  // isDoctor: string; // 是否为医生(1-是,2-否)
  // doctorType: string; // 医生类型(1-专科-2-全科)
  // hasHivPassword: string; // 是否设置HIV管理密码(1-有-2-无)
};

// 通过手机号码登陆的接口返回值
export interface ILoginByPhoneRes {
  token: string;
  info: TAccountInfo;
  roleInfo: TRoleInfo;
  allowAccessResources: string[]; // 可访问接口资源列表
  mainMenus: []; // 主菜单code列表
  mainMenuItems: TMenuItem[]; // 主菜单列表(对象)
  isWhitelistUser: boolean; // 是否白名单用户
  miniProgramUrl: string; // 医生小程序主页的地址
  hisLoginRespVO: {
    accessToken: string; // 令牌
    tokenType: string; // 令牌类型
    refreshToken: string; // 刷新令牌
    expiresIn: string; // 过期时间
    scope: string;
  };
}

export function login(data: LoginData) {
  return axios.post<LoginRes>('/api/user/login', data);
}

// 通过手机号码进行登陆
export function loginByPhone(data: ILoginByPhone) {
  return axios.post<ILoginByPhoneRes>('/user/auth/loginByPhone', data);
}

// 获取手机验证码
export function getAuthCode(data: IGetAuthCode) {
  return axios.post<IGetAuthCodeRes>('/user/auth/sendAuthCode', data);
}

export function logout() {
  return axios.post<LoginRes>('/api/user/logout');
}

export function getUserInfo() {
  return axios.post<UserState>('/api/user/info');
}

export function getMenuList() {
  return axios.post<RouteRecordNormalized[]>('/api/user/menu');
}

interface IUserTeamListParams {
  isOrganTeam: EIsOrNotOne
}

interface IUserTeamListRes {
  content: {
    teamId: string
    name: string
    isChecked: EIsOrNotTwo
  }[]
}

// 获取用户在当前机构下的团队列表
export function getUserTeamList(data: IUserTeamListParams) {
  return axios.post<IUserTeamListRes>('/user/team/userList', data)
}