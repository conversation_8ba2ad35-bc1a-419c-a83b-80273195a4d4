/*
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2024-11-15 09:22:49
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-04-18 16:32:03
 */

import dayjs from "dayjs"

/**
 * 手机号脱敏
 * @param str 手机号
 * @returns
 */
export function SensitivePhoneDel (str: string): string {
  if (typeof str !== 'string' || str === '') return ''
  const pat = /(\d{3})\d*(\d{4})/
  return str.replace(pat, '$1****$2')
}

/**
 * 身份证脱敏
 * @param str 身份证
 * @returns
 */
export function SensitiveIdCardDel (str: string): string {
  if (typeof str !== 'string' || str === '') return ''
  return str.replace(/^(.{4})(?:\w+)(.{4})$/, '$1****$2')
}

// 根据身份证获取患者的基本信息
export function parseIdCard (id: string): {
  sex: string
  birthDay: string
  age: number
} | false {
  const p = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
  const validResult = p.test(id)
  if (!validResult) {
    return false
  }

  // 返回的用户信息
  const obj: {
    sex: string
    birthDay: string
    age: number
  } = {
    sex: '',
    birthDay: '',
    age: 0
  }

  // 身份证号 获取性别
  const sexNum: number = Number(id.substring(id.length - 2, id.length - 1))
  const isEven = (sexNum % 2) === 0
  obj.sex = isEven ? '2' : '1' // 姓别 1 -> 男 | 2 -> 女

  // 身份证号 获取生日
  const birthDay = id.substring(6, 14)
  const y = birthDay.substring(0, 4)
  const m = birthDay.substring(4, 6)
  const d = birthDay.substring(6, 8)
  obj.birthDay = y + '-' + m + '-' + d

  // 身份证号 获取年龄
  const nowDate = new Date() // 获取当前时间
  const intYear = parseInt(y) // 身份证的年份转为数字格式
  const month = nowDate.getMonth() + 1 // 获取当前月份，getMonth()方法获取到的月份是从0开始的，所以这里需要加1
  const intMonth = parseInt(m) // 身份证的月份转为数字格式
  const day = nowDate.getDate()
  let age: number = Number(nowDate.getFullYear() - intYear - 1)
  age = parseInt(String(age))
  // 判断身份证月份是否比当前月份小
  // @ts-expect-error
  if (intMonth < month || (intMonth === month && id.substring(12, 14) <= day)) {
    age++
  }
  obj.age = age

  // 判断基本信息
  if (parseInt(y) <= 0 || parseInt(m) <= 0 || parseInt(d) <= 0 || parseInt(d) > 31 || age > 110) {
    // alert("请检查身份证号");
    console.error('请检查身份证号')
    return false
  }

  return obj
};