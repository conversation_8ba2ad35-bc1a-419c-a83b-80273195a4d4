/*
 * @Description: insert description
 * @Author: yangrong<PERSON>
 * @Date: 2024-09-20 15:57:10
 * @LastEditors: yangrongxin
 * @LastEditTime: 2024-10-16 17:07:40
 */
/**
 * Listening to routes alone would waste rendering performance. Use the publish-subscribe model for distribution management
 * 单独监听路由会浪费渲染性能。使用发布订阅模式去进行分发管理。
 */
import mitt, { Handler } from 'mitt';
import type { RouteLocationNormalized } from 'vue-router';

const emitter = mitt();

// 定义一个唯一的symbol标识
const key = Symbol('ROUTE_CHANGE');

// 记录最后一次跳转的路由信息
let latestRoute: RouteLocationNormalized;

// 定义给Router对象 让Subject 通知所有的订阅者
export function setRouteEmitter(to: RouteLocationNormalized) {
  emitter.emit(key, to);
  latestRoute = to;
}

// 注册订阅者
export function listenerRouteChange(
  // 订阅函数 触发路由变化的时候激活
  handler: (route: RouteLocationNormalized) => void,
  // 是否立即触发订阅函数
  immediate = true
) {
  emitter.on(key, handler as Handler);
  // 立即触发的时候 传入最后一次进入的路由信息
  if (immediate && latestRoute) {
    handler(latestRoute);
  }
}

// 当组件不需要进行路由监控的时候 注销订阅者
export function removeRouteListener() {
  emitter.off(key);
}
