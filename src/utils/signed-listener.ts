/*
 * @Description: 用于进行签约的时候 更新签约相关数据的 发布\订阅模式
 * @Author: yangrong<PERSON>
 * @Date: 2025-04-28 14:23:36
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-04-28 15:08:26
 */
import mitt, {
  Hand<PERSON>
} from 'mitt'

interface IPatientInfo {
  patientId: string
}

const emitter = mitt();

let currentPatientInfo: IPatientInfo | undefined;

const key = Symbol("PATIENT_SIGNED");

// 获取当前更新的患者信息
export function setSignedEmitter(
  _patientInfo: IPatientInfo
) {
  // 触发发布订阅 更新患者的信息
  emitter.emit(key, _patientInfo)
  currentPatientInfo = _patientInfo;
}

export function listenerPatientSigned(
  handler: (patientInfo: IPatientInfo) => void,
  immediate  = true
) {
  emitter.on(key, handler as Handler);
  if (
    immediate && currentPatientInfo
  ) {
    handler(currentPatientInfo)
  }
}

export function removeSignedListener() {
  emitter.off(key);
  currentPatientInfo = undefined;
}