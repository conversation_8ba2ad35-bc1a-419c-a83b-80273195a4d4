/*
 * @Description: 获取pdf的下载路径
 * @Author: yangrong<PERSON>
 * @Date: 2025-05-06 17:05:31
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-26 14:37:30
 */
export interface IRecordProps {
  formId: string | number
  schemaId: string | number
  patientId: string | number
  contentId: string | number
}

const getPdfPath = (
  _record: IRecordProps,
  _isPatient = false
): string => {
  if (
    !_record
  ) {
    return ''
  }
  const inlineFormId = _record.formId; 
  const inlineSchemaId = _record.schemaId;
  const baseParams: any = {
    formId: inlineFormId,
    schemaId: inlineSchemaId,
    env: import.meta.env.VITE_FORM_ENV ?? '1', // 当前是开发环境的标识
    envType: 'web',
    triggerPoint: 'outFormSubmit', // 外部表单提交的标识
    uuid: `${inlineFormId}${inlineSchemaId}`,
    patientId: _record.patientId,
    oprtType: 'view',
    isPatient: _isPatient ? '1' : '2'
  }

  if (
    typeof _record.contentId === 'string' &&
    _record.contentId !== 'null' &&
    _record.contentId !== ''
  ) {
    baseParams.contentId = _record.contentId
  }

  const token = localStorage.getItem('token');
  if (
    typeof token === 'string' &&
    token !== ''
  ) {
    baseParams.token = token;
  }
  const searchParams = new URLSearchParams(baseParams);
  let pdfPath = `${import.meta.env.VITE_FORM_BASE_URL}/#/formInstance?${searchParams.toString()}`

  if (
    import.meta.env.VITE_FORM_ENV === '1'
  ) {
    pdfPath = `/bd-form-engine/#/formInstance?${searchParams.toString()}`
  } 
  // 本地开发环境使用
  // const pdfPath = `/bd-form-engine/#/formInstance?${searchParams.toString()}`
  // const pdfPath = `http://192.168.168.155/bd-form-engine/#/formInstance?${searchParams.toString()}`
  
  return pdfPath;
}

export default getPdfPath;