/*
 * @Description: 用于进行患者360数据刷新的操作
 * @Author: yangrong<PERSON>
 * @Date: 2025-04-28 14:23:36
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-25 14:50:57
 */
import mitt, {
  Handler
} from 'mitt'

const emitter = mitt();

interface IRefreshPatientInfo {
  [props: string]: boolean
}

export const enum ERefreshType {
  // 诊疗信息
  MedicalTreatmentInformation = 'medical-treatment-information',
  // 评估 - 待办
  Evaluation = 'evaluation',
  // 评估历史
  EvaluationHistory = 'evaluation-history',
}

let refreshPatientInfo: IRefreshPatientInfo = {};

const key = Symbol("PATIENT_360");

// 获取当前更新的患者信息
export function setPatientInfoChangeEmitter(
  moduleName: string,
  isRefresh: boolean
) {
  // 触发发布订阅 更新患者的信息
  emitter.emit(key, refreshPatientInfo)
  refreshPatientInfo[moduleName] = isRefresh
}

export function listenerPatientInfoChange(
  handler: (refreshPatientInfo: IRefreshPatientInfo) => void,
  immediate  = true
) {
  emitter.on(key, handler as Handler);
  if (
    immediate && refreshPatientInfo
  ) {
    handler(refreshPatientInfo)
  }
}

export function removePatientInfoChangeListener() {
  emitter.off(key);
  refreshPatientInfo = {};
}