/*
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2025-04-14 09:46:02
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-04-14 09:46:31
 */
/**
 * 手机号验证
 * @param value 手机号码
 * @param callback 回调函数
 */
export const validateMobile = (value: string, callback: (error?: string) => void) => {
  const mobileReg = /^1[3-9]\d{9}$/;
  if (!mobileReg.test(value)) {
    callback('请输入正确的手机号码');
    return;
  }
  callback();
};

/**
 * 身份证号码验证
 * @param value 身份证号码
 * @param callback 回调函数
 * @param skipCheck 是否跳过验证（非身份证类型时使用）
 */
export const validateIdCard = (
  value: string, 
  callback: (error?: string) => void,
  skipCheck = false
) => {
  // 如果设置跳过验证（非身份证类型）
  if (skipCheck) {
    callback();
    return;
  }

  // 身份证号码格式验证
  const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  if (!idCardReg.test(value)) {
    callback('请输入正确的身份证号码');
    return;
  }

  // 身份证号码的严格验证
  if (value.length === 18) {
    // 验证生日
    const year = value.substring(6, 10);
    const month = value.substring(10, 12);
    const day = value.substring(12, 14);
    const date = new Date(`${year}-${month}-${day}`);
    if (date.toString() === 'Invalid Date') {
      callback('身份证号码中的出生日期无效');
      return;
    }

    // 验证校验码
    const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const parity = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    let sum = 0;
    let ai = 0;
    let wi = 0;
    for (let i = 0; i < 17; i++) {
      ai = Number(value[i]);
      wi = factor[i];
      sum += ai * wi;
    }
    const last = parity[sum % 11];
    if (last.toLowerCase() !== value[17].toLowerCase()) {
      callback('身份证号码校验位错误');
      return;
    }
  }

  callback();
};

// 为了方便在表单中使用，可以创建预设的验证规则
export const validationRules = {
  mobile: [
    { required: true, message: '请输入手机号码' },
    { validator: validateMobile }
  ],
  idCard: (isIdCard = true) => [
    { required: true, message: '请输入证件号码' },
    { validator: (value: string, callback: (error?: string) => void) => 
      validateIdCard(value, callback, !isIdCard) 
    }
  ]
};