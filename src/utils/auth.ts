import type { TMenuItem } from '@/api/user';
import type { UserState } from '@/store/modules/user/types';

/*
 * @Description: 持久化当前用户的登陆数据
 * @Author: yangrongxin
 * @Date: 2024-09-20 15:57:10
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-07-18 10:16:46
 */
const TOKEN_KEY = 'token';
const USERINFO_KEY = 'user_info';
const MENU_KEY = 'menus';

const isLogin = () => {
  return (
    !!localStorage.getItem(TOKEN_KEY) || !!localStorage.getItem(USERINFO_KEY)
  );
};

const getToken = () => {
  return localStorage.getItem(TOKEN_KEY);
};

export const getUserInfo = () => {
  const userInfoStr = localStorage.getItem(USERINFO_KEY);
  let userInfo: UserState;
  if (
    typeof userInfoStr === 'string' &&
    userInfoStr !== '' &&
    userInfoStr !== 'null' &&
    userInfoStr !== 'undefined'
  ) {
    userInfo = JSON.parse(userInfoStr);
  } else {
    userInfo = {
      accountInfo: {
        name: undefined,
        avatar: undefined,
        organId: undefined,
        organType: undefined,
        teamId: undefined,
        teamRoleName: undefined,
        phone: undefined,
        userId: undefined,
      },
      token: undefined,
      roleInfo: {
        roleId: '',
        roleName: '',
        // roleCode: '',
        roleType: '',
      },
      authCodeInfo: {
        needSlider: false,
        authKey: '',
        exprieTime: 0,
        canNext: false,
      },
    };
  }
  return userInfo;
};

export const getMenus = () => {
  return localStorage.getItem(MENU_KEY);
};

const setToken = (token: string) => {
  localStorage.setItem(TOKEN_KEY, token);
};

export const setUserInfo = (userInfo: UserState) => {
  localStorage.setItem(USERINFO_KEY, JSON.stringify(userInfo));
};

export const setMenus = (menus: TMenuItem[]) => {
  localStorage.setItem(MENU_KEY, JSON.stringify(menus));
};

const clearToken = () => {
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(USERINFO_KEY);
  localStorage.removeItem(MENU_KEY);
};

export { isLogin, getToken, setToken, clearToken };
