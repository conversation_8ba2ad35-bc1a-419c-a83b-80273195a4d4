/*
 * @Description: insert description
 * @Author: yang<PERSON><PERSON>
 * @Date: 2024-10-17 17:53:49
 * @LastEditors: yangrongxin
 * @LastEditTime: 2024-10-18 10:19:05
 */
/**
 * Theme import
 * 样式按需引入
 * https://github.com/arco-design/arco-plugins/blob/main/packages/plugin-vite-vue/README.md
 * https://arco.design/vue/docs/start
 */
import { vitePluginForArco } from "@arco-plugins/vite-vue";

export default function configArcoStyleImportPlugin() {
  const arcoResolverPlugin = vitePluginForArco({
    theme: "@arco-themes/vue-bdyl",
  });
  return arcoResolverPlugin;
}
