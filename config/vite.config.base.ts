/*
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-09-20 15:57:10
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-01-07 14:33:55
 */
import { resolve } from 'path';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import svgLoader from 'vite-svg-loader';
// import configArcoStyleImportPlugin from './plugin/arcoStyleImport';
import { vitePluginForArco } from "@arco-plugins/vite-vue";
import postcssNested from 'postcss-nested';

export default defineConfig({
  plugins: [
    vue(),
    vueJsx(),
    svgLoader({ svgoConfig: {} }),
    // configArcoStyleImportPlugin(),
    vitePluginForArco({
      theme: "@arco-themes/vue-bdyl",
    })
  ],
  resolve: {
    alias: [
      {
        find: '@',
        replacement: resolve(__dirname, '../src'),
      },
      {
        find: 'assets',
        replacement: resolve(__dirname, '../src/assets'),
      },
      {
        find: 'vue-i18n',
        replacement: 'vue-i18n/dist/vue-i18n.cjs.js', // Resolve the i18n warning issue
      },
      {
        find: 'vue',
        replacement: 'vue/dist/vue.esm-bundler.js', // compile template
      },
    ],
    extensions: ['.ts', '.js'],
  },
  define: {
    'process.env': {},
  },
  css: {
    postcss: {
      plugins: [
        postcssNested()
      ]
    },
    preprocessorOptions: {
      scss: {
        api: 'modern',
        renderSync: {
          silenceDeprecations: ['legacy-js-api'], // 添加此行以配置 renderSync
        },
      },
      less: {
        modifyVars: {
          hack: `true; @import (reference) "${resolve(
            'src/assets/style/breakpoint.less'
          )}";`,
        },
        javascriptEnabled: true,
      },
    },
  },
});
