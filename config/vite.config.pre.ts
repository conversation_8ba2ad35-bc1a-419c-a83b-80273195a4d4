/*
 * @Description: insert description
 * @Author: yangrongxin
 * @Date: 2024-09-20 15:57:10
 * @LastEditors: yangrongxin
 * @LastEditTime: 2025-06-06 14:40:57
 */
import { mergeConfig } from 'vite';
import baseConfig from './vite.config.base';
import configCompressPlugin from './plugin/compress';
import configVisualizerPlugin from './plugin/visualizer';
import configArcoResolverPlugin from './plugin/arcoResolver';
// import configImageminPlugin from './plugin/imagemin';

export default mergeConfig(
  {
    // 代码发布的路径调整 -- 开发环境更新的时候 需要这个地址
    // base: '/bd-management',
    base: './',
    // mode: 'production',
    mode: 'development',
    // plugins: [
    //   configCompressPlugin('gzip'),
    //   configVisualizerPlugin(),
    //   configArcoResolverPlugin(),
    //   // configImageminPlugin(),
    // ],
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            arco: ['@arco-design/web-vue'],
            chart: ['echarts', 'vue-echarts'],
            vue: ['vue', 'vue-router', 'pinia', '@vueuse/core', 'vue-i18n'],
          },
        },
      },
      chunkSizeWarningLimit: 2000,
    },
    server: {
      // 监听所有可用的网络接口
      host: true,
      proxy: {
        // php后端
        '/bd-manage-api': {
          target: 'https://healthhubpre.pkucare.com',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        },
        // 本地的 权益页面的Java接口的代理
        '/bd-manage': {
          target: 'https://healthhubpre.pkucare.com/bd-manage',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/bd-manage/, '')
        },
        // 客户端
        '/bd-client': {
          target: 'https://healthguardpre.pkucare.com',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/bd-client/, '')
        },
        // 文书
        '/bd-form-engine': {
          target: 'https://healthhub.pkucare.com:4456/bd-form-engine',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/bd-form-engine/, '')
        }
      }
    }
  },
  baseConfig
)