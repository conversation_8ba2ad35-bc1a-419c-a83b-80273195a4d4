###
 # @Description: 测试环境使用的配置信息
 # @Author: yang<PERSON><PERSON>
 # @Date: 2024-10-25 11:33:09
 # @LastEditors: yangrongxin
 # @LastEditTime: 2025-07-22 16:42:53
### 
VITE_API_BASE_URL= 'http://**************:9080/bd-manage-api-dev'
VITE_FORM_BASE_URL = 'http://***************/bd-form-engine'
# VITE_FORM_BASE_URL = 'http://************:5174/bd-form-engine'
# VITE_FORM_BASE_URL = 'http://localhost:5174/bd-form-engine'
# 表单编辑器对应的地址
# VITE_FORM_DESIGN_BASE_URL = 'http://localhost:8808'
VITE_FORM_DESIGN_BASE_URL = 'http://***************/bd-form-engine-editor'

VITE_CLIENT_BASE_URL = 'http://***************:8261'

# 测试环境的Java地址
VITE_API_BASE__JAVA_URL =  http://***************:8262

# 常欢本地地址
# VITE_API_BASE__JAVA_URL =  http://************:8262
# 曾伟本地地址
# VITE_API_BASE__JAVA_URL =  http://***********:8262
# 杰哥本地地址
# VITE_API_BASE__JAVA_URL =  http://************:8262
# 杨超本地地址
# VITE_API_BASE__JAVA_URL =  http://************:8262
# 刘杏杏本地地址
# VITE_API_BASE__JAVA_URL =  http://************:8262
# VITE_API_BASE__JAVA_URL =  http://***********:8262
# 刘益本地地址
# VITE_API_BASE__JAVA_URL =  http://***********:8262
# VITE_API_BASE__JAVA_URL =  http://localhost:8262
# VITE_API_BASE__JAVA_URL =  http://***********:8262
# VITE_API_BASE__JAVA_URL =  http://localhost:8262
# VITE_API_BASE__JAVA_URL =  http://************:8262
VITE_FORM_ENV = '1'