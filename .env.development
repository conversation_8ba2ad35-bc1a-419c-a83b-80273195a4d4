###
 # @Description: 开发环境使用的配置信息
 # @Author: yang<PERSON><PERSON>
 # @Date: 2024-09-20 15:57:10
 # @LastEditors: yangrongxin
 # @LastEditTime: 2025-07-24 15:02:09
 # @LastEditTime: 2025-01-07 14:47:05
###

### 本地使用的 未被CI/CD修改配置的请求接口
VITE_API_BASE_URL= 'http://**************:9080/bd-manage-api-dev'
# VITE_API_BASE_URL= 'http://localhost:8081'
# VITE_FORM_BASE_URL = 'http://***************/bd-form-engine'
# VITE_FORM_BASE_URL = 'http://localhost:8808'
# VITE_FORM_BASE_URL = 'http://localhost:8808'
# VITE_CLIENT_BASE_URL = 'http://***************:8261/bd-client'
VITE_CLIENT_BASE_URL = 'http://localhost:8261/bd-client'
# VITE_CLIENT_BASE_URL = 'http://localhost:8261/bd-client'
# VITE_CLIENT_BASE_URL = 'http://************:8261/bd-client'
# VITE_CLIENT_BASE_URL = 'http://***************:8261'

# VITE_API_BASE_URL= '/bd-manage/bd-manage-api-dev'
# VITE_API_BASE_URL= 'http://localhost:8080/bd-manage-api-dev'
# VITE_FORM_BASE_URL = 'http://***************/bd-form-engine'
VITE_FORM_BASE_URL = 'http://localhost:5174/bd-form-engine'
# VITE_FORM_BASE_URL = 'http://***************/bd-form-engine'
VITE_FORM_DESIGN_BASE_URL = 'http://localhost:8808'
# VITE_CLIENT_BASE_URL = '/bd-manage/bd-client'
# 用于 api-swagger 使用的生成文档的api地址
# VITE_API_BASE__JAVA_URL =  http://**************:9080

# 使用本地的代码 用于本地签约 进行调试
VITE_API_BASE__JAVA_URL =  http://localhost:8262
# 杏杏的本地接口
# VITE_API_BASE__JAVA_URL =  http://***********:8262
# VITE_API_BASE__JAVA_URL =  http://************:8262
# VITE_API_BASE__JAVA_URL =  http://**************:9080

VITE_FORM_ENV = '1'