apiVersion: 'apps/v1'
kind: 'Deployment'
# 标记此Deployment的元数据信息
metadata:
  # 设置label标签
  labels:
    app: 'bd-management'
    project: 'hiis'
    # 产品编号
    productCode: 'Portal'
  # 设置Deployment的名称
  name: 'bd-management'
  # 设置Deployment所属的命名空间。开发环境：app-dev，测试环境：app-test
  #namespace: "app-test"
spec:
  # 指定应该保持运行的Pod副本数
  replicas: 1
  # 匹配由这个Deployment管理的Pod的标签选择器
  selector:
    matchLabels:
      app: 'bd-management'
  # 定义了Pod的元数据和规范
  template:
    metadata:
      labels:
        app: 'bd-management'
    spec:
      serviceAccountName: 'app-deployer'
      # 用于拉取私有镜像的密钥名称，这里是docker-secret
      imagePullSecrets:
        - name: docker-secret
      # 容器设置
      containers:
        - image: __IMAGE__
          # 拉取策略，这里是Always，意味着每次都会尝试从镜像仓库拉取最新的镜像
          imagePullPolicy: 'Always'
          name: 'operations'
          ports:
            - containerPort: 80
              name: 'http'
              protocol: 'TCP'
---
apiVersion: v1
kind: Service
metadata:
  # 设置service的label标签
  labels:
    app: bd-management
  # 设置service所属的命名空间。开发环境：app-dev，测试环境：app-test
  name: bd-management
  # 设置service所属的命名空间
  #namespace: app-test
spec:
  # Service暴露的端口，协议为TCP
  ports:
    - port: 80
      protocol: TCP
      # 对应pod 的端口号
      targetPort: 80
  # 选择标签包含app: unified-portal的Pod。
  selector:
    app: bd-management
  # Service类型，这里是ClusterIP，这意味着Service将在集群内部可用，并且会自动分配一个内部IP地址。
  type: ClusterIP